#!/usr/bin/env python3
"""
Personal Manager System - Main Entry Point
"""

import sys
import os
import logging
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

from core.config import ConfigManager
from core.logger import setup_logging, get_logger
from core.database import init_db_manager, close_all_sessions
from core.process_manager import ensure_single_instance, get_process_manager
from core.process_monitor import ProcessMonitor
from gui.main_window import MainWindow

# Import all models to ensure they are registered with SQLAlchemy
from models import *


def setup_application():
    """Setup the application environment"""
    # Initialize configuration
    config = ConfigManager()

    # Setup logging
    log_level = config.get('logging.level', 'INFO')
    log_file = config.get('logging.file')
    if log_file:
        log_file = Path(log_file)
        log_file.parent.mkdir(parents=True, exist_ok=True)

    setup_logging(
        level=log_level,
        log_file=str(log_file) if log_file else None,
        max_size=config.get('logging.max_size', 10485760),
        backup_count=config.get('logging.backup_count', 5)
    )

    logger = get_logger(__name__)
    logger.info("Personal Manager System starting...")

    # Initialize database
    try:
        db_manager = init_db_manager(config=config)
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

    logger.info("Application setup completed")
    return config, db_manager


def exception_handler(exc_type, exc_value, exc_traceback):
    """Global exception handler"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    logger = get_logger(__name__)
    logger.critical("Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback))

    # Show error dialog
    try:
        error_msg = f"发生未处理的错误:\n\n{exc_type.__name__}: {str(exc_value)}\n\n请查看日志文件获取详细信息。"
        QMessageBox.critical(None, "严重错误", error_msg)
    except:
        pass  # If we can't show the dialog, just continue


def main():
    """Main application entry point"""
    try:
        # Set global exception handler
        sys.excepthook = exception_handler

        # Set environment variables for better Qt experience
        os.environ.setdefault('QT_AUTO_SCREEN_SCALE_FACTOR', '1')
        os.environ.setdefault('QT_ENABLE_HIGHDPI_SCALING', '1')

        # Check for single instance
        if not ensure_single_instance():
            print("造神计划已在运行中，请检查系统托盘或任务管理器。")
            return

        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("造神计划")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("造神计划开发团队")
        app.setApplicationDisplayName("造神计划 - 个人管理系统")

        # Get process manager and monitor
        process_manager = get_process_manager()
        process_monitor = ProcessMonitor()

        # Setup application
        config, db_manager = setup_application()
        logger = get_logger(__name__)

        # Set application style
        style = config.get('gui.style', 'Fusion')
        available_styles = QApplication.instance().style().objectName()
        if style in available_styles:
            app.setStyle(style)

        # Set application icon
        icon_path = Path(__file__).parent / "resources" / "icons" / "app.ico"
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))

        # Create and show main window
        main_window = MainWindow(config)

        # Connect process manager and monitor signals
        def cleanup():
            logger.info("正在清理应用程序资源...")

            # 停止进程监控
            process_monitor.stop_monitoring()

            # 强制清理所有相关进程
            cleaned_count = process_monitor.force_cleanup_all()
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个进程")

            try:
                close_all_sessions()
                logger.info("数据库连接已关闭")
            except Exception as e:
                logger.error(f"关闭数据库时出错: {e}")
            config.save()
            logger.info("配置已保存")

        main_window.closing.connect(cleanup)
        process_manager.shutdown_requested.connect(main_window.close)

        # 启动进程监控
        process_monitor.start_monitoring()

        main_window.show()

        logger.info("造神计划启动成功")

        # Start event loop
        exit_code = app.exec()
        logger.info(f"造神计划退出，退出代码: {exit_code}")
        sys.exit(exit_code)

    except Exception as e:
        logger = get_logger(__name__)
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
