"""
专门测试时间显示修复的脚本
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import QTimer
from datetime import datetime

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件
from modules.study_tracker.study_floating_widget import StudyFloatingWidget


class TimeDisplayTestWindow(QMainWindow):
    """时间显示测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("时间显示修复测试")
        self.setGeometry(100, 100, 500, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("测试时间显示修复")
        layout.addWidget(self.status_label)
        
        # 信息标签
        info_label = QLabel("""
时间显示问题修复测试：

修复内容：
1. 修复了UTC时间和本地时间不匹配的问题
2. 改进了get_current_session()方法，避免会话失效
3. 增强了get_current_duration_seconds()的错误处理

测试步骤：
1. 点击"创建悬浮小组件"
2. 点击悬浮小组件的"开始"按钮
3. 观察时间是否从00:00开始并每秒递增
4. 验证时间格式是否为MM:SS或HH:MM:SS
        """)
        layout.addWidget(info_label)
        
        # 控制按钮
        create_btn = QPushButton("创建悬浮小组件")
        create_btn.clicked.connect(self.create_floating_widget)
        layout.addWidget(create_btn)
        
        start_btn = QPushButton("开始学习")
        start_btn.clicked.connect(self.start_study)
        layout.addWidget(start_btn)
        
        stop_btn = QPushButton("停止学习")
        stop_btn.clicked.connect(self.stop_study)
        layout.addWidget(stop_btn)
        
        # 调试信息显示
        self.debug_label = QLabel("调试信息:")
        layout.addWidget(self.debug_label)
        
        self.time_debug_label = QLabel("时间调试: 等待开始...")
        layout.addWidget(self.time_debug_label)
        
        # 悬浮小组件实例
        self.floating_widget = None
        
        # 调试定时器
        self.debug_timer = QTimer()
        self.debug_timer.timeout.connect(self.update_debug_info)
        self.debug_timer.start(1000)  # 每秒更新调试信息
    
    def create_floating_widget(self):
        """创建悬浮小组件"""
        try:
            if self.floating_widget is None:
                self.status_label.setText("正在创建悬浮小组件...")
                self.floating_widget = StudyFloatingWidget()
                
                # 连接信号
                self.floating_widget.start_study.connect(self.on_start_signal)
                self.floating_widget.stop_study.connect(self.on_stop_signal)
            
            self.floating_widget.show()
            self.floating_widget.raise_()
            self.floating_widget.activateWindow()
            self.status_label.setText("✅ 悬浮小组件已创建")
            print("✅ 悬浮小组件已创建")
            
        except Exception as e:
            self.status_label.setText(f"创建失败: {str(e)}")
            print(f"Error creating floating widget: {e}")
            import traceback
            traceback.print_exc()
    
    def start_study(self):
        """开始学习"""
        if self.floating_widget:
            print("手动开始学习...")
            self.floating_widget.start_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def stop_study(self):
        """停止学习"""
        if self.floating_widget:
            print("手动停止学习...")
            self.floating_widget.stop_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def update_debug_info(self):
        """更新调试信息"""
        if self.floating_widget and hasattr(self.floating_widget, 'study_service'):
            try:
                # 获取当前会话
                current_session = self.floating_widget.study_service.get_current_session()
                
                if current_session:
                    # 获取会话开始时间
                    start_time = current_session.start_time
                    current_time = datetime.now()
                    
                    # 计算时长（秒）
                    duration_seconds = self.floating_widget.study_service.get_current_duration_seconds()
                    
                    # 格式化时间显示
                    hours = duration_seconds // 3600
                    minutes = (duration_seconds % 3600) // 60
                    seconds = duration_seconds % 60
                    
                    if hours > 0:
                        time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                    else:
                        time_str = f"{minutes:02d}:{seconds:02d}"
                    
                    # 更新调试信息
                    debug_info = f"""
会话状态: 活动中
开始时间: {start_time.strftime('%H:%M:%S') if start_time else '未知'}
当前时间: {current_time.strftime('%H:%M:%S')}
总秒数: {duration_seconds}
格式化时间: {time_str}
                    """.strip()
                    
                    self.debug_label.setText(debug_info)
                    self.time_debug_label.setText(f"时间显示: {time_str} ({duration_seconds}秒)")
                    
                    # 检查时间是否在增长
                    if hasattr(self, 'last_duration'):
                        if duration_seconds > self.last_duration:
                            self.time_debug_label.setStyleSheet("color: green;")
                        elif duration_seconds == self.last_duration:
                            self.time_debug_label.setStyleSheet("color: orange;")
                        else:
                            self.time_debug_label.setStyleSheet("color: red;")
                    
                    self.last_duration = duration_seconds
                    
                else:
                    self.debug_label.setText("会话状态: 无活动会话")
                    self.time_debug_label.setText("时间显示: 00:00 (0秒)")
                    self.time_debug_label.setStyleSheet("color: gray;")
                    
            except Exception as e:
                self.debug_label.setText(f"调试信息获取失败: {e}")
                self.time_debug_label.setText("时间显示: 错误")
                self.time_debug_label.setStyleSheet("color: red;")
                print(f"Debug error: {e}")
    
    def on_start_signal(self):
        """开始学习信号"""
        self.status_label.setText("✅ 学习已开始，观察时间变化")
        print("✅ 学习已开始，开始监控时间显示")
    
    def on_stop_signal(self):
        """停止学习信号"""
        self.status_label.setText("⏹️ 学习已停止")
        print("⏹️ 学习已停止")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.floating_widget:
            self.floating_widget.close()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建测试窗口
        window = TimeDisplayTestWindow()
        window.show()
        
        print("时间显示修复测试程序已启动")
        print("请按照界面提示进行测试")
        print("重点观察时间是否每秒递增")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
