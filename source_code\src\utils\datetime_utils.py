"""
日期时间工具类

提供日期时间处理的常用功能，包括：
- 日期时间格式化
- 时间差计算
- 时区处理
- 日期解析
- 时间范围判断
"""

from datetime import datetime, timedelta, timezone
from typing import Optional, Union, List
import calendar
import time


class DateTimeUtils:
    """日期时间工具类"""
    
    # 常用日期时间格式
    FORMAT_DATETIME = "%Y-%m-%d %H:%M:%S"
    FORMAT_DATE = "%Y-%m-%d"
    FORMAT_TIME = "%H:%M:%S"
    FORMAT_DATETIME_COMPACT = "%Y%m%d_%H%M%S"
    FORMAT_DATETIME_ISO = "%Y-%m-%dT%H:%M:%S"
    FORMAT_DATETIME_READABLE = "%Y年%m月%d日 %H:%M:%S"
    FORMAT_DATE_READABLE = "%Y年%m月%d日"
    
    @staticmethod
    def now() -> datetime:
        """
        获取当前时间
        
        Returns:
            当前时间
        """
        return datetime.now()
    
    @staticmethod
    def utc_now() -> datetime:
        """
        获取当前UTC时间
        
        Returns:
            当前UTC时间
        """
        return datetime.utcnow()
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = FORMAT_DATETIME) -> str:
        """
        格式化日期时间
        
        Args:
            dt: 日期时间对象
            format_str: 格式字符串
            
        Returns:
            格式化后的日期时间字符串
        """
        try:
            return dt.strftime(format_str)
        except Exception:
            return str(dt)
    
    @staticmethod
    def parse_datetime(date_str: str, format_str: str = FORMAT_DATETIME) -> Optional[datetime]:
        """
        解析日期时间字符串
        
        Args:
            date_str: 日期时间字符串
            format_str: 格式字符串
            
        Returns:
            解析后的日期时间对象，解析失败返回None
        """
        try:
            return datetime.strptime(date_str, format_str)
        except Exception:
            return None
    
    @staticmethod
    def parse_datetime_flexible(date_str: str) -> Optional[datetime]:
        """
        灵活解析日期时间字符串（尝试多种格式）
        
        Args:
            date_str: 日期时间字符串
            
        Returns:
            解析后的日期时间对象，解析失败返回None
        """
        formats = [
            DateTimeUtils.FORMAT_DATETIME,
            DateTimeUtils.FORMAT_DATETIME_ISO,
            DateTimeUtils.FORMAT_DATE,
            DateTimeUtils.FORMAT_DATETIME_COMPACT,
            "%Y/%m/%d %H:%M:%S",
            "%Y/%m/%d",
            "%m/%d/%Y %H:%M:%S",
            "%m/%d/%Y",
            "%d/%m/%Y %H:%M:%S",
            "%d/%m/%Y",
            "%Y-%m-%d %H:%M",
            "%Y/%m/%d %H:%M"
        ]
        
        for fmt in formats:
            result = DateTimeUtils.parse_datetime(date_str, fmt)
            if result:
                return result
        
        return None
    
    @staticmethod
    def time_diff(dt1: datetime, dt2: datetime) -> timedelta:
        """
        计算两个时间的差值
        
        Args:
            dt1: 第一个时间
            dt2: 第二个时间
            
        Returns:
            时间差
        """
        return dt1 - dt2
    
    @staticmethod
    def format_time_diff(td: timedelta) -> str:
        """
        格式化时间差为人类可读格式
        
        Args:
            td: 时间差
            
        Returns:
            格式化后的时间差字符串
        """
        total_seconds = int(td.total_seconds())
        
        if total_seconds < 0:
            total_seconds = abs(total_seconds)
            prefix = "-"
        else:
            prefix = ""
        
        days = total_seconds // 86400
        hours = (total_seconds % 86400) // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        
        parts = []
        if days > 0:
            parts.append(f"{days}天")
        if hours > 0:
            parts.append(f"{hours}小时")
        if minutes > 0:
            parts.append(f"{minutes}分钟")
        if seconds > 0 or not parts:
            parts.append(f"{seconds}秒")
        
        return prefix + "".join(parts)
    
    @staticmethod
    def time_ago(dt: datetime) -> str:
        """
        计算相对于现在的时间描述
        
        Args:
            dt: 目标时间
            
        Returns:
            相对时间描述
        """
        now = datetime.now()
        diff = now - dt
        
        if diff.total_seconds() < 0:
            return "未来时间"
        
        seconds = int(diff.total_seconds())
        
        if seconds < 60:
            return "刚刚"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"{minutes}分钟前"
        elif seconds < 86400:
            hours = seconds // 3600
            return f"{hours}小时前"
        elif seconds < 2592000:  # 30天
            days = seconds // 86400
            return f"{days}天前"
        elif seconds < 31536000:  # 365天
            months = seconds // 2592000
            return f"{months}个月前"
        else:
            years = seconds // 31536000
            return f"{years}年前"
    
    @staticmethod
    def is_same_day(dt1: datetime, dt2: datetime) -> bool:
        """
        判断两个时间是否在同一天
        
        Args:
            dt1: 第一个时间
            dt2: 第二个时间
            
        Returns:
            是否在同一天
        """
        return dt1.date() == dt2.date()
    
    @staticmethod
    def is_today(dt: datetime) -> bool:
        """
        判断时间是否为今天
        
        Args:
            dt: 目标时间
            
        Returns:
            是否为今天
        """
        return DateTimeUtils.is_same_day(dt, datetime.now())
    
    @staticmethod
    def is_yesterday(dt: datetime) -> bool:
        """
        判断时间是否为昨天
        
        Args:
            dt: 目标时间
            
        Returns:
            是否为昨天
        """
        yesterday = datetime.now() - timedelta(days=1)
        return DateTimeUtils.is_same_day(dt, yesterday)
    
    @staticmethod
    def get_week_range(dt: datetime) -> tuple[datetime, datetime]:
        """
        获取指定日期所在周的开始和结束时间
        
        Args:
            dt: 目标日期
            
        Returns:
            (周开始时间, 周结束时间)
        """
        # 获取周一作为一周的开始
        days_since_monday = dt.weekday()
        week_start = dt - timedelta(days=days_since_monday)
        week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
        
        week_end = week_start + timedelta(days=6, hours=23, minutes=59, seconds=59)
        
        return week_start, week_end
    
    @staticmethod
    def get_month_range(dt: datetime) -> tuple[datetime, datetime]:
        """
        获取指定日期所在月的开始和结束时间
        
        Args:
            dt: 目标日期
            
        Returns:
            (月开始时间, 月结束时间)
        """
        month_start = dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # 获取月末日期
        last_day = calendar.monthrange(dt.year, dt.month)[1]
        month_end = dt.replace(day=last_day, hour=23, minute=59, second=59, microsecond=999999)
        
        return month_start, month_end
    
    @staticmethod
    def add_business_days(dt: datetime, days: int) -> datetime:
        """
        添加工作日（跳过周末）
        
        Args:
            dt: 起始日期
            days: 要添加的工作日数
            
        Returns:
            添加工作日后的日期
        """
        current_date = dt
        remaining_days = days
        
        while remaining_days > 0:
            current_date += timedelta(days=1)
            # 0-6 代表周一到周日，0-4是工作日
            if current_date.weekday() < 5:
                remaining_days -= 1
        
        return current_date
    
    @staticmethod
    def is_business_day(dt: datetime) -> bool:
        """
        判断是否为工作日
        
        Args:
            dt: 目标日期
            
        Returns:
            是否为工作日
        """
        return dt.weekday() < 5
    
    @staticmethod
    def timestamp_to_datetime(timestamp: Union[int, float]) -> datetime:
        """
        时间戳转换为日期时间
        
        Args:
            timestamp: 时间戳
            
        Returns:
            日期时间对象
        """
        return datetime.fromtimestamp(timestamp)
    
    @staticmethod
    def datetime_to_timestamp(dt: datetime) -> float:
        """
        日期时间转换为时间戳
        
        Args:
            dt: 日期时间对象
            
        Returns:
            时间戳
        """
        return dt.timestamp()
    
    @staticmethod
    def get_age(birth_date: datetime) -> int:
        """
        根据出生日期计算年龄
        
        Args:
            birth_date: 出生日期
            
        Returns:
            年龄
        """
        today = datetime.now()
        age = today.year - birth_date.year
        
        # 检查是否还没到生日
        if today.month < birth_date.month or (today.month == birth_date.month and today.day < birth_date.day):
            age -= 1
        
        return age
    
    @staticmethod
    def sleep_until(target_time: datetime):
        """
        睡眠直到指定时间
        
        Args:
            target_time: 目标时间
        """
        now = datetime.now()
        if target_time > now:
            sleep_seconds = (target_time - now).total_seconds()
            time.sleep(sleep_seconds)
