#!/usr/bin/env python3
"""
Test script for the new task manager interface

This script tests the new task management interface components.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt

# Test imports
try:
    from modules.task_manager import TaskManagerWidget, DateWeekDisplay, EditableTaskTable
    print("✓ Successfully imported new task manager components")
except ImportError as e:
    print(f"✗ Failed to import components: {e}")
    sys.exit(1)


class TestWindow(QMainWindow):
    """Test window for new task manager"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("新任务管理界面测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Add task manager widget
        try:
            self.task_manager = TaskManagerWidget()
            layout.addWidget(self.task_manager)
            print("✓ Task manager widget created successfully")
        except Exception as e:
            print(f"✗ Failed to create task manager widget: {e}")
            return


def test_components():
    """Test individual components"""
    print("Testing individual components...")
    
    # Test DateWeekDisplay
    try:
        date_display = DateWeekDisplay()
        week_info = date_display.get_current_week_info()
        print(f"✓ DateWeekDisplay working - Week {week_info['week_number']}, {week_info['date']}")
    except Exception as e:
        print(f"✗ DateWeekDisplay failed: {e}")
    
    # Test EditableTaskTable
    try:
        task_table = EditableTaskTable()
        print("✓ EditableTaskTable created successfully")
    except Exception as e:
        print(f"✗ EditableTaskTable failed: {e}")


def main():
    """Main test function"""
    print("=" * 50)
    print("新任务管理界面测试")
    print("=" * 50)
    
    # Test component imports and creation
    test_components()
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Create test window
    try:
        window = TestWindow()
        window.show()
        
        print("\n✓ Test window created successfully")
        print("请检查新的任务管理界面是否正常显示")
        print("- 周和日期显示是否正确")
        print("- 任务表格是否可编辑")
        print("- 界面布局是否合理")
        
        # Run application
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"✗ Failed to create test window: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
