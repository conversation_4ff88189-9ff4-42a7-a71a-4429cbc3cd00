#!/usr/bin/env python3
"""
测试任务分类一致性
验证桌面应用端和桌面小组件的任务分类逻辑是否一致
"""

import sys
import os
from datetime import datetime, date
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from core.database import DatabaseManager
from models.task_models import Task
from modules.task_manager.task_service import TaskService

def test_task_categorization():
    """测试任务分类逻辑一致性"""
    print("=" * 60)
    print("测试任务分类逻辑一致性")
    print("=" * 60)
    
    # 初始化数据库和服务
    from core.config import Config
    config = Config()
    db_manager = DatabaseManager(config=config)
    task_service = TaskService()
    
    # 获取今天的任务
    today = date.today()
    print(f"📅 查询日期: {today}")
    
    # 获取所有任务
    all_tasks = task_service.get_tasks(limit=1000)
    today_tasks = []
    
    for task in all_tasks:
        task_date = None
        if task.start_date:
            task_date = task.start_date.date()
        elif task.due_date:
            task_date = task.due_date.date()
        
        if task_date == today:
            today_tasks.append(task)
    
    print(f"📋 今日任务总数: {len(today_tasks)}")
    
    if not today_tasks:
        print("❌ 没有找到今日任务")
        return
    
    # 导入两个分类函数
    from modules.task_manager.weekly_task_table import WeeklyTaskTable
    from modules.task_manager.desktop_widget import DesktopTaskWidget
    
    # 创建实例来测试分类方法
    weekly_table = WeeklyTaskTable()
    desktop_widget = DesktopTaskWidget()
    
    print("\n" + "=" * 60)
    print("任务分类对比")
    print("=" * 60)
    
    categorization_differences = []
    
    for task in today_tasks:
        # 使用两种方法分类
        weekly_category = weekly_table.determine_task_category(task)
        desktop_category = desktop_widget.determine_task_category(task)
        
        print(f"\n📝 任务: {task.title}")
        print(f"   🏷️  标签: {task.tags}")
        print(f"   🖥️  桌面应用分类: {weekly_category}")
        print(f"   📱 桌面小组件分类: {desktop_category}")
        
        if weekly_category != desktop_category:
            categorization_differences.append({
                'task': task,
                'weekly_category': weekly_category,
                'desktop_category': desktop_category
            })
            print(f"   ⚠️  分类不一致!")
        else:
            print(f"   ✅ 分类一致")
    
    print("\n" + "=" * 60)
    print("分类一致性总结")
    print("=" * 60)
    
    if categorization_differences:
        print(f"❌ 发现 {len(categorization_differences)} 个分类不一致的任务:")
        for diff in categorization_differences:
            task = diff['task']
            print(f"   - {task.title} (标签: {task.tags})")
            print(f"     桌面应用: {diff['weekly_category']} vs 桌面小组件: {diff['desktop_category']}")
    else:
        print("✅ 所有任务的分类都一致!")
    
    # 按分类统计任务
    print("\n" + "=" * 60)
    print("按分类统计任务 (桌面应用端)")
    print("=" * 60)
    
    weekly_categories = {}
    for task in today_tasks:
        category = weekly_table.determine_task_category(task)
        if category not in weekly_categories:
            weekly_categories[category] = []
        weekly_categories[category].append(task)
    
    for category, tasks in weekly_categories.items():
        print(f"📂 {category}: {len(tasks)} 个任务")
        for task in tasks:
            print(f"   - {task.title} (标签: {task.tags})")
    
    print("\n" + "=" * 60)
    print("按分类统计任务 (桌面小组件)")
    print("=" * 60)
    
    desktop_categories = {}
    for task in today_tasks:
        category = desktop_widget.determine_task_category(task)
        if category not in desktop_categories:
            desktop_categories[category] = []
        desktop_categories[category].append(task)
    
    for category, tasks in desktop_categories.items():
        print(f"📂 {category}: {len(tasks)} 个任务")
        for task in tasks:
            print(f"   - {task.title} (标签: {task.tags})")

if __name__ == "__main__":
    test_task_categorization()
