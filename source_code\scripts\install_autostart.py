#!/usr/bin/env python3
"""
桌面小部件自动启动安装脚本

将桌面小部件配置为开机自启动
"""

import os
import sys
import winreg
from pathlib import Path
import shutil
import subprocess
from PyQt6.QtWidgets import QApplication, QMessageBox, QDialog, QVBoxLayout, QLabel, QPushButton, QCheckBox, QHBoxLayout


class AutoStartInstaller:
    """自动启动安装器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.app_name = "桌面任务小部件"
        self.exe_name = "DesktopTaskWidget"
        
    def install_registry_startup(self):
        """安装注册表自启动"""
        try:
            # 获取启动脚本路径
            startup_script = self.project_root / "start_desktop_widget.bat"
            if not startup_script.exists():
                raise FileNotFoundError("启动脚本不存在")
            
            # 打开注册表键
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Run",
                0,
                winreg.KEY_SET_VALUE
            )
            
            # 设置自启动项
            winreg.SetValueEx(
                key,
                self.app_name,
                0,
                winreg.REG_SZ,
                f'"{startup_script}"'
            )
            
            winreg.CloseKey(key)
            return True
            
        except Exception as e:
            print(f"注册表自启动安装失败: {e}")
            return False
    
    def uninstall_registry_startup(self):
        """卸载注册表自启动"""
        try:
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Run",
                0,
                winreg.KEY_SET_VALUE
            )
            
            winreg.DeleteValue(key, self.app_name)
            winreg.CloseKey(key)
            return True
            
        except FileNotFoundError:
            # 键不存在，认为卸载成功
            return True
        except Exception as e:
            print(f"注册表自启动卸载失败: {e}")
            return False
    
    def check_registry_startup(self):
        """检查注册表自启动状态"""
        try:
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Run",
                0,
                winreg.KEY_READ
            )
            
            value, _ = winreg.QueryValueEx(key, self.app_name)
            winreg.CloseKey(key)
            return True
            
        except FileNotFoundError:
            return False
        except Exception:
            return False
    
    def install_startup_folder(self):
        """安装到启动文件夹"""
        try:
            # 获取启动文件夹路径
            startup_folder = Path(os.environ['APPDATA']) / "Microsoft" / "Windows" / "Start Menu" / "Programs" / "Startup"
            
            # 创建快捷方式脚本
            shortcut_script = startup_folder / f"{self.app_name}.bat"
            startup_bat = self.project_root / "start_desktop_widget.bat"
            
            # 创建启动脚本
            with open(shortcut_script, 'w', encoding='utf-8') as f:
                f.write(f'@echo off\n')
                f.write(f'cd /d "{self.project_root}"\n')
                f.write(f'start "" "{startup_bat}"\n')
            
            return True
            
        except Exception as e:
            print(f"启动文件夹安装失败: {e}")
            return False
    
    def uninstall_startup_folder(self):
        """从启动文件夹卸载"""
        try:
            startup_folder = Path(os.environ['APPDATA']) / "Microsoft" / "Windows" / "Start Menu" / "Programs" / "Startup"
            shortcut_script = startup_folder / f"{self.app_name}.bat"
            
            if shortcut_script.exists():
                shortcut_script.unlink()
            
            return True
            
        except Exception as e:
            print(f"启动文件夹卸载失败: {e}")
            return False
    
    def create_task_scheduler(self):
        """创建任务计划程序"""
        try:
            task_name = self.app_name
            startup_script = self.project_root / "start_desktop_widget.bat"
            
            # 创建任务计划程序命令
            cmd = [
                'schtasks', '/create',
                '/tn', task_name,
                '/tr', f'"{startup_script}"',
                '/sc', 'onlogon',
                '/rl', 'limited',
                '/f'  # 强制覆盖已存在的任务
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            print(f"任务计划程序创建失败: {e}")
            return False
    
    def delete_task_scheduler(self):
        """删除任务计划程序"""
        try:
            task_name = self.app_name
            
            cmd = ['schtasks', '/delete', '/tn', task_name, '/f']
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            print(f"任务计划程序删除失败: {e}")
            return False


class AutoStartDialog(QDialog):
    """自动启动配置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.installer = AutoStartInstaller()
        self.setWindowTitle("自动启动配置")
        self.setModal(True)
        self.resize(400, 300)
        self.init_ui()
        self.update_status()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("桌面任务小部件 - 自动启动配置")
        title_label.setStyleSheet("font-size: 14pt; font-weight: bold; color: #2E4A6B;")
        layout.addWidget(title_label)
        
        # 说明
        desc_label = QLabel("配置桌面小部件在系统启动时自动运行，无需手动开启。")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # 状态显示
        self.status_label = QLabel()
        self.status_label.setStyleSheet("padding: 10px; background: #F0F0F0; border-radius: 5px;")
        layout.addWidget(self.status_label)
        
        # 选项
        options_layout = QVBoxLayout()
        
        self.registry_checkbox = QCheckBox("注册表自启动（推荐）")
        self.registry_checkbox.setChecked(True)
        options_layout.addWidget(self.registry_checkbox)
        
        self.startup_folder_checkbox = QCheckBox("启动文件夹")
        options_layout.addWidget(self.startup_folder_checkbox)
        
        self.task_scheduler_checkbox = QCheckBox("任务计划程序")
        options_layout.addWidget(self.task_scheduler_checkbox)
        
        layout.addLayout(options_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.install_btn = QPushButton("安装自启动")
        self.install_btn.clicked.connect(self.install_autostart)
        self.install_btn.setStyleSheet("""
            QPushButton {
                background: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #45A049;
            }
        """)
        button_layout.addWidget(self.install_btn)
        
        self.uninstall_btn = QPushButton("卸载自启动")
        self.uninstall_btn.clicked.connect(self.uninstall_autostart)
        self.uninstall_btn.setStyleSheet("""
            QPushButton {
                background: #F44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #DA190B;
            }
        """)
        button_layout.addWidget(self.uninstall_btn)
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background: #9E9E9E;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #757575;
            }
        """)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def update_status(self):
        """更新状态显示"""
        registry_status = "✓ 已启用" if self.installer.check_registry_startup() else "✗ 未启用"
        
        status_text = f"""
        当前自启动状态:
        • 注册表自启动: {registry_status}
        
        选择一种或多种自启动方式，然后点击"安装自启动"。
        """
        
        self.status_label.setText(status_text)
    
    def install_autostart(self):
        """安装自启动"""
        success_count = 0
        total_count = 0
        
        if self.registry_checkbox.isChecked():
            total_count += 1
            if self.installer.install_registry_startup():
                success_count += 1
        
        if self.startup_folder_checkbox.isChecked():
            total_count += 1
            if self.installer.install_startup_folder():
                success_count += 1
        
        if self.task_scheduler_checkbox.isChecked():
            total_count += 1
            if self.installer.create_task_scheduler():
                success_count += 1
        
        if total_count == 0:
            QMessageBox.warning(self, "警告", "请至少选择一种自启动方式")
            return
        
        if success_count == total_count:
            QMessageBox.information(self, "成功", "自启动安装成功！\n桌面小部件将在下次开机时自动启动。")
        else:
            QMessageBox.warning(self, "部分成功", f"自启动安装部分成功（{success_count}/{total_count}）")
        
        self.update_status()
    
    def uninstall_autostart(self):
        """卸载自启动"""
        success_count = 0
        
        if self.installer.uninstall_registry_startup():
            success_count += 1
        
        if self.installer.uninstall_startup_folder():
            success_count += 1
        
        if self.installer.delete_task_scheduler():
            success_count += 1
        
        QMessageBox.information(self, "完成", "自启动已卸载")
        self.update_status()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    dialog = AutoStartDialog()
    dialog.exec()
    
    sys.exit(0)


if __name__ == "__main__":
    main()
