"""
文件管理相关数据模型

包含文件收藏、文件标签、文件历史等模型
"""

from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, ForeignKey, Index
from sqlalchemy.orm import relationship, Session
from typing import List, Optional
from datetime import datetime

from .base import BaseModel


class FileBookmark(BaseModel):
    """文件收藏夹模型"""
    
    __tablename__ = 'file_bookmarks'
    
    name = Column(String(255), nullable=False)
    path = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    icon = Column(String(255), nullable=True)
    sort_order = Column(Integer, default=0)
    is_folder = Column(Boolean, default=False)
    
    # 索引
    __table_args__ = (
        Index('idx_file_bookmarks_path', 'path'),
        Index('idx_file_bookmarks_sort', 'sort_order'),
    )
    
    @classmethod
    def get_by_path(cls, session: Session, path: str) -> Optional['FileBookmark']:
        """根据路径获取收藏"""
        return session.query(cls).filter(
            cls.path == path,
            cls.is_deleted == False
        ).first()
    
    @classmethod
    def get_sorted_bookmarks(cls, session: Session) -> List['FileBookmark']:
        """获取排序后的收藏列表"""
        return session.query(cls).filter(
            cls.is_deleted == False
        ).order_by(cls.sort_order, cls.name).all()


class FileTag(BaseModel):
    """文件标签模型"""
    
    __tablename__ = 'file_tags'
    
    name = Column(String(100), nullable=False, unique=True)
    color = Column(String(7), nullable=True)  # 十六进制颜色值
    description = Column(Text, nullable=True)
    
    # 关联关系
    file_tag_relations = relationship("FileTagRelation", back_populates="tag")
    
    @classmethod
    def get_by_name(cls, session: Session, name: str) -> Optional['FileTag']:
        """根据名称获取标签"""
        return session.query(cls).filter(
            cls.name == name,
            cls.is_deleted == False
        ).first()
    
    @classmethod
    def get_or_create(cls, session: Session, name: str, color: str = None) -> 'FileTag':
        """获取或创建标签"""
        tag = cls.get_by_name(session, name)
        if not tag:
            tag = cls(name=name, color=color)
            session.add(tag)
            session.flush()
        return tag


class FileTagRelation(BaseModel):
    """文件标签关联模型"""
    
    __tablename__ = 'file_tag_relations'
    
    file_path = Column(Text, nullable=False)
    tag_id = Column(String(36), ForeignKey('file_tags.id'), nullable=False)
    
    # 关联关系
    tag = relationship("FileTag", back_populates="file_tag_relations")
    
    # 索引
    __table_args__ = (
        Index('idx_file_tag_relations_path', 'file_path'),
        Index('idx_file_tag_relations_tag', 'tag_id'),
        Index('idx_file_tag_relations_unique', 'file_path', 'tag_id', unique=True),
    )
    
    @classmethod
    def add_tag_to_file(cls, session: Session, file_path: str, tag_name: str) -> 'FileTagRelation':
        """为文件添加标签"""
        tag = FileTag.get_or_create(session, tag_name)
        
        # 检查是否已存在
        existing = session.query(cls).filter(
            cls.file_path == file_path,
            cls.tag_id == tag.id,
            cls.is_deleted == False
        ).first()
        
        if existing:
            return existing
        
        relation = cls(file_path=file_path, tag_id=tag.id)
        session.add(relation)
        session.flush()
        return relation
    
    @classmethod
    def remove_tag_from_file(cls, session: Session, file_path: str, tag_name: str) -> bool:
        """从文件移除标签"""
        tag = FileTag.get_by_name(session, tag_name)
        if not tag:
            return False
        
        relation = session.query(cls).filter(
            cls.file_path == file_path,
            cls.tag_id == tag.id,
            cls.is_deleted == False
        ).first()
        
        if relation:
            relation.soft_delete()
            session.add(relation)
            session.flush()
            return True
        
        return False
    
    @classmethod
    def get_file_tags(cls, session: Session, file_path: str) -> List[FileTag]:
        """获取文件的所有标签"""
        return session.query(FileTag).join(cls).filter(
            cls.file_path == file_path,
            cls.is_deleted == False,
            FileTag.is_deleted == False
        ).all()
    
    @classmethod
    def get_files_by_tag(cls, session: Session, tag_name: str) -> List[str]:
        """获取具有指定标签的所有文件"""
        tag = FileTag.get_by_name(session, tag_name)
        if not tag:
            return []
        
        relations = session.query(cls).filter(
            cls.tag_id == tag.id,
            cls.is_deleted == False
        ).all()
        
        return [relation.file_path for relation in relations]


class FileHistory(BaseModel):
    """文件访问历史模型"""
    
    __tablename__ = 'file_history'
    
    file_path = Column(Text, nullable=False)
    file_name = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=True)
    file_type = Column(String(50), nullable=True)
    action = Column(String(50), nullable=False)  # open, create, modify, delete
    access_count = Column(Integer, default=1)
    last_accessed = Column(DateTime, default=datetime.utcnow)
    
    # 索引
    __table_args__ = (
        Index('idx_file_history_path', 'file_path'),
        Index('idx_file_history_accessed', 'last_accessed'),
        Index('idx_file_history_action', 'action'),
    )
    
    @classmethod
    def record_access(cls, session: Session, file_path: str, action: str = 'open',
                     file_name: str = None, file_size: int = None, file_type: str = None) -> 'FileHistory':
        """记录文件访问"""
        import os
        
        if not file_name:
            file_name = os.path.basename(file_path)
        
        # 查找现有记录
        existing = session.query(cls).filter(
            cls.file_path == file_path,
            cls.action == action,
            cls.is_deleted == False
        ).first()
        
        if existing:
            existing.access_count += 1
            existing.last_accessed = datetime.utcnow()
            if file_size is not None:
                existing.file_size = file_size
            if file_type:
                existing.file_type = file_type
            session.add(existing)
            session.flush()
            return existing
        else:
            history = cls(
                file_path=file_path,
                file_name=file_name,
                file_size=file_size,
                file_type=file_type,
                action=action,
                last_accessed=datetime.utcnow()
            )
            session.add(history)
            session.flush()
            return history
    
    @classmethod
    def get_recent_files(cls, session: Session, limit: int = 20, action: str = None) -> List['FileHistory']:
        """获取最近访问的文件"""
        query = session.query(cls).filter(cls.is_deleted == False)
        
        if action:
            query = query.filter(cls.action == action)
        
        return query.order_by(cls.last_accessed.desc()).limit(limit).all()
    
    @classmethod
    def get_frequent_files(cls, session: Session, limit: int = 20, action: str = None) -> List['FileHistory']:
        """获取经常访问的文件"""
        query = session.query(cls).filter(cls.is_deleted == False)
        
        if action:
            query = query.filter(cls.action == action)
        
        return query.order_by(cls.access_count.desc()).limit(limit).all()
    
    @classmethod
    def cleanup_old_history(cls, session: Session, days: int = 90) -> int:
        """清理旧的历史记录"""
        from datetime import timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        old_records = session.query(cls).filter(
            cls.last_accessed < cutoff_date,
            cls.is_deleted == False
        ).all()
        
        count = len(old_records)
        for record in old_records:
            record.soft_delete()
            session.add(record)
        
        session.flush()
        return count


class FileSearch(BaseModel):
    """文件搜索历史模型"""
    
    __tablename__ = 'file_searches'
    
    query = Column(String(500), nullable=False)
    search_path = Column(Text, nullable=True)
    file_types = Column(String(200), nullable=True)  # 逗号分隔的文件类型
    results_count = Column(Integer, default=0)
    search_time = Column(Integer, nullable=True)  # 搜索耗时（毫秒）
    
    # 索引
    __table_args__ = (
        Index('idx_file_searches_query', 'query'),
        Index('idx_file_searches_created', 'created_at'),
    )
    
    @classmethod
    def record_search(cls, session: Session, query: str, search_path: str = None,
                     file_types: List[str] = None, results_count: int = 0,
                     search_time: int = None) -> 'FileSearch':
        """记录搜索历史"""
        file_types_str = ','.join(file_types) if file_types else None
        
        search_record = cls(
            query=query,
            search_path=search_path,
            file_types=file_types_str,
            results_count=results_count,
            search_time=search_time
        )
        session.add(search_record)
        session.flush()
        return search_record
    
    @classmethod
    def get_recent_searches(cls, session: Session, limit: int = 10) -> List['FileSearch']:
        """获取最近的搜索记录"""
        return session.query(cls).filter(
            cls.is_deleted == False
        ).order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_popular_searches(cls, session: Session, limit: int = 10) -> List[str]:
        """获取热门搜索词"""
        from sqlalchemy import func
        
        results = session.query(
            cls.query,
            func.count(cls.query).label('count')
        ).filter(
            cls.is_deleted == False
        ).group_by(cls.query).order_by(
            func.count(cls.query).desc()
        ).limit(limit).all()
        
        return [result.query for result in results]
