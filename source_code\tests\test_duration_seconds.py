"""
直接测试get_current_duration_seconds方法
"""

import sys
from pathlib import Path
import time

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config
from modules.study_tracker.study_service import StudyService


def test_duration_seconds():
    """测试时长秒数计算"""
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建学习服务
        study_service = StudyService()
        
        # 确保有科目
        subjects = study_service.get_subjects()
        if not subjects:
            print("创建默认科目...")
            subject = study_service.create_subject(
                name="测试科目",
                description="用于测试时间显示的科目",
                color="#3498db"
            )
            if subject:
                subject_id = subject.id
                print(f"创建科目成功，ID: {subject_id}")
            else:
                print("创建科目失败")
                return
        else:
            subject_id = subjects[0]['id']
            print(f"使用现有科目，ID: {subject_id}")
        
        # 开始学习会话
        print("开始学习会话...")
        session = study_service.start_session(
            subject_id=subject_id,
            title="时间测试会话",
            planned_duration=5
        )
        
        if session:
            print("✅ 学习会话创建成功")
            print("开始监控时间变化...")
            
            # 监控10秒钟
            for i in range(10):
                duration_seconds = study_service.get_current_duration_seconds()
                print(f"第{i+1}秒: {duration_seconds}秒")
                
                # 格式化时间显示
                hours = duration_seconds // 3600
                minutes = (duration_seconds % 3600) // 60
                seconds = duration_seconds % 60
                
                if hours > 0:
                    time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                else:
                    time_str = f"{minutes:02d}:{seconds:02d}"
                
                print(f"  格式化时间: {time_str}")
                
                time.sleep(1)
            
            # 停止会话
            print("停止学习会话...")
            success = study_service.complete_session()
            if success:
                print("✅ 学习会话已完成")
            else:
                print("❌ 停止会话失败")
                
        else:
            print("❌ 学习会话创建失败")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_duration_seconds()
