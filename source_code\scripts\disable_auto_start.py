"""
禁用桌面插件自启动功能
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONLEGACYWINDOWSFSENCODING'] = '0'
os.environ['PYTHONUTF8'] = '1'

from modules.task_manager.desktop_widget_controller import DesktopWidgetController

def disable_auto_start():
    """禁用自启动功能"""
    print("🔧 正在禁用桌面插件自启动功能...")
    
    try:
        # 创建控制器实例
        controller = DesktopWidgetController()
        
        # 检查当前自启动状态
        current_status = controller.is_auto_start_enabled()
        print(f"📋 当前自启动状态: {'已启用' if current_status else '已禁用'}")
        
        if current_status:
            # 禁用自启动
            success = controller.set_auto_start(False)
            
            if success:
                print("✅ 自启动已成功禁用！")
                
                # 再次检查状态确认
                new_status = controller.is_auto_start_enabled()
                print(f"🔍 确认状态: {'已启用' if new_status else '已禁用'}")
                
                if not new_status:
                    print("🎉 自启动功能已完全关闭！")
                    return True
                else:
                    print("⚠️ 自启动状态检查异常")
                    return False
            else:
                print("❌ 禁用自启动失败！")
                return False
        else:
            print("ℹ️ 自启动功能已经是禁用状态")
            return True
            
    except Exception as e:
        print(f"💥 禁用自启动过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        result = disable_auto_start()
        if result:
            print("\n🎯 操作完成！桌面插件将不会在系统启动时自动运行。")
        else:
            print("\n❌ 操作失败！请检查错误信息。")
    except Exception as e:
        print(f"\n💥 脚本执行失败: {e}")
