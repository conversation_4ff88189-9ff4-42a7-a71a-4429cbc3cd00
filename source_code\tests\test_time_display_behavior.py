"""
测试时间显示行为：
1. 打开小组件显示00:00:00
2. 点击开始从00:00:01开始动
3. 关闭再打开保持上次时间
4. 只有点击停止才清零
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件
from modules.study_tracker.study_floating_widget import StudyFloatingWidget


class TimeDisplayBehaviorTestWindow(QMainWindow):
    """时间显示行为测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("时间显示行为测试")
        self.setGeometry(100, 100, 700, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("测试时间显示行为")
        layout.addWidget(self.status_label)
        
        # 信息标签
        info_label = QLabel("""
🎯 测试目标：

1. 初始显示：打开小组件时显示 00:00:00
2. 开始计时：点击开始后从 00:00:01 开始递增
3. 状态保持：关闭再打开小组件时，上次的时间状态保持
4. 清零条件：只有点击"停止"按钮时才清零

🧪 测试步骤：

第一阶段：初始状态测试
1. 创建悬浮小组件 → 应显示 00:00:00
2. 点击"开始" → 应从 00:00:01 开始递增

第二阶段：状态保持测试  
3. 让时间运行到 00:00:05 左右
4. 关闭悬浮小组件
5. 重新打开悬浮小组件 → 应显示上次的时间（如 00:00:05）

第三阶段：清零测试
6. 点击"停止"按钮 → 时间应清零为 00:00:00
7. 再次关闭并打开 → 应显示 00:00:00
        """)
        layout.addWidget(info_label)
        
        # 控制按钮
        create_btn = QPushButton("1. 创建悬浮小组件")
        create_btn.clicked.connect(self.create_floating_widget)
        layout.addWidget(create_btn)
        
        start_btn = QPushButton("2. 开始学习")
        start_btn.clicked.connect(self.start_study)
        layout.addWidget(start_btn)
        
        close_btn = QPushButton("3. 关闭悬浮小组件")
        close_btn.clicked.connect(self.close_floating_widget)
        layout.addWidget(close_btn)
        
        reopen_btn = QPushButton("4. 重新打开悬浮小组件")
        reopen_btn.clicked.connect(self.reopen_floating_widget)
        layout.addWidget(reopen_btn)
        
        stop_btn = QPushButton("5. 停止学习（清零）")
        stop_btn.clicked.connect(self.stop_study)
        layout.addWidget(stop_btn)
        
        # 状态显示
        self.time_display_label = QLabel("当前时间: --:--:--")
        layout.addWidget(self.time_display_label)
        
        self.session_status_label = QLabel("会话状态: 无")
        layout.addWidget(self.session_status_label)
        
        self.widget_status_label = QLabel("小组件状态: 未创建")
        layout.addWidget(self.widget_status_label)
        
        # 测试结果
        self.test_result_label = QLabel("测试结果: 等待测试...")
        layout.addWidget(self.test_result_label)
        
        # 悬浮小组件实例
        self.floating_widget = None
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_monitor)
        self.monitor_timer.start(1000)  # 每秒更新
    
    def create_floating_widget(self):
        """创建悬浮小组件"""
        try:
            if self.floating_widget is None:
                self.status_label.setText("正在创建悬浮小组件...")
                self.floating_widget = StudyFloatingWidget()
                
                # 连接信号
                self.floating_widget.start_study.connect(self.on_start_signal)
                self.floating_widget.stop_study.connect(self.on_stop_signal)
            
            self.floating_widget.show()
            self.floating_widget.raise_()
            self.floating_widget.activateWindow()
            
            # 检查初始时间显示
            initial_time = self.floating_widget.time_label.text()
            self.status_label.setText(f"✅ 悬浮小组件已创建，初始时间: {initial_time}")
            self.widget_status_label.setText("小组件状态: 已打开")
            
            if initial_time == "00:00:00":
                self.test_result_label.setText("✅ 测试1通过: 初始显示00:00:00")
            else:
                self.test_result_label.setText(f"❌ 测试1失败: 初始显示{initial_time}，应为00:00:00")
            
            print(f"✅ 悬浮小组件已创建，初始时间显示: {initial_time}")
            
        except Exception as e:
            self.status_label.setText(f"创建失败: {str(e)}")
            print(f"Error creating floating widget: {e}")
            import traceback
            traceback.print_exc()
    
    def start_study(self):
        """开始学习"""
        if self.floating_widget:
            print("🔥 开始学习...")
            self.floating_widget.start_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def close_floating_widget(self):
        """关闭悬浮小组件"""
        if self.floating_widget:
            # 记录关闭前的时间
            close_time = self.floating_widget.time_label.text()
            self.floating_widget.hide()
            self.status_label.setText(f"🔒 悬浮小组件已关闭，关闭前时间: {close_time}")
            self.widget_status_label.setText("小组件状态: 已关闭")
            print(f"🔒 悬浮小组件已关闭，关闭前时间: {close_time}")
        else:
            self.status_label.setText("悬浮小组件未创建")
    
    def reopen_floating_widget(self):
        """重新打开悬浮小组件"""
        if self.floating_widget:
            self.floating_widget.show()
            self.floating_widget.raise_()
            self.floating_widget.activateWindow()
            
            # 检查重新打开后的时间显示
            reopen_time = self.floating_widget.time_label.text()
            self.status_label.setText(f"🔓 悬浮小组件已重新打开，时间: {reopen_time}")
            self.widget_status_label.setText("小组件状态: 已重新打开")
            
            print(f"🔓 悬浮小组件已重新打开，时间显示: {reopen_time}")
            
            # 检查时间是否保持
            if reopen_time != "00:00:00":
                self.test_result_label.setText(f"✅ 测试3通过: 重新打开保持时间{reopen_time}")
            else:
                self.test_result_label.setText("❌ 测试3失败: 重新打开时间被重置为00:00:00")
        else:
            self.status_label.setText("悬浮小组件未创建")
    
    def stop_study(self):
        """停止学习"""
        if self.floating_widget:
            print("⏹️ 停止学习...")
            self.floating_widget.stop_session()
            
            # 检查停止后的时间显示
            stop_time = self.floating_widget.time_label.text()
            if stop_time == "00:00:00":
                self.test_result_label.setText("✅ 测试4通过: 停止后时间清零为00:00:00")
            else:
                self.test_result_label.setText(f"❌ 测试4失败: 停止后时间为{stop_time}，应为00:00:00")
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def update_monitor(self):
        """更新监控信息"""
        if self.floating_widget and hasattr(self.floating_widget, 'study_service'):
            try:
                # 获取时间显示
                time_display = self.floating_widget.time_label.text()
                self.time_display_label.setText(f"当前时间: {time_display}")
                
                # 获取会话状态
                cache = self.floating_widget.study_service.current_session_cache
                if cache['id'] is not None:
                    status = cache['status']
                    self.session_status_label.setText(f"会话状态: {status}")
                else:
                    self.session_status_label.setText("会话状态: 无")
                    
            except Exception as e:
                self.time_display_label.setText("当前时间: 错误")
                self.session_status_label.setText("会话状态: 错误")
    
    def on_start_signal(self):
        """开始学习信号"""
        self.status_label.setText("✅ 学习已开始，观察时间从00:00:01开始")
        print("✅ 学习已开始，时间应从00:00:01开始递增")
    
    def on_stop_signal(self):
        """停止学习信号"""
        self.status_label.setText("⏹️ 学习已停止，时间应清零")
        print("⏹️ 学习已停止，时间应清零为00:00:00")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.floating_widget:
            self.floating_widget.close()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建测试窗口
        window = TimeDisplayBehaviorTestWindow()
        window.show()
        
        print("时间显示行为测试程序已启动")
        print("请按照界面提示进行测试")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
