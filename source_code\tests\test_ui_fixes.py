"""
测试界面修复效果
"""

import sys
from pathlib import Path
from datetime import datetime, date, timedelta

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout
from PyQt6.QtCore import QTimer
from PyQt6.QtGui import QFont

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入修复后的任务管理组件
from modules.task_manager import TaskManagerWidget
from modules.task_manager.weekly_task_manager_widget import LakeBlueTheme


class UIFixesTestWindow(QMainWindow):
    """界面修复测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("界面修复测试 - 湖面蓝主题")
        self.setGeometry(50, 50, 1600, 1000)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 测试信息面板
        self.create_test_info_panel()
        layout.addWidget(self.test_info_widget)
        
        # 修复后的任务管理界面
        self.task_manager = TaskManagerWidget()
        layout.addWidget(self.task_manager)
        
        # 设置布局比例
        layout.setStretchFactor(self.test_info_widget, 0)
        layout.setStretchFactor(self.task_manager, 1)
        
        print("🔧 界面修复测试程序已启动")
        print("✅ 验证修复效果")
    
    def create_test_info_panel(self):
        """创建测试信息面板"""
        self.test_info_widget = QWidget()
        self.test_info_widget.setMaximumHeight(120)
        self.test_info_widget.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 {LakeBlueTheme.DEEP_BLUE}, 
                    stop:0.5 {LakeBlueTheme.MEDIUM_BLUE},
                    stop:1 {LakeBlueTheme.SEAWEED_GREEN});
                border-bottom: 3px solid {LakeBlueTheme.LIGHT_BLUE};
            }}
        """)
        
        layout = QVBoxLayout(self.test_info_widget)
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("🔧 界面修复测试 - 湖面蓝主题")
        title_label.setFont(QFont("Inter", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white;")
        layout.addWidget(title_label)
        
        # 修复项目说明
        fixes_text = """
🔧 修复项目：
1. ✅ 左上角"添加任务"和"刷新"按钮显示完整（增加按钮尺寸和工具栏高度）
2. ✅ 页面底部蓝色线条左右两边文字显示完整（增加状态栏高度和边距）
3. ✅ 任务框背景改为透明（移除白色背景填充）
4. ✅ 高优先级任务字体颜色改为紫色（紧急任务为红色）
        """
        fixes_label = QLabel(fixes_text)
        fixes_label.setFont(QFont("", 10))
        fixes_label.setStyleSheet("color: #E0F8FF; line-height: 1.3;")
        fixes_label.setWordWrap(True)
        layout.addWidget(fixes_label)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建测试窗口
        window = UIFixesTestWindow()
        window.show()
        
        print("\n🔧 界面修复测试")
        print("=" * 50)
        print("🎯 修复内容验证：")
        print("1. 检查左上角按钮是否显示完整")
        print("2. 检查底部状态栏文字是否显示完整")
        print("3. 检查任务框是否为透明背景")
        print("4. 检查高优先级任务是否为紫色字体")
        print("5. 检查紧急任务是否为红色字体")
        print("\n💡 测试方法：")
        print("- 创建不同优先级的任务验证颜色")
        print("- 检查界面各部分显示是否完整")
        print("- 验证湖面蓝主题样式是否正确应用")
        print("=" * 50)
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
