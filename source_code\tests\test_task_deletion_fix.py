#!/usr/bin/env python3
"""
Test script to verify the task deletion fix
"""

import sys
from pathlib import Path
from datetime import date, datetime, timedelta

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from core.config import ConfigManager
from core.database import init_db_manager
from modules.task_manager.task_service import TaskService

# Import all models to ensure they are registered
from models import *


def test_task_deletion_fix():
    """Test that deleting a single task doesn't delete all tasks in the same cell"""
    print("Testing Task Deletion Fix")
    print("=" * 40)
    
    try:
        # Initialize database
        config_manager = ConfigManager()
        db_manager = init_db_manager(config_manager)
        
        if not db_manager:
            print("Failed to initialize database")
            return False
        
        # Create tables if they don't exist
        db_manager.create_tables()
        print("✓ Database initialized")
        
        service = TaskService()
        
        # Create test tasks for Monday morning (上午)
        today = date.today()
        monday = today - timedelta(days=today.weekday())  # Get Monday of current week
        
        # Create multiple tasks for the same time slot
        task1_data = {
            'title': '测试任务1 - 周一上午',
            'description': '这是第一个测试任务',
            'start_date': datetime.combine(monday, datetime.min.time()),
            'due_date': datetime.combine(monday, datetime.min.time()) + timedelta(hours=2),
            'priority': 'normal'
        }

        task2_data = {
            'title': '测试任务2 - 周一上午',
            'description': '这是第二个测试任务',
            'start_date': datetime.combine(monday, datetime.min.time()),
            'due_date': datetime.combine(monday, datetime.min.time()) + timedelta(hours=3),
            'priority': 'high'
        }

        task3_data = {
            'title': '测试任务3 - 周一上午',
            'description': '这是第三个测试任务',
            'start_date': datetime.combine(monday, datetime.min.time()),
            'due_date': datetime.combine(monday, datetime.min.time()) + timedelta(hours=4),
            'priority': 'urgent'
        }
        
        # Create the tasks
        task1 = service.create_task(**task1_data)
        task2 = service.create_task(**task2_data)
        task3 = service.create_task(**task3_data)
        
        if not all([task1, task2, task3]):
            print("✗ Failed to create test tasks")
            return False
        
        print(f"✓ Created 3 test tasks for Monday morning:")
        print(f"  - Task 1: {task1.id} - {task1.title}")
        print(f"  - Task 2: {task2.id} - {task2.title}")
        print(f"  - Task 3: {task3.id} - {task3.title}")
        
        # Verify all tasks exist
        all_tasks_before = service.get_tasks(limit=1000)
        monday_morning_tasks_before = [t for t in all_tasks_before if '上午' in t.title]

        print(f"✓ Found {len(monday_morning_tasks_before)} tasks in Monday morning before deletion")

        # Delete only the middle task (task2)
        success = service.delete_task(task2.id)
        if not success:
            print("✗ Failed to delete task2")
            return False

        print(f"✓ Deleted task2: {task2.id}")

        # Verify that only task2 was deleted, task1 and task3 should still exist
        all_tasks_after = service.get_tasks(limit=1000)
        monday_morning_tasks_after = [t for t in all_tasks_after if '上午' in t.title]
        
        print(f"✓ Found {len(monday_morning_tasks_after)} tasks in Monday morning after deletion")
        
        # Check specific tasks
        remaining_task_ids = [t.id for t in monday_morning_tasks_after]
        
        if task1.id in remaining_task_ids:
            print(f"✓ Task1 still exists: {task1.id}")
        else:
            print(f"✗ Task1 was incorrectly deleted: {task1.id}")
            return False
        
        if task2.id not in remaining_task_ids:
            print(f"✓ Task2 was correctly deleted: {task2.id}")
        else:
            print(f"✗ Task2 was not deleted: {task2.id}")
            return False
        
        if task3.id in remaining_task_ids:
            print(f"✓ Task3 still exists: {task3.id}")
        else:
            print(f"✗ Task3 was incorrectly deleted: {task3.id}")
            return False
        
        # Clean up remaining test tasks
        service.delete_task(task1.id)
        service.delete_task(task3.id)
        print("✓ Cleaned up remaining test tasks")
        
        print("\n✅ Task deletion fix test passed!")
        print("Individual task deletion now works correctly without affecting other tasks in the same cell.")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Task Deletion Fix Test")
    print("=" * 50)
    
    success = test_task_deletion_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test completed successfully!")
        print("The task deletion bug has been fixed.")
    else:
        print("❌ Test failed. The bug may still exist.")
