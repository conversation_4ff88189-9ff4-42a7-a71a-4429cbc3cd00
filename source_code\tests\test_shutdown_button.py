#!/usr/bin/env python3
"""
测试关闭所有后台程序功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_shutdown_functionality():
    """测试关闭功能"""
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 初始化配置和数据库
        from core.config import Config
        from core.database import init_database
        
        config = Config()
        init_database()
        
        # 创建主窗口
        from gui.main_window import MainWindow
        main_window = MainWindow(config)
        
        # 显示主窗口
        main_window.show()
        
        # 显示测试信息
        QMessageBox.information(
            main_window,
            "测试信息",
            "主窗口已启动！\n\n"
            "您可以测试以下功能：\n"
            "1. 点击工具栏中的 '🔴 关闭所有后台程序' 按钮\n"
            "2. 使用菜单 '文件' -> '关闭所有后台程序'\n"
            "3. 使用快捷键 Ctrl+Shift+Q\n\n"
            "这些操作将会：\n"
            "• 停止所有定时器\n"
            "• 关闭桌面小部件\n"
            "• 停止守护进程\n"
            "• 清理所有资源\n"
            "• 安全退出应用程序"
        )
        
        # 启动应用程序事件循环
        return app.exec()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = test_shutdown_functionality()
    sys.exit(exit_code)
