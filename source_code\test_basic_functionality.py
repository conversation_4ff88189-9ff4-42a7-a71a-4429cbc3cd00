#!/usr/bin/env python3
"""
Basic Functionality Test - 造神计划
测试基本功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加src路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        # 测试核心模块
        from core.config import ConfigManager
        from core.logger import setup_logging, LoggerMixin
        from core.process_manager import ensure_single_instance, get_process_manager
        from core.process_monitor import ProcessMonitor
        print("✓ 核心模块导入成功")
        
        # 测试通用模块
        from modules.common import BaseDesktopWidget, UnifiedWidgetController
        print("✓ 通用模块导入成功")
        
        # 测试任务管理模块
        from modules.task_manager.unified_task_manager import UnifiedTaskManagerWidget
        from modules.task_manager.strategies import DefaultTheme, LakeBlueTheme, WeeklyView
        print("✓ 任务管理模块导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        # 测试配置管理器
        from core.config import ConfigManager
        config = ConfigManager()
        config.set('test.key', 'test_value')
        assert config.get('test.key') == 'test_value'
        print("✓ 配置管理器工作正常")
        
        # 测试日志功能
        from core.logger import LoggerMixin
        logger_mixin = LoggerMixin()
        logger_mixin.logger.info("测试日志消息")
        print("✓ 日志功能工作正常")
        
        # 测试进程管理器
        from core.process_manager import get_process_manager
        process_manager = get_process_manager()
        info = process_manager.get_process_info()
        assert 'main_process' in info
        print("✓ 进程管理器工作正常")
        
        # 测试主题策略
        from modules.task_manager.strategies import LakeBlueTheme
        theme = LakeBlueTheme()
        colors = theme.get_colors()
        assert 'primary' in colors
        print("✓ 主题策略工作正常")
        
        return True
    except Exception as e:
        print(f"✗ 功能测试失败: {e}")
        return False

def test_gui_components():
    """测试GUI组件（无显示）"""
    print("\n测试GUI组件...")

    try:
        # 测试主题策略类
        from modules.task_manager.strategies import LakeBlueTheme, WeeklyView
        theme = LakeBlueTheme()
        view = WeeklyView(None)

        # 测试基本属性
        assert hasattr(theme, 'apply_styles')
        assert hasattr(theme, 'get_colors')
        assert hasattr(view, 'create_layout')

        print("✓ GUI策略组件测试成功")
        return True
    except Exception as e:
        print(f"✗ GUI组件测试失败: {e}")
        return False

def test_file_organization():
    """测试文件组织"""
    print("\n测试文件组织...")
    
    try:
        # 检查关键目录是否存在
        base_dir = Path(__file__).parent
        
        required_dirs = [
            'src/core',
            'src/modules/common',
            'src/modules/task_manager',
            'src/modules/task_manager/strategies',
            'tests',
            'scripts',
            'docs'
        ]
        
        for dir_path in required_dirs:
            full_path = base_dir / dir_path
            if not full_path.exists():
                print(f"✗ 缺少目录: {dir_path}")
                return False
        
        print("✓ 文件组织结构正确")
        
        # 检查关键文件
        required_files = [
            'main.py',
            'launcher.py',
            'uninstaller.py',
            'src/modules/task_manager/unified_task_manager.py',
            'src/modules/common/base_desktop_widget.py'
        ]
        
        for file_path in required_files:
            full_path = base_dir / file_path
            if not full_path.exists():
                print(f"✗ 缺少文件: {file_path}")
                return False
        
        print("✓ 关键文件存在")
        return True
    except Exception as e:
        print(f"✗ 文件组织测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("造神计划 - 基本功能测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("功能测试", test_basic_functionality),
        ("GUI组件测试", test_gui_components),
        ("文件组织测试", test_file_organization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！造神计划基本功能正常。")
        return True
    else:
        print("❌ 部分测试失败，需要修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)