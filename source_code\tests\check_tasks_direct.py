#!/usr/bin/env python3
"""
直接查询数据库中的任务数据
"""

import sqlite3
from datetime import date
from pathlib import Path

def check_tasks_direct():
    """直接查询数据库中的任务"""
    print("=" * 60)
    print("直接查询数据库中的任务")
    print("=" * 60)
    
    # 数据库路径
    db_path = Path.home() / "AppData" / "Roaming" / "PersonalManager" / "personal_manager.db"
    
    if not db_path.exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"📁 数据库路径: {db_path}")
    
    # 连接数据库
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # 查询今天的任务
    today = date.today()
    print(f"📅 查询日期: {today}")
    
    # 查询所有任务
    cursor.execute("""
        SELECT id, title, description, status, priority, tags, start_date, due_date, created_at
        FROM tasks 
        WHERE DATE(start_date) = ? OR DATE(due_date) = ?
        ORDER BY created_at DESC
    """, (today.isoformat(), today.isoformat()))
    
    tasks = cursor.fetchall()
    
    print(f"📋 今日任务总数: {len(tasks)}")
    
    if not tasks:
        print("❌ 没有找到今日任务")
        
        # 查询所有任务看看数据库中有什么
        cursor.execute("SELECT id, title, tags, start_date, due_date FROM tasks ORDER BY created_at DESC LIMIT 10")
        all_tasks = cursor.fetchall()
        print(f"\n📋 数据库中最近的 {len(all_tasks)} 个任务:")
        for task in all_tasks:
            print(f"   - ID: {task[0]}, 标题: {task[1]}, 标签: {task[2]}, 开始: {task[3]}, 截止: {task[4]}")
        
        conn.close()
        return
    
    print("\n" + "=" * 60)
    print("今日任务详情")
    print("=" * 60)
    
    # 定义分类函数（与修复后的逻辑一致）
    def determine_task_category_fixed(tags):
        """修复后的任务分类逻辑"""
        if tags:
            tags_lower = tags.lower()
            if '日总结' in tags_lower or '总结' in tags_lower:
                return "日总结"
            elif '周计划' in tags_lower:
                return "周计划"
            elif '上午' in tags_lower:
                return "上午"
            elif '下午' in tags_lower:
                return "下午"
            elif '晚上' in tags_lower:
                return "晚上"
            elif '全天' in tags_lower:
                return "全天"
        return "其他"
    
    def determine_task_category_old(tags):
        """旧的任务分类逻辑（WeeklyTaskTable修复前）"""
        if tags:
            tags_lower = tags.lower()
            if '周计划' in tags_lower:
                return "周计划"
            elif '上午' in tags_lower:
                return "上午"
            elif '下午' in tags_lower:
                return "下午"
            elif '晚上' in tags_lower:
                return "晚上"
            elif '全天' in tags_lower:
                return "全天"
        return "其他"
    
    # 按分类统计
    categories_fixed = {}
    categories_old = {}
    
    for task in tasks:
        task_id, title, description, status, priority, tags, start_date, due_date, created_at = task
        
        print(f"\n📝 任务 ID: {task_id}")
        print(f"   📋 标题: {title}")
        print(f"   🏷️  标签: {tags}")
        print(f"   📅 开始日期: {start_date}")
        print(f"   ⏰ 截止日期: {due_date}")
        print(f"   📊 状态: {status}")
        print(f"   🔥 优先级: {priority}")
        
        # 使用两种分类方法
        category_fixed = determine_task_category_fixed(tags)
        category_old = determine_task_category_old(tags)
        
        print(f"   🔧 修复后分类: {category_fixed}")
        print(f"   🐛 修复前分类: {category_old}")
        
        if category_fixed != category_old:
            print(f"   ⚠️  分类发生变化!")
        
        # 统计
        if category_fixed not in categories_fixed:
            categories_fixed[category_fixed] = []
        categories_fixed[category_fixed].append(task)
        
        if category_old not in categories_old:
            categories_old[category_old] = []
        categories_old[category_old].append(task)
    
    print("\n" + "=" * 60)
    print("修复后的分类统计")
    print("=" * 60)
    
    for category, tasks_in_category in categories_fixed.items():
        print(f"📂 {category}: {len(tasks_in_category)} 个任务")
        for task in tasks_in_category:
            print(f"   - {task[1]} (标签: {task[5]})")
    
    print("\n" + "=" * 60)
    print("修复前的分类统计")
    print("=" * 60)
    
    for category, tasks_in_category in categories_old.items():
        print(f"📂 {category}: {len(tasks_in_category)} 个任务")
        for task in tasks_in_category:
            print(f"   - {task[1]} (标签: {task[5]})")
    
    conn.close()

if __name__ == "__main__":
    check_tasks_direct()
