"""
File Manager Widget for Personal Manager System

This widget provides the main file management interface.
"""

import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QTreeView, QListView, QTableView, QLineEdit,
    QPushButton, QComboBox, QLabel, QToolBar,
    QMenu, QMessageBox, QInputDialog,
    QFileDialog, QProgressBar, QTabWidget,
    QHeaderView, QAbstractItemView
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QAbstractTableModel,
    QModelIndex, QVariant, QTimer
)
from PyQt6.QtGui import QIcon, QStandardItemModel, QStandardItem, QAction

from core.logger import LoggerMixin
from .file_service import FileService


class FileTableModel(QAbstractTableModel):
    """Table model for file listing"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.files = []
        self.headers = ['Name', 'Size', 'Type', 'Modified']
    
    def rowCount(self, parent=QModelIndex()):
        return len(self.files)
    
    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)
    
    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or index.row() >= len(self.files):
            return QVariant()
        
        file_info = self.files[index.row()]
        column = index.column()
        
        if role == Qt.ItemDataRole.DisplayRole:
            if column == 0:  # Name
                return file_info.get('name', '')
            elif column == 1:  # Size
                if file_info.get('is_directory', False):
                    return '<DIR>'
                size = file_info.get('size', 0)
                return self.format_size(size)
            elif column == 2:  # Type
                if file_info.get('is_directory', False):
                    return 'Folder'
                ext = file_info.get('extension', '')
                return ext.upper() if ext else 'File'
            elif column == 3:  # Modified
                modified = file_info.get('modified')
                if modified:
                    return modified.strftime('%Y-%m-%d %H:%M')
                return ''
        
        elif role == Qt.ItemDataRole.UserRole:
            return file_info
        
        return QVariant()
    
    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.DisplayRole:
            return self.headers[section]
        return QVariant()
    
    def format_size(self, size):
        """Format file size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    def update_files(self, files):
        """Update the file list"""
        self.beginResetModel()
        self.files = files
        self.endResetModel()


class DirectoryLoadThread(QThread):
    """Thread for loading directory contents"""
    
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, file_service, directory_path):
        super().__init__()
        self.file_service = file_service
        self.directory_path = directory_path
    
    def run(self):
        try:
            contents = self.file_service.get_directory_contents(self.directory_path)
            self.finished.emit(contents)
        except Exception as e:
            self.error.emit(str(e))


class FileManagerWidget(QWidget, LoggerMixin):
    """Main file manager widget"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.file_service = FileService()
        self.current_path = str(Path.home())
        self.load_thread = None

        self.init_ui()
        self.setup_connections()
        self.load_directory(self.current_path)
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Create toolbar
        self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # Create address bar
        self.create_address_bar()
        layout.addLayout(self.address_layout)
        
        # Create main content area
        self.create_main_content()
        layout.addWidget(self.main_splitter)
        
        # Create status bar
        self.create_status_bar()
        layout.addLayout(self.status_layout)
    
    def create_toolbar(self):
        """Create the toolbar"""
        self.toolbar = QToolBar()
        self.toolbar.setMovable(False)
        
        # Navigation buttons
        self.back_action = QAction('Back', self)
        self.back_action.setEnabled(False)
        self.back_action.triggered.connect(self.go_back)
        self.toolbar.addAction(self.back_action)
        
        self.forward_action = QAction('Forward', self)
        self.forward_action.setEnabled(False)
        self.forward_action.triggered.connect(self.go_forward)
        self.toolbar.addAction(self.forward_action)
        
        self.up_action = QAction('Up', self)
        self.up_action.triggered.connect(self.go_up)
        self.toolbar.addAction(self.up_action)
        
        self.toolbar.addSeparator()
        
        # View options
        self.view_combo = QComboBox()
        self.view_combo.addItems(['Table', 'List', 'Icons'])
        self.view_combo.currentTextChanged.connect(self.change_view)
        self.toolbar.addWidget(QLabel('View:'))
        self.toolbar.addWidget(self.view_combo)
        
        self.toolbar.addSeparator()
        
        # File operations
        self.new_folder_action = QAction('New Folder', self)
        self.new_folder_action.triggered.connect(self.create_new_folder)
        self.toolbar.addAction(self.new_folder_action)
        
        self.refresh_action = QAction('Refresh', self)
        self.refresh_action.triggered.connect(self.refresh_current_directory)
        self.toolbar.addAction(self.refresh_action)
    
    def create_address_bar(self):
        """Create the address bar"""
        self.address_layout = QHBoxLayout()
        
        self.address_edit = QLineEdit()
        self.address_edit.returnPressed.connect(self.navigate_to_address)
        self.address_layout.addWidget(QLabel('Location:'))
        self.address_layout.addWidget(self.address_edit)
        
        self.go_button = QPushButton('Go')
        self.go_button.clicked.connect(self.navigate_to_address)
        self.address_layout.addWidget(self.go_button)
    
    def create_main_content(self):
        """Create the main content area"""
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Create sidebar
        self.create_sidebar()
        self.main_splitter.addWidget(self.sidebar_widget)
        
        # Create file view area
        self.create_file_view()
        self.main_splitter.addWidget(self.file_view_widget)
        
        # Set splitter proportions
        self.main_splitter.setSizes([200, 600])
    
    def create_sidebar(self):
        """Create the sidebar with bookmarks and shortcuts"""
        self.sidebar_widget = QWidget()
        sidebar_layout = QVBoxLayout(self.sidebar_widget)
        
        # Quick access section
        sidebar_layout.addWidget(QLabel('Quick Access'))
        
        self.quick_access_list = QListView()
        self.quick_access_model = QStandardItemModel()
        self.quick_access_list.setModel(self.quick_access_model)
        self.quick_access_list.clicked.connect(self.on_quick_access_clicked)
        sidebar_layout.addWidget(self.quick_access_list)
        
        # Add default quick access items
        self.add_quick_access_items()
        
        # Bookmarks section
        sidebar_layout.addWidget(QLabel('Bookmarks'))
        
        self.bookmarks_list = QListView()
        self.bookmarks_model = QStandardItemModel()
        self.bookmarks_list.setModel(self.bookmarks_model)
        self.bookmarks_list.clicked.connect(self.on_bookmark_clicked)
        sidebar_layout.addWidget(self.bookmarks_list)
        
        # Load bookmarks
        self.load_bookmarks()
    
    def create_file_view(self):
        """Create the file view area"""
        self.file_view_widget = QWidget()
        file_view_layout = QVBoxLayout(self.file_view_widget)
        
        # Search bar
        search_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText('Search files...')
        self.search_edit.textChanged.connect(self.on_search_text_changed)
        search_layout.addWidget(self.search_edit)
        
        self.search_button = QPushButton('Search')
        self.search_button.clicked.connect(self.perform_search)
        search_layout.addWidget(self.search_button)
        
        file_view_layout.addLayout(search_layout)
        
        # File table
        self.file_table = QTableView()
        self.file_model = FileTableModel()
        self.file_table.setModel(self.file_model)
        
        # Configure table
        self.file_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.file_table.setAlternatingRowColors(True)
        self.file_table.setSortingEnabled(True)
        self.file_table.doubleClicked.connect(self.on_file_double_clicked)
        self.file_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.file_table.customContextMenuRequested.connect(self.show_context_menu)
        
        # Set column widths
        header = self.file_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        
        file_view_layout.addWidget(self.file_table)
    
    def create_status_bar(self):
        """Create the status bar"""
        self.status_layout = QHBoxLayout()
        
        self.status_label = QLabel('Ready')
        self.status_layout.addWidget(self.status_label)
        
        self.status_layout.addStretch()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_layout.addWidget(self.progress_bar)
        
        self.file_count_label = QLabel()
        self.status_layout.addWidget(self.file_count_label)
    
    def setup_connections(self):
        """Setup signal connections"""
        # Search timer for delayed search
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
    
    def add_quick_access_items(self):
        """Add default quick access items"""
        quick_access_items = [
            ('Desktop', str(Path.home() / 'Desktop')),
            ('Documents', str(Path.home() / 'Documents')),
            ('Downloads', str(Path.home() / 'Downloads')),
            ('Pictures', str(Path.home() / 'Pictures')),
            ('Music', str(Path.home() / 'Music')),
            ('Videos', str(Path.home() / 'Videos')),
        ]
        
        for name, path in quick_access_items:
            if Path(path).exists():
                item = QStandardItem(name)
                item.setData(path, Qt.ItemDataRole.UserRole)
                self.quick_access_model.appendRow(item)

    def load_bookmarks(self):
        """Load bookmarks from database"""
        try:
            bookmarks = self.file_service.get_bookmarks()
            self.bookmarks_model.clear()

            for bookmark in bookmarks:
                item = QStandardItem(bookmark['name'])
                item.setData(bookmark['path'], Qt.ItemDataRole.UserRole)
                if not bookmark['exists']:
                    item.setEnabled(False)
                self.bookmarks_model.appendRow(item)

        except Exception as e:
            self.logger.error(f"Error loading bookmarks: {e}")

    def load_directory(self, path: str):
        """Load directory contents"""
        if self.load_thread and self.load_thread.isRunning():
            self.load_thread.quit()
            self.load_thread.wait()

        self.current_path = path
        self.address_edit.setText(path)
        self.status_label.setText(f'Loading {path}...')
        self.progress_bar.setVisible(True)

        # Start loading thread
        self.load_thread = DirectoryLoadThread(self.file_service, path)
        self.load_thread.finished.connect(self.on_directory_loaded)
        self.load_thread.error.connect(self.on_directory_load_error)
        self.load_thread.start()

    def on_directory_loaded(self, contents: Dict[str, Any]):
        """Handle directory loading completion"""
        self.progress_bar.setVisible(False)

        if 'error' in contents:
            self.status_label.setText(f'Error: {contents["error"]}')
            return

        # Combine files and directories
        all_items = []

        # Add directories first
        for directory in contents.get('directories', []):
            directory['is_directory'] = True
            all_items.append(directory)

        # Add files
        for file_info in contents.get('files', []):
            file_info['is_directory'] = False
            all_items.append(file_info)

        # Update model
        self.file_model.update_files(all_items)

        # Update status
        file_count = contents.get('total_files', 0)
        dir_count = contents.get('total_directories', 0)
        self.file_count_label.setText(f'{dir_count} folders, {file_count} files')
        self.status_label.setText('Ready')

    def on_directory_load_error(self, error: str):
        """Handle directory loading error"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(f'Error: {error}')
        QMessageBox.warning(self, 'Error', f'Failed to load directory: {error}')

    def on_file_double_clicked(self, index: QModelIndex):
        """Handle file double click"""
        file_info = self.file_model.data(index, Qt.ItemDataRole.UserRole)
        if not file_info:
            return

        file_path = file_info['path']

        if file_info.get('is_directory', False):
            # Navigate to directory
            self.load_directory(file_path)
        else:
            # Open file with default application
            self.open_file(file_path)

    def on_quick_access_clicked(self, index: QModelIndex):
        """Handle quick access item click"""
        path = self.quick_access_model.data(index, Qt.ItemDataRole.UserRole)
        if path:
            self.load_directory(path)

    def on_bookmark_clicked(self, index: QModelIndex):
        """Handle bookmark click"""
        path = self.bookmarks_model.data(index, Qt.ItemDataRole.UserRole)
        if path:
            self.load_directory(path)

    def navigate_to_address(self):
        """Navigate to the address in the address bar"""
        path = self.address_edit.text().strip()
        if path and Path(path).exists():
            self.load_directory(path)
        else:
            QMessageBox.warning(self, 'Error', 'Invalid path or path does not exist')

    def go_back(self):
        """Go back in navigation history"""
        # TODO: Implement navigation history
        pass

    def go_forward(self):
        """Go forward in navigation history"""
        # TODO: Implement navigation history
        pass

    def go_up(self):
        """Go up one directory level"""
        current_path = Path(self.current_path)
        parent_path = current_path.parent
        if parent_path != current_path:  # Not at root
            self.load_directory(str(parent_path))

    def change_view(self, view_type: str):
        """Change the file view type"""
        # TODO: Implement different view types (List, Icons)
        pass

    def create_new_folder(self):
        """Create a new folder"""
        name, ok = QInputDialog.getText(self, 'New Folder', 'Folder name:')
        if ok and name:
            new_folder_path = Path(self.current_path) / name
            try:
                new_folder_path.mkdir()
                self.refresh_current_directory()
                self.status_label.setText(f'Created folder: {name}')
            except Exception as e:
                QMessageBox.warning(self, 'Error', f'Failed to create folder: {e}')

    def refresh_current_directory(self):
        """Refresh the current directory"""
        self.load_directory(self.current_path)

    def on_search_text_changed(self, text: str):
        """Handle search text change"""
        self.search_timer.stop()
        if text.strip():
            self.search_timer.start(500)  # Delay search by 500ms

    def perform_search(self):
        """Perform file search"""
        query = self.search_edit.text().strip()
        if not query:
            self.refresh_current_directory()
            return

        try:
            results = self.file_service.search_files(self.current_path, query, True)
            self.file_model.update_files(results)
            self.status_label.setText(f'Found {len(results)} files matching "{query}"')
            self.file_count_label.setText(f'{len(results)} files found')
        except Exception as e:
            self.logger.error(f"Search error: {e}")
            QMessageBox.warning(self, 'Search Error', f'Search failed: {e}')

    def show_context_menu(self, position):
        """Show context menu for file operations"""
        index = self.file_table.indexAt(position)

        menu = QMenu(self)

        if index.isValid():
            file_info = self.file_model.data(index, Qt.ItemDataRole.UserRole)
            file_path = file_info['path']

            # File-specific actions
            open_action = menu.addAction('Open')
            open_action.triggered.connect(lambda: self.open_file(file_path))

            menu.addSeparator()

            copy_action = menu.addAction('Copy')
            copy_action.triggered.connect(lambda: self.copy_file(file_path))

            cut_action = menu.addAction('Cut')
            cut_action.triggered.connect(lambda: self.cut_file(file_path))

            menu.addSeparator()

            rename_action = menu.addAction('Rename')
            rename_action.triggered.connect(lambda: self.rename_file(file_path))

            delete_action = menu.addAction('Delete')
            delete_action.triggered.connect(lambda: self.delete_file(file_path))

            menu.addSeparator()

            bookmark_action = menu.addAction('Add to Bookmarks')
            bookmark_action.triggered.connect(lambda: self.add_bookmark(file_path))

            properties_action = menu.addAction('Properties')
            properties_action.triggered.connect(lambda: self.show_properties(file_path))

        else:
            # General actions
            paste_action = menu.addAction('Paste')
            paste_action.triggered.connect(self.paste_files)

            menu.addSeparator()

            new_folder_action = menu.addAction('New Folder')
            new_folder_action.triggered.connect(self.create_new_folder)

            refresh_action = menu.addAction('Refresh')
            refresh_action.triggered.connect(self.refresh_current_directory)

        menu.exec(self.file_table.mapToGlobal(position))

    def open_file(self, file_path: str):
        """Open file with default application"""
        try:
            import subprocess
            import platform

            # Record file access
            self.file_service.record_file_access(file_path)

            system = platform.system()
            if system == 'Windows':
                os.startfile(file_path)
            elif system == 'Darwin':  # macOS
                subprocess.run(['open', file_path])
            else:  # Linux
                subprocess.run(['xdg-open', file_path])

            self.status_label.setText(f'Opened: {Path(file_path).name}')

        except Exception as e:
            self.logger.error(f"Error opening file {file_path}: {e}")
            QMessageBox.warning(self, 'Error', f'Failed to open file: {e}')

    def copy_file(self, file_path: str):
        """Copy file to clipboard"""
        # TODO: Implement clipboard operations
        self.status_label.setText(f'Copied: {Path(file_path).name}')

    def cut_file(self, file_path: str):
        """Cut file to clipboard"""
        # TODO: Implement clipboard operations
        self.status_label.setText(f'Cut: {Path(file_path).name}')

    def paste_files(self):
        """Paste files from clipboard"""
        # TODO: Implement clipboard operations
        self.status_label.setText('Paste operation not implemented yet')

    def rename_file(self, file_path: str):
        """Rename a file"""
        current_name = Path(file_path).name
        new_name, ok = QInputDialog.getText(self, 'Rename', 'New name:', text=current_name)

        if ok and new_name and new_name != current_name:
            try:
                if self.file_service.rename_file(file_path, new_name):
                    self.refresh_current_directory()
                    self.status_label.setText(f'Renamed to: {new_name}')
                else:
                    QMessageBox.warning(self, 'Error', 'Failed to rename file')
            except Exception as e:
                QMessageBox.warning(self, 'Error', f'Failed to rename file: {e}')

    def delete_file(self, file_path: str):
        """Delete a file"""
        file_name = Path(file_path).name
        reply = QMessageBox.question(
            self, 'Delete File',
            f'Are you sure you want to delete "{file_name}"?',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                if self.file_service.delete_file(file_path, use_trash=True):
                    self.refresh_current_directory()
                    self.status_label.setText(f'Deleted: {file_name}')
                else:
                    QMessageBox.warning(self, 'Error', 'Failed to delete file')
            except Exception as e:
                QMessageBox.warning(self, 'Error', f'Failed to delete file: {e}')

    def add_bookmark(self, file_path: str):
        """Add file to bookmarks"""
        try:
            if self.file_service.add_bookmark(file_path):
                self.load_bookmarks()
                self.status_label.setText(f'Added bookmark: {Path(file_path).name}')
            else:
                QMessageBox.information(self, 'Info', 'Bookmark already exists')
        except Exception as e:
            QMessageBox.warning(self, 'Error', f'Failed to add bookmark: {e}')

    def show_properties(self, file_path: str):
        """Show file properties dialog"""
        file_info = self.file_service.get_file_info(file_path)
        if file_info:
            # TODO: Create a proper properties dialog
            info_text = f"""
File: {file_info['name']}
Path: {file_info['path']}
Size: {file_info.get('size_formatted', 'N/A')}
Modified: {file_info['modified'].strftime('%Y-%m-%d %H:%M:%S')}
Type: {'Directory' if file_info['is_directory'] else 'File'}
            """
            QMessageBox.information(self, 'Properties', info_text.strip())
