@echo off
REM Personal Manager Deployment Script
REM This script provides multiple deployment options for Personal Manager

:MENU
cls
echo ========================================
echo Personal Manager Deployment Options
echo ========================================
echo.
echo Please choose a deployment option:
echo.
echo 1. Create Desktop Shortcut (Batch Launcher)
echo 2. Build Standalone Executable
echo 3. Install Dependencies Only
echo 4. Test Application
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto SHORTCUT
if "%choice%"=="2" goto EXECUTABLE
if "%choice%"=="3" goto DEPENDENCIES
if "%choice%"=="4" goto TEST
if "%choice%"=="5" goto EXIT
goto INVALID

:SHORTCUT
echo.
echo ========================================
echo Creating Desktop Shortcut
echo ========================================
echo.
call install_shortcut.bat
echo.
pause
goto MENU

:EXECUTABLE
echo.
echo ========================================
echo Building Standalone Executable
echo ========================================
echo.
echo This will create a single .exe file that includes all dependencies.
echo This process may take several minutes...
echo.
set /p confirm="Continue? (y/n): "
if /i not "%confirm%"=="y" goto MENU

python build_executable.py
echo.
pause
goto MENU

:DEPENDENCIES
echo.
echo ========================================
echo Installing Dependencies
echo ========================================
echo.
echo Installing required Python packages...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt
echo.
echo Dependencies installation complete!
pause
goto MENU

:TEST
echo.
echo ========================================
echo Testing Application
echo ========================================
echo.
echo Starting Personal Manager in test mode...
echo Close the application window to return to this menu.
echo.
python main.py
echo.
echo Application test complete!
pause
goto MENU

:INVALID
echo.
echo Invalid choice. Please enter a number between 1 and 5.
echo.
pause
goto MENU

:EXIT
echo.
echo Thank you for using Personal Manager!
echo.
exit /b 0
