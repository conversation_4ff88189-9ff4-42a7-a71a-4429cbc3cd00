"""
Main Window for Personal Manager System
"""

import sys
from pathlib import Path
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QToolBar, QStatusBar,
    QMessageBox, QSplitter, QTreeWidget,
    QTreeWidgetItem, QLabel, QPushButton
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QCoreApplication, QLockFile, QDir
from PyQt6.QtGui import QIcon, QPixmap, QFont, QAction

from core.config import Config
from core.logger import LoggerMixin


class MainWindow(QMainWindow, LoggerMixin):
    """Main application window"""
    
    # Signals
    closing = pyqtSignal()
    
    def __init__(self, config: Config):
        super().__init__()
        self.config = config
        self.modules = {}

        # Initialize UI
        self.init_ui()
        self.setup_menu_bar()
        self.setup_tool_bar()
        self.setup_status_bar()
        self.setup_central_widget()
        
        # Load settings
        self.load_window_settings()
        
        # Setup auto-save timer
        self.setup_auto_save()

        self.logger.info("Main window initialized")
    
    def init_ui(self):
        """Initialize basic UI properties"""
        self.setWindowTitle(self.config.get('app.name', 'Personal Manager'))
        
        # Set window icon
        icon_path = Path(__file__).parent.parent.parent / "resources" / "icons" / "app.ico"
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))
        
        # Set minimum size
        self.setMinimumSize(800, 600)
        
        # Set default size
        default_size = self.config.get('gui.window_size', [1200, 800])
        self.resize(default_size[0], default_size[1])
        
        # Center window
        self.center_window()
    
    def center_window(self):
        """Center window on screen"""
        screen = self.screen().availableGeometry()
        window = self.frameGeometry()
        window.moveCenter(screen.center())
        self.move(window.topLeft())
    
    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('文件(&F)')
        
        # New action
        new_action = QAction('新建(&N)', self)
        new_action.setShortcut('Ctrl+N')
        new_action.setStatusTip('创建新项目')
        new_action.triggered.connect(self.new_file)
        file_menu.addAction(new_action)
        
        # Open action
        open_action = QAction('打开(&O)', self)
        open_action.setShortcut('Ctrl+O')
        open_action.setStatusTip('打开文件')
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # Settings action
        settings_action = QAction('设置(&S)', self)
        settings_action.setShortcut('Ctrl+,')
        settings_action.setStatusTip('打开设置')
        settings_action.triggered.connect(self.open_settings)
        file_menu.addAction(settings_action)
        
        file_menu.addSeparator()

        # Exit action
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.setStatusTip('退出应用程序')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu('视图(&V)')
        
        # Toggle sidebar
        toggle_sidebar_action = QAction('切换侧边栏(&S)', self)
        toggle_sidebar_action.setShortcut('Ctrl+B')
        toggle_sidebar_action.setCheckable(True)
        toggle_sidebar_action.setChecked(True)
        toggle_sidebar_action.triggered.connect(self.toggle_sidebar)
        view_menu.addAction(toggle_sidebar_action)


        
        # Tools menu
        tools_menu = menubar.addMenu('工具(&T)')
        
        # System monitor
        system_monitor_action = QAction('系统监控(&M)', self)
        system_monitor_action.setStatusTip('打开系统监控')
        system_monitor_action.triggered.connect(self.open_system_monitor)
        tools_menu.addAction(system_monitor_action)
        
        # File manager
        file_manager_action = QAction('文件管理器(&F)', self)
        file_manager_action.setStatusTip('打开文件管理器')
        file_manager_action.triggered.connect(self.open_file_manager)
        tools_menu.addAction(file_manager_action)
        
        # Help menu
        help_menu = menubar.addMenu('帮助(&H)')



        # About action
        about_action = QAction('关于(&A)', self)
        about_action.setStatusTip('关于此应用程序')
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_tool_bar(self):
        """Setup tool bar"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setMovable(False)
        
        # Quick access buttons
        file_manager_btn = QAction('文件管理', self)
        file_manager_btn.setStatusTip('打开文件管理器')
        file_manager_btn.triggered.connect(self.open_file_manager)
        toolbar.addAction(file_manager_btn)
        
        system_monitor_btn = QAction('系统监控', self)
        system_monitor_btn.setStatusTip('打开系统监控')
        system_monitor_btn.triggered.connect(self.open_system_monitor)
        toolbar.addAction(system_monitor_btn)
        
        toolbar.addSeparator()

        settings_btn = QAction('设置', self)
        settings_btn.setStatusTip('打开设置')
        settings_btn.triggered.connect(self.open_settings)
        toolbar.addAction(settings_btn)

        toolbar.addSeparator()

        # 关闭所有后台程序按钮
        shutdown_btn = QAction('🔴 退出应用', self)
        shutdown_btn.setStatusTip('停止所有后台程序并退出应用程序')
        shutdown_btn.triggered.connect(self.close) # Changed to self.close for graceful exit
        toolbar.addAction(shutdown_btn)
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = self.statusBar()
        
        # Status label
        self.status_label = QLabel('就绪')
        self.status_bar.addWidget(self.status_label)
        
        # Add permanent widgets
        self.status_bar.addPermanentWidget(QLabel('Personal Manager v1.0.0'))
    
    def setup_central_widget(self):
        """Setup central widget with sidebar and main content"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # Create splitter
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.splitter)
        
        # Setup sidebar
        self.setup_sidebar()
        
        # Setup main content area
        self.setup_main_content()
        
        # Set splitter proportions
        self.splitter.setSizes([250, 950])
    
    def setup_sidebar(self):
        """Setup sidebar with navigation"""
        sidebar_widget = QWidget()
        sidebar_layout = QVBoxLayout(sidebar_widget)
        
        # Navigation tree
        self.nav_tree = QTreeWidget()
        self.nav_tree.setHeaderLabel('功能模块')
        self.nav_tree.itemClicked.connect(self.on_nav_item_clicked)
        
        # Add navigation items
        self.add_nav_items()
        
        sidebar_layout.addWidget(self.nav_tree)
        self.splitter.addWidget(sidebar_widget)
    
    def add_nav_items(self):
        """Add navigation items to sidebar"""
        # File Manager
        file_manager_item = QTreeWidgetItem(['文件管理器'])
        file_manager_item.setData(0, Qt.ItemDataRole.UserRole, 'file_manager')
        self.nav_tree.addTopLevelItem(file_manager_item)
        
        # System Monitor
        system_monitor_item = QTreeWidgetItem(['系统监控'])
        system_monitor_item.setData(0, Qt.ItemDataRole.UserRole, 'system_monitor')
        self.nav_tree.addTopLevelItem(system_monitor_item)
        
        # Task Manager
        task_manager_item = QTreeWidgetItem(['任务管理'])
        task_manager_item.setData(0, Qt.ItemDataRole.UserRole, 'task_manager')
        self.nav_tree.addTopLevelItem(task_manager_item)

        # Study Tracker
        study_tracker_item = QTreeWidgetItem(['学习时间追踪'])
        study_tracker_item.setData(0, Qt.ItemDataRole.UserRole, 'study_tracker')
        self.nav_tree.addTopLevelItem(study_tracker_item)


        # Note Manager
        note_manager_item = QTreeWidgetItem(['笔记管理'])
        note_manager_item.setData(0, Qt.ItemDataRole.UserRole, 'note_manager')
        self.nav_tree.addTopLevelItem(note_manager_item)
        
        # Password Manager
        password_manager_item = QTreeWidgetItem(['密码管理'])
        password_manager_item.setData(0, Qt.ItemDataRole.UserRole, 'password_manager')
        self.nav_tree.addTopLevelItem(password_manager_item)
    
    def setup_main_content(self):
        """Setup main content area"""
        self.main_content = QTabWidget()
        self.main_content.setTabsClosable(True)
        self.main_content.tabCloseRequested.connect(self.close_tab)
        
        # Add welcome tab
        self.add_welcome_tab()
        
        self.splitter.addWidget(self.main_content)
    
    def add_welcome_tab(self):
        """Add welcome tab"""
        welcome_widget = QWidget()
        welcome_layout = QVBoxLayout(welcome_widget)
        
        # Welcome message
        welcome_label = QLabel('欢迎使用个人管理系统')
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        welcome_label.setFont(QFont('Arial', 16, QFont.Weight.Bold))
        welcome_layout.addWidget(welcome_label)
        
        # Quick actions
        quick_actions_layout = QHBoxLayout()
        
        file_manager_btn = QPushButton('文件管理器')
        file_manager_btn.clicked.connect(self.open_file_manager)
        quick_actions_layout.addWidget(file_manager_btn)
        
        system_monitor_btn = QPushButton('系统监控')
        system_monitor_btn.clicked.connect(self.open_system_monitor)
        quick_actions_layout.addWidget(system_monitor_btn)
        
        welcome_layout.addLayout(quick_actions_layout)
        welcome_layout.addStretch()
        
        self.main_content.addTab(welcome_widget, '欢迎')
    
    def setup_auto_save(self):
        """Setup auto-save timer"""
        auto_save_interval = self.config.get('app.auto_save_interval', 300) * 1000  # Convert to ms

        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.auto_save_timer.start(auto_save_interval)

    def show_and_activate(self):
        """显示并激活主窗口"""
        self.show()
        self.raise_()
        self.activateWindow()
        self.logger.info("Main window activated by floating widget")


    
    # Event handlers
    def on_nav_item_clicked(self, item, column):
        """Handle navigation item click"""
        module_name = item.data(0, Qt.ItemDataRole.UserRole)
        if module_name:
            self.open_module(module_name)
    
    def open_module(self, module_name: str):
        """Open a module in a new tab"""
        # Get display name first to check if module is already open
        display_name = self.get_module_display_name(module_name)

        # Check if module is already open by display name
        for i in range(self.main_content.count()):
            if self.main_content.tabText(i) == display_name:
                self.main_content.setCurrentIndex(i)
                self.logger.info(f"Module {module_name} already open, switching to existing tab")
                return

        # Create module widget based on module name
        module_widget = None

        try:
            if module_name == 'file_manager':
                from modules.file_manager import FileManagerWidget
                module_widget = FileManagerWidget()
            elif module_name == 'system_monitor':
                from modules.system_monitor import SystemMonitorWidget
                module_widget = SystemMonitorWidget()
            elif module_name == 'task_manager':
                from modules.task_manager import TaskManagerWidget
                module_widget = TaskManagerWidget()
            elif module_name == 'study_tracker':
                from modules.study_tracker import StudyTrackerWidget
                module_widget = StudyTrackerWidget()
            elif module_name == 'note_manager':
                # TODO: Implement note manager
                module_widget = QWidget()
                layout = QVBoxLayout(module_widget)
                layout.addWidget(QLabel('笔记管理模块正在开发中...'))
            elif module_name == 'password_manager':
                # TODO: Implement password manager
                module_widget = QWidget()
                layout = QVBoxLayout(module_widget)
                layout.addWidget(QLabel('密码管理模块正在开发中...'))
            else:
                # Default placeholder
                module_widget = QWidget()
                layout = QVBoxLayout(module_widget)
                layout.addWidget(QLabel(f'{module_name} 模块正在开发中...'))

            if module_widget:
                self.main_content.addTab(module_widget, display_name)
                self.main_content.setCurrentWidget(module_widget)
                self.logger.info(f"Opened module: {module_name}")

        except Exception as e:
            self.logger.error(f"Error opening module {module_name}: {e}")
            # Fallback to placeholder
            module_widget = QWidget()
            layout = QVBoxLayout(module_widget)
            layout.addWidget(QLabel(f'加载 {module_name} 模块时出错: {str(e)}'))
            self.main_content.addTab(module_widget, display_name)
            self.main_content.setCurrentWidget(module_widget)

    def get_module_display_name(self, module_name: str) -> str:
        """Get display name for module"""
        display_names = {
            'file_manager': '文件管理器',
            'system_monitor': '系统监控',
            'task_manager': '任务管理',
            'study_tracker': '学习时间追踪',
            'note_manager': '笔记管理',
            'password_manager': '密码管理'
        }
        return display_names.get(module_name, module_name)
    
    def close_tab(self, index: int):
        """Close tab at index"""
        if index > 0:  # Don't close welcome tab
            self.main_content.removeTab(index)
    
    def toggle_sidebar(self, checked: bool):
        """Toggle sidebar visibility"""
        sidebar_widget = self.splitter.widget(0)
        sidebar_widget.setVisible(checked)
    
    # Menu actions
    def new_file(self):
        """Create new file"""
        self.status_label.setText('新建文件...')
    
    def open_file(self):
        """Open file"""
        self.status_label.setText('打开文件...')
    
    def open_settings(self):
        """Open settings dialog"""
        self.status_label.setText('打开设置...')
    
    def open_file_manager(self):
        """Open file manager module"""
        self.open_module('file_manager')
    
    def open_system_monitor(self):
        """Open system monitor module"""
        self.open_module('system_monitor')
    


    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            '关于',
            f'{self.config.get("app.name")}\n'
            f'版本: {self.config.get("app.version")}\n\n'
            '一个功能强大的个人管理系统'
        )
    
    def auto_save(self):
        """Auto-save application state"""
        self.save_window_settings()
        self.logger.debug('Auto-save completed')
    
    def load_window_settings(self):
        """Load window settings"""
        if self.config.get('gui.remember_geometry', True):
            # Load window position and size
            pass
    
    def save_window_settings(self):
        """Save window settings"""
        if self.config.get('gui.remember_geometry', True):
            # Save window position and size
            self.config.set('gui.window_size', [self.width(), self.height()])
            self.config.set('gui.window_position', [self.x(), self.y()])
    
    def close(self):
        """
        Overrides the default close method to provide a confirmation dialog.
        This is connected to the 'Exit' button and menu action.
        """
        reply = QMessageBox.question(
            self,
            '确认退出',
            '您确定要退出 Personal Manager 吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # closeAllWindows() will trigger closeEvent on all windows
            QApplication.instance().closeAllWindows()

    def closeEvent(self, event):
        """Handle window close event"""
        self.logger.info("Main window close event triggered.")
        self.save_window_settings()
        self.closing.emit()
        event.accept()
        self.logger.info("Main window state saved, closing.")
