"""
全局快捷键管理器

提供系统级全局快捷键注册和处理功能
"""

import sys
import threading
from typing import Dict, Callable, Optional
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

from core.logger import LoggerMixin

# Windows API 支持
if sys.platform == "win32":
    try:
        import ctypes
        from ctypes import wintypes
        import ctypes.wintypes as wintypes
        
        # Windows API 常量
        MOD_ALT = 0x0001
        MOD_CONTROL = 0x0002
        MOD_SHIFT = 0x0004
        MOD_WIN = 0x0008
        
        WM_HOTKEY = 0x0312
        
        # Windows API 函数
        user32 = ctypes.windll.user32
        kernel32 = ctypes.windll.kernel32
        
        WINDOWS_API_AVAILABLE = True
    except ImportError:
        WINDOWS_API_AVAILABLE = False
else:
    WINDOWS_API_AVAILABLE = False


class GlobalHotkeyManager(QObject, LoggerMixin):
    """全局快捷键管理器"""
    
    # 信号
    hotkey_triggered = pyqtSignal(str)  # 快捷键触发信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.hotkeys: Dict[int, Dict] = {}  # 注册的快捷键
        self.next_id = 1  # 下一个快捷键ID
        self.message_thread = None  # 消息循环线程
        self.running = False  # 运行状态
        
        # 检查平台支持
        if not WINDOWS_API_AVAILABLE:
            self.logger.warning("Windows API not available, global hotkeys disabled")
            return
        
        self.logger.info("Global hotkey manager initialized")
    
    def is_supported(self) -> bool:
        """检查是否支持全局快捷键"""
        return WINDOWS_API_AVAILABLE
    
    def register_hotkey(self, key_combination: str, callback: Callable, description: str = "") -> bool:
        """
        注册全局快捷键
        
        Args:
            key_combination: 快捷键组合，如 "Ctrl+Shift+S"
            callback: 回调函数
            description: 快捷键描述
            
        Returns:
            是否注册成功
        """
        if not self.is_supported():
            self.logger.warning("Global hotkeys not supported on this platform")
            return False
        
        try:
            # 解析快捷键组合
            modifiers, vk_code = self._parse_key_combination(key_combination)
            if modifiers is None or vk_code is None:
                self.logger.error(f"Invalid key combination: {key_combination}")
                return False
            
            # 注册快捷键
            hotkey_id = self.next_id
            self.next_id += 1
            
            success = user32.RegisterHotKey(None, hotkey_id, modifiers, vk_code)
            if not success:
                error_code = kernel32.GetLastError()
                self.logger.error(f"Failed to register hotkey {key_combination}: Error {error_code}")
                return False
            
            # 保存快捷键信息
            self.hotkeys[hotkey_id] = {
                'combination': key_combination,
                'callback': callback,
                'description': description,
                'modifiers': modifiers,
                'vk_code': vk_code
            }
            
            # 启动消息循环（如果还没启动）
            if not self.running:
                self._start_message_loop()
            
            self.logger.info(f"Registered global hotkey: {key_combination} - {description}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error registering hotkey {key_combination}: {e}")
            return False
    
    def unregister_hotkey(self, key_combination: str) -> bool:
        """
        注销全局快捷键
        
        Args:
            key_combination: 快捷键组合
            
        Returns:
            是否注销成功
        """
        if not self.is_supported():
            return False
        
        try:
            # 查找快捷键ID
            hotkey_id = None
            for hid, info in self.hotkeys.items():
                if info['combination'] == key_combination:
                    hotkey_id = hid
                    break
            
            if hotkey_id is None:
                self.logger.warning(f"Hotkey not found: {key_combination}")
                return False
            
            # 注销快捷键
            success = user32.UnregisterHotKey(None, hotkey_id)
            if success:
                del self.hotkeys[hotkey_id]
                self.logger.info(f"Unregistered global hotkey: {key_combination}")
            else:
                error_code = kernel32.GetLastError()
                self.logger.error(f"Failed to unregister hotkey {key_combination}: Error {error_code}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error unregistering hotkey {key_combination}: {e}")
            return False
    
    def unregister_all(self):
        """注销所有快捷键"""
        if not self.is_supported():
            return
        
        for hotkey_id in list(self.hotkeys.keys()):
            try:
                user32.UnregisterHotKey(None, hotkey_id)
            except Exception as e:
                self.logger.error(f"Error unregistering hotkey ID {hotkey_id}: {e}")
        
        self.hotkeys.clear()
        self.running = False
        self.logger.info("All global hotkeys unregistered")
    
    def get_registered_hotkeys(self) -> Dict[str, str]:
        """获取已注册的快捷键列表"""
        return {info['combination']: info['description'] for info in self.hotkeys.values()}
    
    def _parse_key_combination(self, combination: str) -> tuple:
        """
        解析快捷键组合
        
        Args:
            combination: 快捷键组合字符串，如 "Ctrl+Shift+S"
            
        Returns:
            (modifiers, vk_code) 元组
        """
        try:
            parts = [part.strip() for part in combination.split('+')]
            modifiers = 0
            vk_code = None
            
            # 修饰键映射
            modifier_map = {
                'ctrl': MOD_CONTROL,
                'control': MOD_CONTROL,
                'alt': MOD_ALT,
                'shift': MOD_SHIFT,
                'win': MOD_WIN,
                'windows': MOD_WIN
            }
            
            # 虚拟键码映射
            vk_map = {
                'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46,
                'g': 0x47, 'h': 0x48, 'i': 0x49, 'j': 0x4A, 'k': 0x4B, 'l': 0x4C,
                'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
                's': 0x53, 't': 0x54, 'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58,
                'y': 0x59, 'z': 0x5A,
                '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
                '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
                'f1': 0x70, 'f2': 0x71, 'f3': 0x72, 'f4': 0x73, 'f5': 0x74,
                'f6': 0x75, 'f7': 0x76, 'f8': 0x77, 'f9': 0x78, 'f10': 0x79,
                'f11': 0x7A, 'f12': 0x7B,
                'space': 0x20, 'enter': 0x0D, 'escape': 0x1B, 'tab': 0x09,
                'backspace': 0x08, 'delete': 0x2E, 'insert': 0x2D,
                'home': 0x24, 'end': 0x23, 'pageup': 0x21, 'pagedown': 0x22,
                'up': 0x26, 'down': 0x28, 'left': 0x25, 'right': 0x27
            }
            
            for part in parts:
                part_lower = part.lower()
                
                if part_lower in modifier_map:
                    modifiers |= modifier_map[part_lower]
                elif part_lower in vk_map:
                    vk_code = vk_map[part_lower]
                else:
                    # 尝试单字符
                    if len(part) == 1:
                        vk_code = ord(part.upper())
                    else:
                        return None, None
            
            return modifiers, vk_code
            
        except Exception as e:
            self.logger.error(f"Error parsing key combination {combination}: {e}")
            return None, None
    
    def _start_message_loop(self):
        """启动Windows消息循环"""
        if not self.is_supported() or self.running:
            return
        
        self.running = True
        self.message_thread = threading.Thread(target=self._message_loop, daemon=True)
        self.message_thread.start()
        self.logger.info("Started global hotkey message loop")
    
    def _message_loop(self):
        """Windows消息循环"""
        try:
            msg = wintypes.MSG()
            
            while self.running:
                # 获取消息
                bRet = user32.GetMessageW(ctypes.byref(msg), None, 0, 0)
                
                if bRet == 0:  # WM_QUIT
                    break
                elif bRet == -1:  # 错误
                    self.logger.error("GetMessage error")
                    break
                
                # 处理热键消息
                if msg.message == WM_HOTKEY:
                    hotkey_id = msg.wParam
                    if hotkey_id in self.hotkeys:
                        hotkey_info = self.hotkeys[hotkey_id]
                        
                        # 在主线程中触发信号
                        self.hotkey_triggered.emit(hotkey_info['combination'])
                        
                        # 调用回调函数
                        try:
                            hotkey_info['callback']()
                        except Exception as e:
                            self.logger.error(f"Error in hotkey callback: {e}")
                
                # 分发消息
                user32.TranslateMessage(ctypes.byref(msg))
                user32.DispatchMessageW(ctypes.byref(msg))
                
        except Exception as e:
            self.logger.error(f"Error in message loop: {e}")
        finally:
            self.running = False
    
    def __del__(self):
        """析构函数，清理资源"""
        self.unregister_all()


# 全局实例
_global_hotkey_manager = None


def get_global_hotkey_manager() -> GlobalHotkeyManager:
    """获取全局快捷键管理器实例"""
    global _global_hotkey_manager
    if _global_hotkey_manager is None:
        _global_hotkey_manager = GlobalHotkeyManager()
    return _global_hotkey_manager
