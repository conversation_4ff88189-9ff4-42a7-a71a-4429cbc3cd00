#!/usr/bin/env python3
"""
测试周计划任务分类修复
验证周计划任务不再错误地出现在小组件的"其他"分类中
"""

import sys
import os
from datetime import datetime, date

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 直接定义分类方法，避免导入问题

def determine_task_category_desktop_widget(task) -> str:
    """桌面小组件的任务分类逻辑（修复后）"""
    if task.tags:
        tags = task.tags.lower()
        if '日总结' in tags or '总结' in tags:
            return '日总结'
        elif '周计划' in tags:
            return None  # 周计划任务不在小组件中显示
        elif '上午' in tags:
            return '上午'
        elif '下午' in tags:
            return '下午'
        elif '晚上' in tags:
            return '晚上'
        elif '全天' in tags:
            return '全天'
    return '其他'

def determine_task_category_weekly_table(task) -> str:
    """桌面应用端的任务分类逻辑"""
    if task.tags:
        tags = task.tags.lower()
        if '日总结' in tags or '总结' in tags:
            return "日总结"
        elif '周计划' in tags:
            return "周计划"
        elif '上午' in tags:
            return "上午"
        elif '下午' in tags:
            return "下午"
        elif '晚上' in tags:
            return "晚上"
        elif '全天' in tags:
            return "全天"
    return "其他"

class MockTask:
    """模拟任务对象"""
    def __init__(self, id, title, tags, start_date=None, due_date=None):
        self.id = id
        self.title = title
        self.tags = tags
        self.start_date = start_date
        self.due_date = due_date
        self.description = ""
        self.status = "pending"
        self.priority = "medium"

def test_task_categorization():
    """测试任务分类逻辑"""
    print("=" * 60)
    print("测试周计划任务分类修复")
    print("=" * 60)
    
    # 创建测试任务
    test_tasks = [
        MockTask(1, "学习程序设计制作内容", "周计划", datetime.now()),
        MockTask(2, "准备开会", "上午", datetime.now()),
        MockTask(3, "写代码", "下午", datetime.now()),
        MockTask(4, "看书", "晚上", datetime.now()),
        MockTask(5, "锻炼身体", "全天", datetime.now()),
        MockTask(6, "其他杂事", "其他", datetime.now()),
        MockTask(7, "今日总结", "日总结", datetime.now()),
        MockTask(8, "无标签任务", "", datetime.now()),
    ]
    
    # 使用分类方法
    
    print("\n任务分类对比:")
    print("-" * 60)
    print(f"{'任务标题':<20} {'标签':<10} {'桌面应用':<10} {'小组件':<10} {'状态'}")
    print("-" * 60)
    
    for task in test_tasks:
        weekly_category = determine_task_category_weekly_table(task)
        desktop_category = determine_task_category_desktop_widget(task)
        
        # 检查分类状态
        if task.tags == "周计划":
            if desktop_category is None:
                status = "✅ 正确"
            else:
                status = "❌ 错误"
        else:
            if weekly_category == desktop_category:
                status = "✅ 一致"
            else:
                status = "⚠️ 不同"
        
        desktop_display = "不显示" if desktop_category is None else desktop_category
        
        print(f"{task.title:<20} {task.tags:<10} {weekly_category:<10} {desktop_display:<10} {status}")
    
    print("\n" + "=" * 60)
    print("修复验证结果")
    print("=" * 60)
    
    # 检查周计划任务
    weekly_plan_tasks = [task for task in test_tasks if task.tags == "周计划"]
    for task in weekly_plan_tasks:
        desktop_category = determine_task_category_desktop_widget(task)
        if desktop_category is None:
            print(f"✅ 周计划任务 '{task.title}' 正确地不在小组件中显示")
        else:
            print(f"❌ 周计划任务 '{task.title}' 错误地被分类为 '{desktop_category}'")
    
    # 检查其他分类任务
    other_tasks = [task for task in test_tasks if task.tags in ["其他", ""]]
    print(f"\n📂 真正的'其他'分类任务:")
    for task in other_tasks:
        desktop_category = determine_task_category_desktop_widget(task)
        print(f"   - {task.title} (标签: '{task.tags}') → {desktop_category}")

if __name__ == "__main__":
    test_task_categorization()
