"""
调试控制器状态检查逻辑
"""

import sys
from pathlib import Path
import time

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件和控制器
from modules.study_tracker.study_floating_widget import StudyFloatingWidget
from modules.study_tracker.study_floating_widget_controller import StudyFloatingWidgetController


def debug_controller_status():
    """调试控制器状态检查"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        print("\n🔍 调试控制器状态检查逻辑")
        print("=" * 50)
        
        # 1. 创建控制器
        print("1. 创建控制器...")
        controller = StudyFloatingWidgetController()
        
        print(f"   状态文件路径: {controller.widget_status_file}")
        print(f"   状态文件存在: {controller.widget_status_file.exists()}")
        
        # 2. 重写check_status方法添加调试信息
        original_check_status = controller.check_status
        
        def debug_check_status(self):
            print(f"\n   🔍 check_status 被调用")
            print(f"   状态文件存在: {self.widget_status_file.exists()}")
            
            # 检查小组件是否通过状态文件通知关闭
            status_file_closed = False
            if self.widget_status_file.exists():
                try:
                    with open(self.widget_status_file, 'r', encoding='utf-8') as f:
                        status_content = f.read().strip()
                    print(f"   状态文件内容: {status_content}")
                    if status_content.startswith('closed:'):
                        print("   ✅ 检测到关闭状态")
                        status_file_closed = True
                        
                        # 删除状态文件
                        try:
                            self.widget_status_file.unlink()
                            print("   ✅ 状态文件已删除")
                        except Exception as delete_error:
                            print(f"   ❌ 删除状态文件失败: {delete_error}")
                    else:
                        print("   状态文件内容不是关闭状态")
                except Exception as e:
                    print(f"   ❌ 读取状态文件失败: {e}")
            else:
                print("   状态文件不存在")
            
            # 检查进程状态
            process_running = self.is_running()
            print(f"   进程检查结果: {'运行中' if process_running else '已停止'}")
            
            # 根据状态文件通知决定当前状态
            if status_file_closed:
                current_status = False
                print("   最终状态: 已停止（基于状态文件）")
            else:
                current_status = process_running
                print(f"   最终状态: {'运行中' if current_status else '已停止'}（基于进程检查）")
            
            if current_status != self._is_running:
                print(f"   状态变化: {self._is_running} → {current_status}")
                self._update_status(current_status)
            else:
                print("   状态无变化")
        
        # 绑定调试方法
        controller.check_status = lambda: debug_check_status(controller)
        
        # 3. 手动触发一次状态检查
        print("\n2. 初始状态检查...")
        controller.check_status()
        
        # 4. 创建悬浮小组件
        print("\n3. 创建悬浮小组件...")
        widget = StudyFloatingWidget()
        widget.show()
        print("   ✅ 悬浮小组件已创建")
        
        # 等待状态检查
        time.sleep(3)
        print("\n4. 创建后状态检查...")
        controller.check_status()
        
        # 5. 关闭悬浮小组件
        print("\n5. 关闭悬浮小组件...")
        widget.close()
        print("   ✅ 悬浮小组件已关闭")
        
        # 6. 立即检查状态
        time.sleep(1)
        print("\n6. 关闭后立即状态检查...")
        controller.check_status()
        
        # 7. 等待定时器触发
        print("\n7. 等待定时器触发状态检查...")
        for i in range(5):
            time.sleep(1)
            print(f"   等待第{i+1}秒...")
        
        print("\n8. 最终状态检查...")
        controller.check_status()
        
        app.quit()
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        app.quit()


if __name__ == "__main__":
    debug_controller_status()
