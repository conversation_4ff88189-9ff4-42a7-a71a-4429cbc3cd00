@echo off
REM Personal Manager Application Launcher
REM This script ensures the application starts with the correct environment

REM Set the script directory as the working directory
cd /d "%~dp0"

REM Set environment variables for better Qt experience
set QT_AUTO_SCREEN_SCALE_FACTOR=1
set QT_ENABLE_HIGHDPI_SCALING=1
set QT_SCALE_FACTOR_ROUNDING_POLICY=RoundPreferFloor

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8 or later and add it to your PATH
    pause
    exit /b 1
)

REM Check if required packages are installed
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo Error: PyQt6 is not installed
    echo Installing required dependencies...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Start the application without showing console window
start /B pythonw main.py
