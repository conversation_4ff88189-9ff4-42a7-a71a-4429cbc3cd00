@echo off
REM Personal Manager Silent Launcher
REM This script starts the application without showing any console windows

REM Set the script directory as the working directory
cd /d "%~dp0"

REM Set environment variables for better Qt experience
set QT_AUTO_SCREEN_SCALE_FACTOR=1
set QT_ENABLE_HIGHDPI_SCALING=1
set QT_SCALE_FACTOR_ROUNDING_POLICY=RoundPreferFloor

REM Start the application silently using pythonw (no console window)
pythonw main.py
