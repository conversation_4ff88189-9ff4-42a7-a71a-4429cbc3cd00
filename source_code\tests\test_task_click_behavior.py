#!/usr/bin/env python3
"""
测试任务点击行为 - 验证点击任务时不会自动最小化
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from PyQt6.QtWidgets import QApplication, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt6.QtCore import QTimer

def test_task_click_behavior():
    """测试任务点击行为"""
    print("开始测试任务点击行为...")
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = QWidget()
    test_window.setWindowTitle("任务点击行为测试")
    test_window.setGeometry(100, 100, 400, 300)
    
    layout = QVBoxLayout(test_window)
    
    # 添加说明
    info_label = QLabel("""
测试说明：
1. 点击下面的"启动桌面小部件"按钮
2. 等待桌面小部件显示
3. 点击桌面小部件中的任务卡片
4. 观察桌面小部件是否保持显示状态
5. 检查任务详情对话框是否正常弹出

预期结果：
- 桌面小部件应该保持显示，不会自动最小化到系统托盘
- 任务详情对话框应该正常弹出并可以编辑
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 启动桌面小部件按钮
    start_btn = QPushButton("启动桌面小部件")
    
    def start_widget():
        try:
            from src.modules.task_manager.desktop_widget import DesktopTaskWidget
            
            # 创建桌面小部件
            widget = DesktopTaskWidget()
            widget.show()
            
            start_btn.setText("桌面小部件已启动")
            start_btn.setEnabled(False)
            
            print("桌面小部件已启动，请点击任务卡片测试行为")
            
        except Exception as e:
            print(f"启动失败: {e}")
            start_btn.setText(f"启动失败: {e}")
    
    start_btn.clicked.connect(start_widget)
    layout.addWidget(start_btn)
    
    # 关闭按钮
    close_btn = QPushButton("关闭测试")
    close_btn.clicked.connect(app.quit)
    layout.addWidget(close_btn)
    
    test_window.show()
    
    print("测试窗口已显示")
    print("请按照说明进行测试")
    
    # 运行应用
    app.exec()

if __name__ == "__main__":
    test_task_click_behavior()
