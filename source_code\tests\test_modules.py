#!/usr/bin/env python3
"""Test script to verify all modules work correctly"""

import sys
import os
import traceback

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_system_monitor():
    """Test system monitor module"""
    print("Testing System Monitor Module...")
    try:
        from modules.system_monitor.system_service import SystemService
        
        service = SystemService()
        
        # Test basic info methods
        print("  Testing get_system_info...")
        system_info = service.get_system_info()
        print(f"    System: {system_info.get('system', 'Unknown')}")
        
        print("  Testing get_cpu_info...")
        cpu_info = service.get_cpu_info()
        print(f"    CPU Usage: {cpu_info.get('cpu_usage_total', 0):.2f}%")
        
        print("  Testing get_memory_info...")
        memory_info = service.get_memory_info()
        print(f"    Memory Usage: {memory_info.get('percentage', 0):.2f}%")
        
        print("  Testing get_disk_info...")
        disk_info = service.get_disk_info()
        print(f"    Found {len(disk_info)} disk(s)")
        
        print("  Testing get_network_info...")
        network_info = service.get_network_info()
        print(f"    Network interfaces: {len(network_info.get('interfaces', {}))}")
        
        print("  Testing record_system_metrics...")
        service.record_system_metrics()
        print("    Metrics recorded successfully")
        
        print("  System Monitor Module: ✓ PASSED")
        return True
        
    except Exception as e:
        print(f"  System Monitor Module: ✗ FAILED - {e}")
        print(f"  Traceback: {traceback.format_exc()}")
        return False

def test_file_manager():
    """Test file manager module"""
    print("Testing File Manager Module...")
    try:
        from modules.file_manager.file_service import FileService
        
        service = FileService()
        
        # Test directory listing
        print("  Testing get_directory_contents...")
        current_dir = os.getcwd()
        files = service.get_directory_contents(current_dir)
        print(f"    Found {len(files)} items in current directory")
        
        print("  File Manager Module: ✓ PASSED")
        return True
        
    except Exception as e:
        print(f"  File Manager Module: ✗ FAILED - {e}")
        print(f"  Traceback: {traceback.format_exc()}")
        return False

def test_task_manager():
    """Test task manager module"""
    print("Testing Task Manager Module...")
    try:
        from modules.task_manager.task_service import TaskService
        
        service = TaskService()
        
        # Test getting tasks
        print("  Testing get_tasks...")
        tasks = service.get_tasks()
        print(f"    Found {len(tasks)} tasks")
        
        print("  Task Manager Module: ✓ PASSED")
        return True
        
    except Exception as e:
        print(f"  Task Manager Module: ✗ FAILED - {e}")
        print(f"  Traceback: {traceback.format_exc()}")
        return False



def test_database():
    """Test database connectivity"""
    print("Testing Database...")
    try:
        from core.database import init_db_manager, get_session
        from models.system_models import SystemMetrics
        from core.config import Config

        # Initialize config and database
        config = Config()
        db_manager = init_db_manager(config=config)

        with get_session() as session:
            # Test query
            metrics_count = session.query(SystemMetrics).count()
            print(f"    Found {metrics_count} system metrics records")

        print("  Database: ✓ PASSED")
        return True

    except Exception as e:
        print(f"  Database: ✗ FAILED - {e}")
        print(f"  Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all tests"""
    print("Personal Manager System - Module Testing")
    print("=" * 50)
    
    tests = [
        test_database,
        test_system_monitor,
        test_file_manager,
        test_task_manager,

    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test failed with exception: {e}")
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests PASSED! The application is working correctly.")
    else:
        print("⚠️  Some tests FAILED. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
