"""
学习追踪悬浮小组件控制器

管理学习追踪悬浮小组件的启动、停止和状态监控
"""

import os
import sys
import subprocess
import psutil
import winreg
from pathlib import Path
from typing import Optional
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

from core.logger import LoggerMixin
from core.config import ConfigManager


class StudyFloatingWidgetController(QObject, LoggerMixin):
    """学习追踪悬浮小组件控制器"""
    
    # 信号
    status_changed = pyqtSignal(bool)  # 状态变化信号 (True=运行中, False=已停止)
    error_occurred = pyqtSignal(str)   # 错误信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config_manager = ConfigManager()
        
        # 路径配置
        self.project_root = Path(__file__).parent.parent.parent.parent.parent / "source_code"
        self.daemon_script = self.project_root / "study_floating_daemon.py"
        self.daemon_pid_file = self.project_root / "study_floating_daemon.pid"
        self.start_script = self.project_root / "start_study_floating_widget.py"
        self.widget_status_file = self.project_root.parent / "study_floating_status.txt"
        
        # 状态监控定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.check_status)
        self.status_timer.start(2000)  # 每2秒检查一次状态
        
        # 当前状态
        self._is_running = False
        self.daemon_process = None

        # 状态文件关闭通知缓存
        self._status_file_closed_time = None
        
    def is_running(self) -> bool:
        """检查学习追踪悬浮小组件是否正在运行"""
        try:
            # 检查守护进程
            if self.daemon_pid_file.exists():
                with open(self.daemon_pid_file, 'r') as f:
                    pid = int(f.read().strip())
                if psutil.pid_exists(pid):
                    return True
            
            # 检查相关进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if ('start_study_floating_widget.py' in cmdline or 
                        'study_floating_widget.py' in cmdline or
                        'study_floating_daemon.py' in cmdline):
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查运行状态失败: {e}")
            return False
    
    def start_widget(self) -> bool:
        """启动学习追踪悬浮小组件"""
        try:
            # 检查是否已在运行
            is_running = self.is_running()
            
            if is_running:
                self.logger.info("学习追踪悬浮小组件已在运行，发送手动接管命令")
                result = self.send_manual_takeover_command()
                return result
            
            self.logger.info("启动学习追踪悬浮小组件守护进程...")
            
            # 启动守护进程
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSFSENCODING'] = '0'
            if os.name == 'nt':
                env['PYTHONUTF8'] = '1'
            
            self.daemon_process = subprocess.Popen(
                [sys.executable, str(self.daemon_script), "--manual"],
                cwd=str(self.project_root),
                env=env,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            
            self.logger.info(f"守护进程已启动，PID: {self.daemon_process.pid}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动学习追踪悬浮小组件失败: {e}")
            self.error_occurred.emit(f"启动失败: {str(e)}")
            return False
    
    def stop_widget(self) -> bool:
        """停止学习追踪悬浮小组件"""
        try:
            is_running = self.is_running()
            if not is_running:
                self.logger.info("学习追踪悬浮小组件未在运行")
                return True
            
            self.logger.info("停止学习追踪悬浮小组件...")
            
            # 停止守护进程
            if self.daemon_pid_file.exists():
                try:
                    with open(self.daemon_pid_file, 'r') as f:
                        pid = int(f.read().strip())
                    
                    if psutil.pid_exists(pid):
                        process = psutil.Process(pid)
                        process.terminate()
                        
                        try:
                            process.wait(timeout=3)
                        except psutil.TimeoutExpired:
                            process.kill()
                            
                except Exception as e:
                    self.logger.warning(f"停止守护进程失败: {e}")
            
            # 停止所有相关进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if ('start_study_floating_widget.py' in cmdline or 
                        'study_floating_widget.py' in cmdline or
                        'study_floating_daemon.py' in cmdline):
                        proc.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 检查停止状态
            import time
            for i in range(5):
                time.sleep(0.1)
                if not self.is_running():
                    self.logger.info("学习追踪悬浮小组件已停止")
                    self._update_status(False)
                    return True
            
            self.logger.info("学习追踪悬浮小组件停止完成")
            self.daemon_process = None
            self._update_status(False)
            return True
            
        except Exception as e:
            self.logger.error(f"停止学习追踪悬浮小组件失败: {e}")
            self.error_occurred.emit(f"停止失败: {str(e)}")
            return False
    
    def check_status(self):
        """检查状态并发送信号"""
        # 检查小组件是否通过状态文件通知关闭
        status_file_closed = False
        if self.widget_status_file.exists():
            try:
                with open(self.widget_status_file, 'r', encoding='utf-8') as f:
                    status_content = f.read().strip()

                if status_content.startswith('closed:'):
                    status_file_closed = True
                    self.logger.info("检测到学习追踪悬浮小组件已关闭（通过状态文件）")

                    # 记录状态文件关闭时间
                    import time
                    self._status_file_closed_time = time.time()

                    # 删除状态文件
                    try:
                        self.widget_status_file.unlink()
                    except Exception as delete_error:
                        self.logger.warning(f"删除状态文件失败: {delete_error}")
            except Exception as e:
                self.logger.warning(f"读取状态文件失败: {e}")

        # 检查进程状态
        process_running = self.is_running()

        # 检查是否在状态文件关闭后的缓存期内（10秒）
        import time
        current_time = time.time()
        in_closed_cache_period = (
            self._status_file_closed_time is not None and
            current_time - self._status_file_closed_time < 10
        )

        # 根据状态文件通知决定当前状态
        if status_file_closed:
            current_status = False
        elif in_closed_cache_period:
            current_status = False
        else:
            current_status = process_running
            # 如果进程检查显示运行中，清除关闭缓存
            if process_running:
                self._status_file_closed_time = None

        if current_status != self._is_running:
            self._update_status(current_status)
    
    def send_manual_takeover_command(self) -> bool:
        """发送手动接管命令给已运行的守护进程"""
        try:
            command_file = self.project_root / "study_floating_command.txt"
            
            with open(command_file, 'w', encoding='utf-8') as f:
                f.write("manual_takeover")
            
            self.logger.info("已发送手动接管命令给守护进程")
            return True
            
        except Exception as e:
            self.logger.error(f"发送手动接管命令失败: {e}")
            return False
    
    def send_show_command(self) -> bool:
        """发送显示命令给已运行的守护进程"""
        try:
            command_file = self.project_root / "study_floating_command.txt"
            with open(command_file, 'w', encoding='utf-8') as f:
                f.write("show_widget")
            
            self.logger.info("已发送显示命令给守护进程")
            return True
            
        except Exception as e:
            self.logger.error(f"发送显示命令失败: {e}")
            return False
    
    def _update_status(self, is_running: bool):
        """更新状态并发送信号"""
        self.logger.info(f"StudyFloatingWidgetController status updated: {is_running}")

        self._is_running = is_running
        self.status_changed.emit(is_running)
    
    def get_widget_enabled_setting(self) -> bool:
        """获取悬浮小组件开关设置"""
        return self.config_manager.get('study_floating_widget.enabled', False)
    
    def set_widget_enabled_setting(self, enabled: bool):
        """保存悬浮小组件开关设置"""
        self.config_manager.set('study_floating_widget.enabled', enabled)
        self.config_manager.save()
