#!/usr/bin/env python3
"""
Test script for task creation functionality
"""

import sys
import os
from pathlib import Path
from datetime import datetime, date

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_task_creation():
    """Test task creation functionality"""
    print("Testing task creation...")
    
    try:
        # Initialize core components
        from core.config import ConfigManager
        from core.database import init_db_manager
        from core.logger import setup_logging, get_logger
        
        # Setup logging
        setup_logging(level='DEBUG')
        logger = get_logger(__name__)
        
        # Initialize configuration and database
        config = ConfigManager()
        db_manager = init_db_manager(config)
        
        # Initialize task service
        from modules.task_manager.task_service import TaskService
        task_service = TaskService()
        
        # Test task creation
        test_task_data = {
            'title': '测试任务',
            'description': '这是一个测试任务',
            'priority': 'normal',
            'due_date': datetime.now(),
            'start_date': datetime.now(),
            'tags': ['上午', '测试']
        }
        
        print(f"Creating task with data: {test_task_data}")
        
        task = task_service.create_task(
            title=test_task_data['title'],
            description=test_task_data['description'],
            priority=test_task_data['priority'],
            due_date=test_task_data['due_date'],
            start_date=test_task_data['start_date'],
            tags=test_task_data['tags']
        )
        
        if task:
            print(f"✓ Task created successfully: {task.title}")
            print(f"  ID: {task.id}")
            print(f"  Priority: {task.priority}")
            print(f"  Start Date: {task.start_date}")
            print(f"  Tags: {task.tags}")
            return True
        else:
            print("✗ Task creation failed")
            return False
            
    except Exception as e:
        logger.error(f"Error in task creation test: {e}")
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("Task Creation Test")
    print("=" * 30)
    
    success = test_task_creation()
    
    print("\n" + "=" * 30)
    if success:
        print("✓ Task creation test passed!")
        return 0
    else:
        print("✗ Task creation test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
