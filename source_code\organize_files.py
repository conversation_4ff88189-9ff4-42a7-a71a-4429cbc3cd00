#!/usr/bin/env python3
"""
File Organization Script - 造神计划
整理source_code目录中的文件，按类型分类存放
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict


class FileOrganizer:
    """文件整理器"""
    
    def __init__(self, source_dir: str):
        self.source_dir = Path(source_dir)
        self.organized_count = 0
        self.error_count = 0
        
        # 定义文件分类规则
        self.file_categories = {
            'tests': {
                'patterns': ['test_*.py', 'debug_*.py', 'verify_*.py', 'check_*.py'],
                'description': '测试和调试文件'
            },
            'scripts': {
                'patterns': ['*.bat', '*.vbs', '*.ps1', 'start_*.py', 'install_*.py', 
                           'disable_*.py', 'reset_*.py', 'fix_*.py', 'clean_*.py'],
                'description': '脚本和工具文件'
            },
            'docs': {
                'patterns': ['*.md', '*.txt'],
                'description': '文档文件'
            },
            'config': {
                'patterns': ['*.json', '*.yaml', '*.yml', '*.ini'],
                'description': '配置文件'
            },
            'logs': {
                'patterns': ['*.log', '*.pid'],
                'description': '日志和进程文件'
            },
            'deprecated': {
                'patterns': ['*_daemon.py', '*_backup.py'],
                'description': '废弃的文件'
            },
            'temp': {
                'patterns': ['*.tmp', '*.temp', '__pycache__'],
                'description': '临时文件'
            }
        }
    
    def create_directories(self):
        """创建目标目录"""
        for category in self.file_categories.keys():
            target_dir = self.source_dir / category
            target_dir.mkdir(exist_ok=True)
            print(f"创建目录: {category}/")
    
    def should_skip_file(self, file_path: Path) -> bool:
        """检查是否应该跳过文件"""
        skip_files = {
            'main.py', 'setup.py', 'requirements.txt', 
            'universal_widget_daemon.py', 'organize_files.py'
        }
        
        skip_dirs = {'src', 'resources', 'tests', 'docs'}
        
        return (file_path.name in skip_files or 
                file_path.is_dir() and file_path.name in skip_dirs)
    
    def get_file_category(self, file_path: Path) -> str:
        """获取文件应该归属的分类"""
        file_name = file_path.name
        
        # 检查每个分类的模式
        for category, config in self.file_categories.items():
            for pattern in config['patterns']:
                if file_path.match(pattern):
                    return category
        
        # 特殊处理：版本号文件夹
        if file_name.replace('.', '').isdigit():
            return 'temp'
        
        # 默认分类
        return 'misc'
    
    def move_file(self, source_file: Path, target_category: str) -> bool:
        """移动文件到目标分类"""
        try:
            target_dir = self.source_dir / target_category
            target_dir.mkdir(exist_ok=True)
            
            target_file = target_dir / source_file.name
            
            # 如果目标文件已存在，添加序号
            counter = 1
            while target_file.exists():
                name_parts = source_file.stem, counter, source_file.suffix
                target_file = target_dir / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                counter += 1
            
            shutil.move(str(source_file), str(target_file))
            print(f"  移动: {source_file.name} -> {target_category}/")
            self.organized_count += 1
            return True
        
        except Exception as e:
            print(f"  移动失败: {source_file.name} - {e}")
            self.error_count += 1
            return False
    
    def organize_files(self):
        """整理文件"""
        print("开始整理文件 - 造神计划")
        print("=" * 50)
        
        # 创建目标目录
        self.create_directories()
        print()

        # 获取所有需要整理的文件
        all_files = [f for f in self.source_dir.iterdir()
                    if f.is_file() and not self.should_skip_file(f)]

        print(f"发现 {len(all_files)} 个文件需要整理")
        print()

        # 按分类整理文件
        for category, config in self.file_categories.items():
            print(f"整理 {category}/ - {config['description']}")
            
            category_files = [f for f in all_files 
                            if self.get_file_category(f) == category]
            
            if category_files:
                for file_path in category_files:
                    self.move_file(file_path, category)
            else:
                print(f"  (无文件)")
            print()
        
        # 处理其他文件
        remaining_files = [f for f in self.source_dir.iterdir() 
                          if f.is_file() and not self.should_skip_file(f)]
        
        if remaining_files:
            print("整理 misc/ - 其他文件")
            misc_dir = self.source_dir / 'misc'
            misc_dir.mkdir(exist_ok=True)

            for file_path in remaining_files:
                self.move_file(file_path, 'misc')
            print()

        # 清理空的版本号目录
        self.cleanup_version_dirs()

        # 显示整理结果
        self.show_summary()
    
    def cleanup_version_dirs(self):
        """清理版本号目录"""
        print("清理版本号目录")
        
        version_dirs = [d for d in self.source_dir.iterdir() 
                       if d.is_dir() and d.name.replace('.', '').isdigit()]
        
        for version_dir in version_dirs:
            try:
                if not any(version_dir.iterdir()):  # 空目录
                    version_dir.rmdir()
                    print(f"  删除空目录: {version_dir.name}/")
                else:
                    # 移动到temp目录
                    temp_dir = self.source_dir / 'temp'
                    temp_dir.mkdir(exist_ok=True)
                    shutil.move(str(version_dir), str(temp_dir / version_dir.name))
                    print(f"  移动: {version_dir.name}/ -> temp/")
            except Exception as e:
                print(f"  处理失败: {version_dir.name} - {e}")
        print()
    
    def show_summary(self):
        """显示整理摘要"""
        print("=" * 50)
        print("整理完成摘要")
        print(f"成功整理: {self.organized_count} 个文件")
        if self.error_count > 0:
            print(f"失败: {self.error_count} 个文件")

        print("\n整理后的目录结构:")
        self.show_directory_structure()
    
    def show_directory_structure(self):
        """显示目录结构"""
        for item in sorted(self.source_dir.iterdir()):
            if item.is_dir() and item.name not in {'__pycache__'}:
                file_count = len([f for f in item.iterdir() if f.is_file()])
                print(f"  {item.name}/ ({file_count} 个文件)")


def main():
    """主函数"""
    script_dir = Path(__file__).parent
    organizer = FileOrganizer(script_dir)
    
    try:
        organizer.organize_files()
        print("\n文件整理完成！")
    except Exception as e:
        print(f"\n整理过程中发生错误: {e}")


if __name__ == "__main__":
    main()