"""
Personal Manager System - 数据模型模块

这个模块包含了系统中使用的所有数据模型。

模块列表:
- base: 基础模型类和通用模型
- file_models: 文件管理相关模型
- task_models: 任务管理相关模型
- system_models: 系统监控相关模型
- app_models: 应用管理和笔记相关模型

"""

# 基础模型
from .base import BaseModel, Setting, AuditLog

# 文件管理模型
from .file_models import (
    FileBookmark, FileTag, FileTagRelation,
    FileHistory, FileSearch
)

# 任务管理模型
from .task_models import (
    TaskStatus, TaskPriority, TaskCategory, Task,
    TaskReminder, TaskTemplate, TaskComment, DailySummary, WeeklySummary
)

# 系统监控模型
from .system_models import (
    SystemMetrics, ProcessMonitor, HardwareInfo, SystemAlert
)

# 应用和笔记模型
from .app_models import (
    Application, Note, NoteCategory, NoteAttachment, PasswordEntry
)

# 学习时间记录模型
from .study_models import (
    StudySubject, StudySession, StudyGoal, StudyBreak,
    StudyStatus, GoalType
)



__all__ = [
    # 基础模型
    'BaseModel', 'Setting', 'AuditLog',

    # 文件管理模型
    'FileBookmark', 'FileTag', 'FileTagRelation',
    'FileHistory', 'FileSearch',

    # 任务管理模型
    'TaskStatus', 'TaskPriority', 'TaskCategory', 'Task',
    'TaskReminder', 'TaskTemplate', 'TaskComment', 'DailySummary', 'WeeklySummary',

    # 系统监控模型
    'SystemMetrics', 'ProcessMonitor', 'HardwareInfo', 'SystemAlert',

    # 应用和笔记模型
    'Application', 'Note', 'NoteCategory', 'NoteAttachment', 'PasswordEntry',

    # 学习时间记录模型
    'StudySubject', 'StudySession', 'StudyGoal', 'StudyBreak',
    'StudyStatus', 'GoalType',
]