"""
Weekly Summary Service for Task Manager Module

This service provides business logic for weekly summary management.
"""

import logging
from datetime import datetime, date, timedelta
from typing import Optional, Dict, List

from core.database import get_session
from models.task_models import WeeklySummary


class WeeklySummaryService:
    """Service for managing weekly summaries"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def save_weekly_summary(self, week_start_date: date, content: str) -> bool:
        """Save or update weekly summary
        
        Args:
            week_start_date: Start date of the week for the summary
            content: Summary content
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with get_session() as session:
                # Convert date to datetime if needed
                if isinstance(week_start_date, date):
                    week_start_datetime = datetime.combine(week_start_date, datetime.min.time())
                else:
                    week_start_datetime = week_start_date
                
                # Create or update summary
                summary = WeeklySummary.create_or_update(
                    session=session,
                    week_start_date=week_start_datetime,
                    content=content
                )
                
                session.commit()
                self.logger.info(f"Weekly summary saved for week starting: {week_start_date}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error saving weekly summary: {e}")
            return False
    
    def get_weekly_summary(self, week_start_date: date) -> str:
        """Get weekly summary for a specific week
        
        Args:
            week_start_date: Start date of the week to get summary for
            
        Returns:
            str: Summary content or empty string if not found
        """
        try:
            with get_session() as session:
                # Convert date to datetime if needed
                if isinstance(week_start_date, date):
                    week_start_datetime = datetime.combine(week_start_date, datetime.min.time())
                else:
                    week_start_datetime = week_start_date
                
                summary = WeeklySummary.get_by_week_start(session, week_start_datetime)
                
                if summary:
                    return summary.content or ""
                else:
                    return ""
                    
        except Exception as e:
            self.logger.error(f"Error getting weekly summary: {e}")
            return ""
    
    def get_summaries_for_month(self, month_start: date) -> Dict[date, str]:
        """Get all weekly summaries for a month
        
        Args:
            month_start: Start date of the month
            
        Returns:
            Dict[date, str]: Dictionary mapping week start dates to summary content
        """
        try:
            summaries = {}
            
            # Get summaries for each week of the month (approximately 4-5 weeks)
            current_date = month_start
            for i in range(6):  # Maximum 6 weeks to cover any month
                # Calculate week start (Monday)
                days_since_monday = current_date.weekday()
                week_start = current_date - timedelta(days=days_since_monday)
                
                content = self.get_weekly_summary(week_start)
                if content:  # Only include non-empty summaries
                    summaries[week_start] = content
                
                # Move to next week
                current_date += timedelta(days=7)
                
                # Stop if we've moved to the next month
                if current_date.month != month_start.month:
                    break
            
            return summaries
            
        except Exception as e:
            self.logger.error(f"Error getting monthly summaries: {e}")
            return {}
    
    def delete_weekly_summary(self, week_start_date: date) -> bool:
        """Delete weekly summary for a specific week
        
        Args:
            week_start_date: Start date of the week to delete summary for
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with get_session() as session:
                # Convert date to datetime if needed
                if isinstance(week_start_date, date):
                    week_start_datetime = datetime.combine(week_start_date, datetime.min.time())
                else:
                    week_start_datetime = week_start_date
                
                summary = WeeklySummary.get_by_week_start(session, week_start_datetime)
                
                if summary:
                    summary.is_deleted = True
                    summary.updated_at = datetime.utcnow()
                    session.commit()
                    self.logger.info(f"Weekly summary deleted for week starting: {week_start_date}")
                    return True
                else:
                    self.logger.warning(f"No summary found for week starting: {week_start_date}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error deleting weekly summary: {e}")
            return False
    
    def get_all_weekly_summaries(self) -> List[Dict]:
        """Get all weekly summaries
        
        Returns:
            List[Dict]: List of dictionaries containing summary data
        """
        try:
            with get_session() as session:
                summaries = session.query(WeeklySummary).filter(
                    WeeklySummary.is_deleted == False
                ).order_by(WeeklySummary.week_start_date.desc()).all()
                
                return [
                    {
                        'id': summary.id,
                        'week_start_date': summary.week_start_date,
                        'content': summary.content,
                        'created_at': summary.created_at,
                        'updated_at': summary.updated_at
                    }
                    for summary in summaries
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting all weekly summaries: {e}")
            return []
