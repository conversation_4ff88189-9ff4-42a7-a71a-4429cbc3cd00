"""
Unified Widget Controller - Manages desktop widgets with unified interface
"""

import os
from pathlib import Path
from typing import Optional
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

from core.logger import LoggerMixin
from core.config import ConfigManager
from .process_manager import WidgetProcessManager
from .communication_manager import WidgetCommunicationManager


class UnifiedWidgetController(QObject, LoggerMixin):
    """统一的小组件控制器"""
    
    # 信号
    status_changed = pyqtSignal(bool)  # 状态变化信号 (True=运行中, False=已停止)
    error_occurred = pyqtSignal(str)   # 错误信号
    
    def __init__(self, widget_type: str, parent=None):
        super().__init__(parent)
        self.widget_type = widget_type
        self.config_manager = ConfigManager()
        
        # 管理器
        self.process_manager = WidgetProcessManager(widget_type)
        self.communication_manager = WidgetCommunicationManager(widget_type)
        
        # 连接信号
        self.process_manager.process_started.connect(self._on_process_started)
        self.process_manager.process_stopped.connect(self._on_process_stopped)
        
        # 状态监控定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.check_status)
        self.status_timer.start(2000)  # 每2秒检查一次
        
        # 当前状态
        self._is_running = False
        
        self.logger.info(f"Unified widget controller initialized for {widget_type}")
    
    def is_running(self) -> bool:
        """检查小组件是否正在运行"""
        return self.process_manager.is_running()
    
    def start_widget(self) -> bool:
        """启动小组件"""
        try:
            # 检查是否已在运行
            if self.is_running():
                self.logger.info(f"{self.widget_type} widget already running, sending show command")
                return self.send_show_command()
            
            self.logger.info(f"Starting {self.widget_type} widget...")
            
            # 启动守护进程
            success = self.process_manager.start_daemon(manual_mode=True)
            if success:
                self.logger.info(f"{self.widget_type} widget started successfully")
            else:
                self.error_occurred.emit(f"Failed to start {self.widget_type} widget")
            
            return success
        
        except Exception as e:
            error_msg = f"Error starting {self.widget_type} widget: {e}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def stop_widget(self) -> bool:
        """停止小组件"""
        try:
            if not self.is_running():
                self.logger.info(f"{self.widget_type} widget not running")
                return True
            
            self.logger.info(f"Stopping {self.widget_type} widget...")
            
            # 停止守护进程
            success = self.process_manager.stop_daemon()
            if success:
                self.logger.info(f"{self.widget_type} widget stopped successfully")
            else:
                self.error_occurred.emit(f"Failed to stop {self.widget_type} widget")
            
            return success
        
        except Exception as e:
            error_msg = f"Error stopping {self.widget_type} widget: {e}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def restart_widget(self) -> bool:
        """重启小组件"""
        self.logger.info(f"Restarting {self.widget_type} widget...")
        return self.process_manager.restart_daemon()
    
    def send_show_command(self) -> bool:
        """发送显示命令"""
        return self.communication_manager.send_show_command("show")
    
    def send_hide_command(self) -> bool:
        """发送隐藏命令"""
        return self.communication_manager.send_show_command("hide")
    
    def send_toggle_command(self) -> bool:
        """发送切换显示命令"""
        return self.communication_manager.send_show_command("toggle")
    
    def send_manual_takeover_command(self) -> bool:
        """发送手动接管命令"""
        return self.communication_manager.send_command_to_daemon("manual_takeover")
    
    def force_terminate_all_processes(self):
        """强制终止所有相关进程"""
        self.logger.warning(f"Force terminating all {self.widget_type} processes")
        self.process_manager.force_kill_all_related_processes()
    
    def check_status(self):
        """检查状态并发送信号"""
        try:
            # 检查状态文件是否通知关闭
            status_file = self.communication_manager.status_file
            status_file_closed = False
            
            if status_file.exists():
                try:
                    with open(status_file, 'r', encoding='utf-8') as f:
                        status_content = f.read().strip()
                    
                    if status_content.startswith('closed:'):
                        self.logger.info(f"Detected {self.widget_type} widget closed via status file")
                        status_file_closed = True
                        
                        # 强制终止所有相关进程
                        self.force_terminate_all_processes()
                        
                        # 删除状态文件
                        try:
                            status_file.unlink()
                        except Exception as delete_error:
                            self.logger.warning(f"Failed to delete status file: {delete_error}")
                
                except Exception as e:
                    self.logger.warning(f"Failed to read status file: {e}")
            
            # 确定当前状态
            if status_file_closed:
                current_status = False
            else:
                current_status = self.is_running()
            
            # 如果状态发生变化，发送信号
            if current_status != self._is_running:
                self._update_status(current_status)
        
        except Exception as e:
            self.logger.error(f"Error checking status: {e}")
    
    def _update_status(self, new_status: bool):
        """更新状态并发送信号"""
        self._is_running = new_status
        self.status_changed.emit(new_status)
        
        status_text = "running" if new_status else "stopped"
        self.logger.info(f"{self.widget_type} widget status changed to: {status_text}")
    
    def _on_process_started(self, pid: int):
        """进程启动事件处理"""
        self.logger.info(f"{self.widget_type} process started with PID: {pid}")
    
    def _on_process_stopped(self, pid: int):
        """进程停止事件处理"""
        self.logger.info(f"{self.widget_type} process stopped, PID: {pid}")
    
    def get_status_info(self) -> dict:
        """获取状态信息"""
        return {
            'widget_type': self.widget_type,
            'is_running': self.is_running(),
            'pid': self.process_manager.get_running_pid(),
            'daemon_script': str(self.process_manager.daemon_script),
            'pid_file': str(self.process_manager.pid_file)
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            self.status_timer.stop()
            self.process_manager.cleanup()
            self.communication_manager.cleanup_files()
        except Exception as e:
            self.logger.warning(f"Error during cleanup: {e}")