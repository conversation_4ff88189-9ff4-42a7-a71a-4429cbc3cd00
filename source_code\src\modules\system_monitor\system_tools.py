"""
System Tools Integration for Personal Manager System

This module provides integration with Windows system tools and utilities.
"""

import subprocess
import os
import sys
from typing import Dict, List, Optional
from PyQt6.QtWidgets import QMessageBox, QWidget
from core.logger import LoggerMixin


class SystemToolsManager(LoggerMixin):
    """Manager for Windows system tools integration"""
    
    def __init__(self):
        super().__init__()
        self.tools = {
            'task_manager': {
                'name': '任务管理器',
                'command': 'taskmgr.exe',
                'description': '查看和管理运行中的进程'
            },
            'performance_monitor': {
                'name': '性能监视器',
                'command': 'perfmon.exe',
                'description': '监视系统性能和资源使用情况'
            },
            'resource_monitor': {
                'name': '资源监视器',
                'command': 'resmon.exe',
                'description': '详细的系统资源监视'
            },
            'system_info': {
                'name': '系统信息',
                'command': 'msinfo32.exe',
                'description': '显示详细的系统配置信息'
            },
            'device_manager': {
                'name': '设备管理器',
                'command': 'devmgmt.msc',
                'description': '管理系统硬件设备'
            },
            'disk_management': {
                'name': '磁盘管理',
                'command': 'diskmgmt.msc',
                'description': '管理磁盘分区和存储'
            },
            'event_viewer': {
                'name': '事件查看器',
                'command': 'eventvwr.msc',
                'description': '查看系统和应用程序日志'
            },
            'services': {
                'name': '服务管理',
                'command': 'services.msc',
                'description': '管理Windows服务'
            },
            'registry_editor': {
                'name': '注册表编辑器',
                'command': 'regedit.exe',
                'description': '编辑Windows注册表'
            },
            'system_configuration': {
                'name': '系统配置',
                'command': 'msconfig.exe',
                'description': '配置系统启动和服务'
            },
            'control_panel': {
                'name': '控制面板',
                'command': 'control.exe',
                'description': '系统设置和配置'
            },
            'computer_management': {
                'name': '计算机管理',
                'command': 'compmgmt.msc',
                'description': '综合系统管理工具'
            }
        }
    
    def launch_tool(self, tool_key: str, parent_widget: Optional[QWidget] = None) -> bool:
        """
        Launch a system tool
        
        Args:
            tool_key: Key of the tool to launch
            parent_widget: Parent widget for error dialogs
            
        Returns:
            True if successful, False otherwise
        """
        if tool_key not in self.tools:
            self.logger.error(f"Unknown tool: {tool_key}")
            return False
        
        tool = self.tools[tool_key]
        
        try:
            # Special handling for MSC files
            if tool['command'].endswith('.msc'):
                subprocess.Popen(['mmc.exe', tool['command']])
            else:
                subprocess.Popen([tool['command']])
            
            self.logger.info(f"Launched {tool['name']} ({tool['command']})")
            return True
            
        except FileNotFoundError:
            error_msg = f"工具 {tool['name']} 未找到"
            self.logger.error(f"Tool not found: {tool['command']}")
            if parent_widget:
                QMessageBox.warning(parent_widget, "错误", error_msg)
            return False
            
        except Exception as e:
            error_msg = f"无法启动 {tool['name']}: {e}"
            self.logger.error(f"Failed to launch {tool['name']}: {e}")
            if parent_widget:
                QMessageBox.warning(parent_widget, "错误", error_msg)
            return False
    
    def get_available_tools(self) -> Dict[str, Dict[str, str]]:
        """Get list of available tools"""
        return self.tools.copy()
    
    def is_tool_available(self, tool_key: str) -> bool:
        """Check if a tool is available on the system"""
        if tool_key not in self.tools:
            return False
        
        tool = self.tools[tool_key]
        command = tool['command']
        
        try:
            # For MSC files, check if mmc.exe exists
            if command.endswith('.msc'):
                subprocess.run(['where', 'mmc.exe'], 
                             capture_output=True, check=True)
            else:
                subprocess.run(['where', command], 
                             capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def launch_task_manager(self, parent_widget: Optional[QWidget] = None) -> bool:
        """Quick access to Task Manager"""
        return self.launch_tool('task_manager', parent_widget)
    
    def launch_performance_monitor(self, parent_widget: Optional[QWidget] = None) -> bool:
        """Quick access to Performance Monitor"""
        return self.launch_tool('performance_monitor', parent_widget)
    
    def launch_resource_monitor(self, parent_widget: Optional[QWidget] = None) -> bool:
        """Quick access to Resource Monitor"""
        return self.launch_tool('resource_monitor', parent_widget)
    
    def launch_system_info(self, parent_widget: Optional[QWidget] = None) -> bool:
        """Quick access to System Information"""
        return self.launch_tool('system_info', parent_widget)
    
    def open_system_folder(self, folder_type: str, parent_widget: Optional[QWidget] = None) -> bool:
        """
        Open system folders
        
        Args:
            folder_type: Type of folder ('temp', 'system32', 'program_files', etc.)
            parent_widget: Parent widget for error dialogs
        """
        folders = {
            'temp': os.environ.get('TEMP', 'C:\\Windows\\Temp'),
            'system32': 'C:\\Windows\\System32',
            'program_files': 'C:\\Program Files',
            'program_files_x86': 'C:\\Program Files (x86)',
            'windows': 'C:\\Windows',
            'users': 'C:\\Users',
            'startup': os.path.join(os.environ.get('APPDATA', ''), 
                                  'Microsoft\\Windows\\Start Menu\\Programs\\Startup')
        }
        
        if folder_type not in folders:
            self.logger.error(f"Unknown folder type: {folder_type}")
            return False
        
        folder_path = folders[folder_type]
        
        try:
            subprocess.Popen(['explorer.exe', folder_path])
            self.logger.info(f"Opened folder: {folder_path}")
            return True
        except Exception as e:
            error_msg = f"无法打开文件夹 {folder_path}: {e}"
            self.logger.error(error_msg)
            if parent_widget:
                QMessageBox.warning(parent_widget, "错误", error_msg)
            return False
    
    def run_command_prompt(self, as_admin: bool = False, parent_widget: Optional[QWidget] = None) -> bool:
        """
        Open Command Prompt
        
        Args:
            as_admin: Whether to run as administrator
            parent_widget: Parent widget for error dialogs
        """
        try:
            if as_admin:
                # Run as administrator using PowerShell
                subprocess.Popen([
                    'powershell.exe', 
                    'Start-Process', 'cmd.exe', '-Verb', 'RunAs'
                ])
            else:
                subprocess.Popen(['cmd.exe'])
            
            self.logger.info(f"Opened Command Prompt (admin: {as_admin})")
            return True
        except Exception as e:
            error_msg = f"无法打开命令提示符: {e}"
            self.logger.error(error_msg)
            if parent_widget:
                QMessageBox.warning(parent_widget, "错误", error_msg)
            return False
    
    def run_powershell(self, as_admin: bool = False, parent_widget: Optional[QWidget] = None) -> bool:
        """
        Open PowerShell
        
        Args:
            as_admin: Whether to run as administrator
            parent_widget: Parent widget for error dialogs
        """
        try:
            if as_admin:
                subprocess.Popen([
                    'powershell.exe', 
                    'Start-Process', 'powershell.exe', '-Verb', 'RunAs'
                ])
            else:
                subprocess.Popen(['powershell.exe'])
            
            self.logger.info(f"Opened PowerShell (admin: {as_admin})")
            return True
        except Exception as e:
            error_msg = f"无法打开PowerShell: {e}"
            self.logger.error(error_msg)
            if parent_widget:
                QMessageBox.warning(parent_widget, "错误", error_msg)
            return False
