"""
调试关闭事件和状态文件创建
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件
from modules.study_tracker.study_floating_widget import StudyFloatingWidget


def debug_close_event():
    """调试关闭事件"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        print("\n🔍 调试关闭事件和状态文件创建")
        print("=" * 50)
        
        # 1. 检查路径计算
        print("1. 检查路径计算...")
        widget_file_path = Path(__file__).parent / "src" / "modules" / "study_tracker" / "study_floating_widget.py"
        print(f"   悬浮小组件文件路径: {widget_file_path}")
        print(f"   文件存在: {widget_file_path.exists()}")
        
        # 计算状态文件路径（模拟悬浮小组件中的计算）
        status_file_path = widget_file_path.parent.parent.parent.parent.parent / "study_floating_status.txt"
        print(f"   计算的状态文件路径: {status_file_path}")
        print(f"   状态文件父目录: {status_file_path.parent}")
        print(f"   父目录存在: {status_file_path.parent.exists()}")
        
        # 2. 手动测试状态文件创建
        print("\n2. 手动测试状态文件创建...")
        try:
            # 确保父目录存在
            status_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(status_file_path, 'w', encoding='utf-8') as f:
                from datetime import datetime
                content = f"closed:{datetime.now().isoformat()}"
                f.write(content)
                f.flush()
            
            print(f"   ✅ 状态文件创建成功: {status_file_path}")
            
            # 读取验证
            with open(status_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            print(f"   文件内容: {content}")
            
        except Exception as e:
            print(f"   ❌ 状态文件创建失败: {e}")
        
        # 3. 创建悬浮小组件并测试关闭
        print("\n3. 创建悬浮小组件并测试关闭...")
        
        # 重写closeEvent来添加调试信息
        original_close_event = StudyFloatingWidget.closeEvent
        
        def debug_close_event(self, event):
            print("   🔍 closeEvent 被调用")
            try:
                print("   🔍 调用 notify_main_app_closed...")
                self.notify_main_app_closed()
                print("   ✅ notify_main_app_closed 调用完成")
            except Exception as e:
                print(f"   ❌ notify_main_app_closed 失败: {e}")
                import traceback
                traceback.print_exc()
            
            print("   🔍 接受关闭事件")
            event.accept()
        
        # 临时替换closeEvent
        StudyFloatingWidget.closeEvent = debug_close_event
        
        widget = StudyFloatingWidget()
        widget.show()
        print("   ✅ 悬浮小组件已创建并显示")
        
        # 等待一下然后关闭
        import time
        time.sleep(1)
        
        print("   🔒 关闭悬浮小组件...")
        widget.close()
        
        # 检查状态文件是否被创建
        time.sleep(1)
        if status_file_path.exists():
            with open(status_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            print(f"   ✅ 状态文件已创建: {content}")
        else:
            print("   ❌ 状态文件未创建")
        
        # 恢复原始方法
        StudyFloatingWidget.closeEvent = original_close_event
        
        app.quit()
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        app.quit()


if __name__ == "__main__":
    debug_close_event()
