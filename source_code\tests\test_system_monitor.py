#!/usr/bin/env python3
"""
Test script for the new System Monitor Widget

This script tests the Windows Task Manager-like interface functionality.
"""

import sys
import os

# Add the src directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

from PyQt6.QtWidgets import QApplication
from modules.system_monitor.system_monitor_widget import SystemMonitorWidget
from core.logger import setup_logging

def test_system_monitor():
    """Test the system monitor widget"""
    
    # Setup logging
    setup_logging()
    
    # Create application
    app = QApplication(sys.argv)
    
    # Create and show system monitor widget
    widget = SystemMonitorWidget()
    widget.setWindowTitle("System Monitor Test - Windows Task Manager Style")
    widget.resize(1200, 800)
    widget.show()
    
    print("System Monitor Widget Test Started")
    print("Features to test:")
    print("1. Performance tab with real-time charts (CPU, Memory, Disk, Network)")
    print("2. Processes tab with detailed process list")
    print("3. Search and filter functionality in processes")
    print("4. System tools toolbar (Task Manager, Performance Monitor, etc.)")
    print("5. Real-time data updates every 2 seconds")
    print("6. Sorting by clicking column headers")
    print("\nClose the window to exit the test.")
    
    # Run the application
    sys.exit(app.exec())

if __name__ == "__main__":
    test_system_monitor()
