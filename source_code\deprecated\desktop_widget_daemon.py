#!/usr/bin/env python3
"""
桌面小部件守护进程

确保桌面小部件持续运行，自动重启崩溃的进程
"""

import sys
import os
import time
import subprocess
import threading
import signal
import argparse
from pathlib import Path
from datetime import datetime, timedelta
import psutil
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from src.core.logger import setup_logging, get_logger


class DesktopWidgetDaemon:
    """桌面小部件守护进程"""
    
    def __init__(self, manual_start=False):
        self.project_root = Path(__file__).parent
        self.widget_script = self.project_root / "start_desktop_widget.py"
        self.config_file = self.project_root / "daemon_config.json"
        self.pid_file = self.project_root / "daemon.pid"
        self.log_file = self.project_root / "daemon.log"
        self.command_file = self.project_root / "daemon_command.txt"  # 新增：命令文件

        # 运行状态
        self.running = True
        self.widget_process = None
        self.restart_count = 0
        self.last_restart_time = None
        self.manual_start = manual_start  # 标识是否为手动启动

        # 配置参数
        self.config = self.load_config()
        
        # 设置日志
        setup_logging(
            level="INFO",
            log_file=str(self.log_file),
            max_size=1024*1024,  # 1MB
            backup_count=3
        )
        self.logger = get_logger(__name__)
        
        # 注册信号处理
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)
    
    def load_config(self):
        """加载配置"""
        default_config = {
            "restart_delay": 5,  # 重启延迟（秒）
            "max_restart_per_hour": 10,  # 每小时最大重启次数
            "check_interval": 10,  # 检查间隔（秒）
            "auto_start_delay": 30,  # 开机启动延迟（秒）
            "enable_crash_detection": True,  # 启用崩溃检测
            "enable_memory_monitor": True,  # 启用内存监控
            "max_memory_mb": 500,  # 最大内存使用（MB）
        }
        
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    default_config.update(config)
            
            # 保存配置
            self.save_config(default_config)
            return default_config
            
        except Exception as e:
            print(f"加载配置失败，使用默认配置: {e}")
            return default_config
    
    def save_config(self, config=None):
        """保存配置"""
        try:
            config = config or self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
    
    def write_pid(self):
        """写入PID文件"""
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(os.getpid()))
        except Exception as e:
            self.logger.error(f"写入PID文件失败: {e}")
    
    def remove_pid(self):
        """删除PID文件"""
        try:
            if self.pid_file.exists():
                self.pid_file.unlink()
        except Exception as e:
            self.logger.error(f"删除PID文件失败: {e}")
    
    def is_already_running(self):
        """检查是否已有守护进程在运行"""
        try:
            if not self.pid_file.exists():
                return False
            
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # 检查进程是否存在
            return psutil.pid_exists(pid)
            
        except Exception:
            return False
    
    def start_widget(self):
        """启动桌面小部件"""
        try:
            if self.widget_process and self.widget_process.poll() is None:
                self.logger.warning("桌面小部件已在运行")
                return True

            self.logger.info("启动桌面小部件...")

            # 启动小部件进程
            # 设置环境变量确保UTF-8编码
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSFSENCODING'] = '0'
            if os.name == 'nt':
                env['PYTHONUTF8'] = '1'

            self.widget_process = subprocess.Popen(
                [sys.executable, str(self.widget_script)],
                cwd=str(self.project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            # 等待一下确保启动成功
            time.sleep(2)

            poll_result = self.widget_process.poll()

            if poll_result is None:
                self.logger.info(f"桌面小部件启动成功，PID: {self.widget_process.pid}")
                return True
            else:
                self.logger.error(f"桌面小部件启动失败，退出码: {poll_result}")

                # 读取错误输出
                try:
                    stdout, stderr = self.widget_process.communicate(timeout=1)
                    if stderr:
                        self.logger.error(f"小组件错误输出: {stderr.decode('utf-8', errors='ignore')}")
                except:
                    pass

                return False

        except Exception as e:
            self.logger.error(f"启动桌面小部件失败: {e}")
            return False
    
    def stop_widget(self):
        """停止桌面小部件"""
        try:
            if self.widget_process and self.widget_process.poll() is None:
                self.logger.info("停止桌面小部件...")
                self.widget_process.terminate()
                
                # 等待进程结束
                try:
                    self.widget_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    self.logger.warning("强制终止桌面小部件")
                    self.widget_process.kill()
                
                self.widget_process = None
                self.logger.info("桌面小部件已停止")
                
        except Exception as e:
            self.logger.error(f"停止桌面小部件失败: {e}")
    
    def check_widget_status(self):
        """检查桌面小部件状态"""
        try:
            if not self.widget_process:
                return False
            
            # 检查进程是否还在运行
            if self.widget_process.poll() is not None:
                return False
            
            # 检查内存使用
            if self.config.get("enable_memory_monitor", True):
                try:
                    process = psutil.Process(self.widget_process.pid)
                    memory_mb = process.memory_info().rss / 1024 / 1024
                    max_memory = self.config.get("max_memory_mb", 500)
                    
                    if memory_mb > max_memory:
                        self.logger.warning(f"桌面小部件内存使用过高: {memory_mb:.1f}MB > {max_memory}MB")
                        return False
                        
                except psutil.NoSuchProcess:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查桌面小部件状态失败: {e}")
            return False
    
    def should_restart(self):
        """判断是否应该重启"""
        now = datetime.now()
        
        # 检查重启频率限制
        if self.last_restart_time:
            time_since_last = now - self.last_restart_time
            if time_since_last < timedelta(hours=1):
                if self.restart_count >= self.config.get("max_restart_per_hour", 10):
                    self.logger.warning("重启次数过多，暂停重启")
                    return False
            else:
                # 重置计数器
                self.restart_count = 0
        
        return True
    
    def restart_widget(self):
        """重启桌面小部件"""
        if not self.should_restart():
            return False
        
        self.logger.info("重启桌面小部件...")
        
        # 停止当前进程
        self.stop_widget()
        
        # 等待重启延迟
        restart_delay = self.config.get("restart_delay", 5)
        time.sleep(restart_delay)
        
        # 启动新进程
        if self.start_widget():
            self.restart_count += 1
            self.last_restart_time = datetime.now()
            self.logger.info(f"桌面小部件重启成功（第{self.restart_count}次）")
            return True
        else:
            self.logger.error("桌面小部件重启失败")
            return False
    
    def run(self):
        """运行守护进程"""
        self.logger.info("桌面小部件守护进程启动")
        
        # 写入PID文件
        self.write_pid()
        
        # 开机启动延迟（仅在自动启动时使用）
        # 手动启动时不需要延迟
        if self.manual_start:
            self.logger.info("手动启动，立即启动桌面小部件...")
        else:
            auto_start_delay = self.config.get("auto_start_delay", 30)
            # 检查是否是开机自动启动（通过检查系统启动时间）
            boot_time = psutil.boot_time()
            current_time = time.time()
            time_since_boot = current_time - boot_time

            # 如果系统启动时间小于5分钟，认为是开机自动启动
            if time_since_boot < 300 and auto_start_delay > 0:
                self.logger.info(f"检测到开机启动，等待{auto_start_delay}秒后启动桌面小部件...")
                time.sleep(auto_start_delay)
            else:
                self.logger.info("自动启动，但系统已运行较长时间，立即启动桌面小部件...")
        
        # 启动桌面小部件
        if not self.start_widget():
            self.logger.error("初始启动失败，但继续运行以监听命令")
            # 不要返回，继续运行以监听手动接管命令
        
        # 主循环
        check_interval = self.config.get("check_interval", 10)

        while self.running:
            try:
                # 检查命令文件
                self.check_command_file()

                # 检查小部件状态
                if not self.check_widget_status():
                    self.logger.warning("检测到桌面小部件异常")

                    if self.config.get("enable_crash_detection", True):
                        self.restart_widget()

                # 等待下次检查
                time.sleep(check_interval)

            except KeyboardInterrupt:
                self.logger.info("收到中断信号")
                break
            except Exception as e:
                self.logger.error(f"守护进程异常: {e}")
                time.sleep(check_interval)
        
        # 清理
        self.cleanup()
    
    def check_command_file(self):
        """检查命令文件是否有新的指令"""
        try:
            if self.command_file.exists():
                with open(self.command_file, 'r', encoding='utf-8') as f:
                    command = f.read().strip()

                # 删除命令文件
                self.command_file.unlink()

                if command == "show_widget":
                    self.logger.info("收到显示小组件命令")
                    self.show_widget_window()
                elif command == "restart_widget":
                    self.logger.info("收到重启小组件命令")
                    self.restart_widget()
                elif command == "manual_takeover":
                    self.logger.info("收到手动接管命令，停止自启动流程并重启")
                    self.manual_takeover()

        except Exception as e:
            self.logger.error(f"检查命令文件失败: {e}")

    def manual_takeover(self):
        """手动接管：停止自启动流程并重新启动小组件"""
        try:
            self.logger.info("开始手动接管流程...")

            # 停止当前小组件进程
            if self.widget_process and self.widget_process.poll() is None:
                self.logger.info("停止当前小组件进程")
                try:
                    self.widget_process.terminate()
                    self.widget_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.widget_process.kill()
                    self.widget_process.wait()
                except Exception as e:
                    self.logger.error(f"停止小组件进程失败: {e}")
                finally:
                    self.widget_process = None

            # 标记为手动模式
            self.is_manual = True
            self.logger.info("切换到手动模式")

            # 立即重新启动小组件
            self.logger.info("手动模式下重新启动小组件...")
            self.start_widget()

        except Exception as e:
            self.logger.error(f"手动接管失败: {e}")

    def show_widget_window(self):
        """显示小组件窗口"""
        try:
            if self.widget_process and self.widget_process.poll() is None:
                # 小组件进程正在运行，尝试通过信号让它显示
                self.logger.info("尝试显示已运行的小组件窗口")

                # 创建显示命令文件，让小组件进程读取
                widget_command_file = self.project_root / "widget_show_command.txt"
                with open(widget_command_file, 'w', encoding='utf-8') as f:
                    f.write("show")

                self.logger.info("已发送显示命令给小组件")
            else:
                # 小组件进程未运行，重新启动
                self.logger.info("小组件未运行，重新启动")
                self.start_widget()

        except Exception as e:
            self.logger.error(f"显示小组件窗口失败: {e}")

    def cleanup(self):
        """清理资源"""
        self.logger.info("清理守护进程资源...")

        # 停止桌面小部件
        self.stop_widget()

        # 删除PID文件
        self.remove_pid()

        # 删除命令文件
        try:
            if self.command_file.exists():
                self.command_file.unlink()
        except Exception as e:
            self.logger.error(f"删除命令文件失败: {e}")

        self.logger.info("桌面小部件守护进程已退出")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}")
        self.running = False


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='桌面小部件守护进程')
    parser.add_argument('--manual', action='store_true',
                       help='标识为手动启动，跳过开机启动延迟')
    args = parser.parse_args()

    daemon = DesktopWidgetDaemon(manual_start=args.manual)

    # 检查是否已有实例在运行
    if daemon.is_already_running():
        print("守护进程已在运行")
        return 1

    try:
        daemon.run()
        return 0
    except Exception as e:
        print(f"守护进程运行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
