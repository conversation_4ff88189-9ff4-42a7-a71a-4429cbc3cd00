#!/usr/bin/env python3
"""
Create application icon for Personal Manager
"""

from PIL import Image, ImageDraw, ImageFont
import os
from pathlib import Path

def create_app_icon():
    """Create a simple application icon"""
    # Create icon directory if it doesn't exist
    icon_dir = Path(__file__).parent / "resources" / "icons"
    icon_dir.mkdir(parents=True, exist_ok=True)
    
    # Create a 256x256 icon
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw background circle
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(52, 152, 219, 255), outline=(41, 128, 185, 255), width=4)
    
    # Draw inner elements - folder icon
    folder_size = size // 3
    folder_x = (size - folder_size) // 2
    folder_y = (size - folder_size) // 2 - 10
    
    # Folder body
    draw.rectangle([folder_x, folder_y + 15, folder_x + folder_size, folder_y + folder_size], 
                  fill=(255, 255, 255, 255), outline=(200, 200, 200, 255), width=2)
    
    # Folder tab
    tab_width = folder_size // 3
    draw.rectangle([folder_x, folder_y, folder_x + tab_width, folder_y + 15], 
                  fill=(255, 255, 255, 255), outline=(200, 200, 200, 255), width=2)
    
    # Add "PM" text
    try:
        # Try to use a system font
        font_size = 24
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
    
    text = "PM"
    text_bbox = draw.textbbox((0, 0), text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    text_x = (size - text_width) // 2
    text_y = folder_y + folder_size + 20
    
    draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)
    
    # Save as ICO file
    ico_path = icon_dir / "app.ico"
    img.save(ico_path, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
    
    # Also save as PNG for other uses
    png_path = icon_dir / "app.png"
    img.save(png_path, format='PNG')
    
    print(f"Icon created successfully:")
    print(f"  ICO: {ico_path}")
    print(f"  PNG: {png_path}")
    
    return ico_path, png_path

if __name__ == "__main__":
    create_app_icon()
