#!/usr/bin/env python3
"""
测试桌面小部件关闭行为
验证关闭窗口时是否直接退出而不是隐藏到托盘
"""

import sys
import os
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from PyQt6.QtWidgets import QApplication
from src.modules.task_manager.desktop_widget import DesktopTaskWidget

def test_close_behavior():
    """测试关闭行为"""
    print("=" * 60)
    print("桌面小部件关闭行为测试")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    print("创建桌面小部件...")
    widget = DesktopTaskWidget()
    
    print("显示桌面小部件...")
    widget.show()
    
    print("桌面小部件已显示")
    print("请观察桌面右上角的小部件")
    print("然后点击小部件右上角的 'X' 关闭按钮")
    print("观察是否直接关闭应用而不是隐藏到系统托盘")
    print("如果应用直接关闭，说明修改成功！")
    print("如果应用隐藏到托盘，说明还有问题需要修复")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_close_behavior()
