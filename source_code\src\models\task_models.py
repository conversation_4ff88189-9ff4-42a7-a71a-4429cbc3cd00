"""
任务管理相关数据模型

包含任务、任务分类、任务提醒等模型
"""

from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, ForeignKey, Index, Enum
from sqlalchemy.orm import relationship, Session
from typing import List, Optional
from datetime import datetime, timedelta
import enum

from .base import BaseModel


class TaskStatus(enum.Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 待处理
    IN_PROGRESS = "in_progress"  # 进行中
    COMPLETED = "completed"  # 已完成
    CANCELLED = "cancelled"  # 已取消
    PAUSED = "paused"       # 已暂停


class TaskPriority(enum.Enum):
    """任务优先级枚举"""
    LOW = "low"         # 低
    NORMAL = "normal"   # 普通
    HIGH = "high"       # 高
    URGENT = "urgent"   # 紧急


class TaskCategory(BaseModel):
    """任务分类模型"""
    
    __tablename__ = 'task_categories'
    
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    color = Column(String(7), nullable=True)  # 十六进制颜色值
    icon = Column(String(50), nullable=True)
    sort_order = Column(Integer, default=0)
    
    # 关联关系
    tasks = relationship("Task", back_populates="category")
    
    @classmethod
    def get_by_name(cls, session: Session, name: str) -> Optional['TaskCategory']:
        """根据名称获取分类"""
        return session.query(cls).filter(
            cls.name == name,
            cls.is_deleted == False
        ).first()
    
    @classmethod
    def get_sorted_categories(cls, session: Session) -> List['TaskCategory']:
        """获取排序后的分类列表"""
        return session.query(cls).filter(
            cls.is_deleted == False
        ).order_by(cls.sort_order, cls.name).all()


class Task(BaseModel):
    """任务模型"""
    
    __tablename__ = 'tasks'
    
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, nullable=False)
    priority = Column(Enum(TaskPriority), default=TaskPriority.NORMAL, nullable=False)
    
    # 时间相关
    due_date = Column(DateTime, nullable=True)
    start_date = Column(DateTime, nullable=True)
    completed_date = Column(DateTime, nullable=True)
    estimated_hours = Column(Integer, nullable=True)  # 预估工时（分钟）
    actual_hours = Column(Integer, nullable=True)     # 实际工时（分钟）
    
    # 分类和标签
    category_id = Column(String(36), ForeignKey('task_categories.id'), nullable=True)
    tags = Column(String(500), nullable=True)  # 逗号分隔的标签
    
    # 进度和完成度
    progress = Column(Integer, default=0)  # 进度百分比 0-100
    
    # 关联关系
    category = relationship("TaskCategory", back_populates="tasks")
    reminders = relationship("TaskReminder", back_populates="task")
    subtasks = relationship("Task", backref="parent_task", remote_side="Task.id")
    parent_id = Column(String(36), ForeignKey('tasks.id'), nullable=True)
    
    # 索引
    __table_args__ = (
        Index('idx_tasks_status', 'status'),
        Index('idx_tasks_priority', 'priority'),
        Index('idx_tasks_due_date', 'due_date'),
        Index('idx_tasks_category', 'category_id'),
    )
    
    def mark_completed(self):
        """标记任务为已完成"""
        self.status = TaskStatus.COMPLETED
        self.completed_date = datetime.utcnow()
        self.progress = 100
    
    def mark_cancelled(self):
        """标记任务为已取消"""
        self.status = TaskStatus.CANCELLED
    
    def start_task(self):
        """开始任务"""
        if self.status == TaskStatus.PENDING:
            self.status = TaskStatus.IN_PROGRESS
            if not self.start_date:
                self.start_date = datetime.utcnow()
    
    def pause_task(self):
        """暂停任务"""
        if self.status == TaskStatus.IN_PROGRESS:
            self.status = TaskStatus.PAUSED
    
    def resume_task(self):
        """恢复任务"""
        if self.status == TaskStatus.PAUSED:
            self.status = TaskStatus.IN_PROGRESS
    
    @property
    def is_overdue(self) -> bool:
        """检查任务是否过期"""
        if not self.due_date or self.status in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]:
            return False
        return datetime.utcnow() > self.due_date
    
    @property
    def days_until_due(self) -> Optional[int]:
        """距离截止日期的天数"""
        if not self.due_date:
            return None
        delta = self.due_date - datetime.utcnow()
        return delta.days
    
    @property
    def tag_list(self) -> List[str]:
        """获取标签列表"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
    
    @tag_list.setter
    def tag_list(self, tags: List[str]):
        """设置标签列表"""
        self.tags = ','.join(tags) if tags else None
    
    @classmethod
    def get_by_status(cls, session: Session, status: TaskStatus) -> List['Task']:
        """根据状态获取任务"""
        return session.query(cls).filter(
            cls.status == status,
            cls.is_deleted == False
        ).all()
    
    @classmethod
    def get_overdue_tasks(cls, session: Session) -> List['Task']:
        """获取过期任务"""
        return session.query(cls).filter(
            cls.due_date < datetime.utcnow(),
            cls.status.in_([TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.PAUSED]),
            cls.is_deleted == False
        ).all()
    
    @classmethod
    def get_due_soon_tasks(cls, session: Session, days: int = 7) -> List['Task']:
        """获取即将到期的任务"""
        due_date = datetime.utcnow() + timedelta(days=days)
        return session.query(cls).filter(
            cls.due_date <= due_date,
            cls.due_date >= datetime.utcnow(),
            cls.status.in_([TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.PAUSED]),
            cls.is_deleted == False
        ).all()
    
    @classmethod
    def get_by_priority(cls, session: Session, priority: TaskPriority) -> List['Task']:
        """根据优先级获取任务"""
        return session.query(cls).filter(
            cls.priority == priority,
            cls.is_deleted == False
        ).all()
    
    @classmethod
    def get_by_category(cls, session: Session, category_id: str) -> List['Task']:
        """根据分类获取任务"""
        return session.query(cls).filter(
            cls.category_id == category_id,
            cls.is_deleted == False
        ).all()
    
    @classmethod
    def search_tasks(cls, session: Session, query: str) -> List['Task']:
        """搜索任务"""
        search_pattern = f"%{query}%"
        return session.query(cls).filter(
            (cls.title.like(search_pattern) | cls.description.like(search_pattern)),
            cls.is_deleted == False
        ).all()


class TaskReminder(BaseModel):
    """任务提醒模型"""
    
    __tablename__ = 'task_reminders'
    
    task_id = Column(String(36), ForeignKey('tasks.id'), nullable=False)
    reminder_time = Column(DateTime, nullable=False)
    message = Column(Text, nullable=True)
    is_sent = Column(Boolean, default=False)
    reminder_type = Column(String(50), default='notification')  # notification, email, sound
    
    # 关联关系
    task = relationship("Task", back_populates="reminders")
    
    # 索引
    __table_args__ = (
        Index('idx_task_reminders_time', 'reminder_time'),
        Index('idx_task_reminders_sent', 'is_sent'),
        Index('idx_task_reminders_task', 'task_id'),
    )
    
    @classmethod
    def get_pending_reminders(cls, session: Session) -> List['TaskReminder']:
        """获取待发送的提醒"""
        return session.query(cls).filter(
            cls.reminder_time <= datetime.utcnow(),
            cls.is_sent == False,
            cls.is_deleted == False
        ).all()
    
    @classmethod
    def mark_sent(cls, session: Session, reminder_id: str):
        """标记提醒为已发送"""
        reminder = cls.get_by_id(session, reminder_id)
        if reminder:
            reminder.is_sent = True
            session.add(reminder)
            session.flush()


class TaskTemplate(BaseModel):
    """任务模板模型"""
    
    __tablename__ = 'task_templates'
    
    name = Column(String(255), nullable=False)
    title_template = Column(String(255), nullable=False)
    description_template = Column(Text, nullable=True)
    default_priority = Column(Enum(TaskPriority), default=TaskPriority.NORMAL)
    default_estimated_hours = Column(Integer, nullable=True)
    default_category_id = Column(String(36), ForeignKey('task_categories.id'), nullable=True)
    default_tags = Column(String(500), nullable=True)
    
    # 关联关系
    default_category = relationship("TaskCategory")
    
    @classmethod
    def create_task_from_template(cls, session: Session, template_id: str, **kwargs) -> Optional['Task']:
        """从模板创建任务"""
        template = cls.get_by_id(session, template_id)
        if not template:
            return None
        
        # 使用模板的默认值，但允许覆盖
        task_data = {
            'title': template.title_template,
            'description': template.description_template,
            'priority': template.default_priority,
            'estimated_hours': template.default_estimated_hours,
            'category_id': template.default_category_id,
            'tags': template.default_tags
        }
        
        # 用传入的参数覆盖默认值
        task_data.update(kwargs)
        
        task = Task(**task_data)
        session.add(task)
        session.flush()
        return task


class TaskComment(BaseModel):
    """任务评论模型"""

    __tablename__ = 'task_comments'

    task_id = Column(String(36), ForeignKey('tasks.id'), nullable=False)
    content = Column(Text, nullable=False)
    author = Column(String(100), nullable=True)  # 评论作者

    # 关联关系
    task = relationship("Task")

    # 索引
    __table_args__ = (
        Index('idx_task_comments_task', 'task_id'),
        Index('idx_task_comments_created', 'created_at'),
    )

    @classmethod
    def get_task_comments(cls, session: Session, task_id: str) -> List['TaskComment']:
        """获取任务的所有评论"""
        return session.query(cls).filter(
            cls.task_id == task_id,
            cls.is_deleted == False
        ).order_by(cls.created_at.desc()).all()


class DailySummary(BaseModel):
    """日总结模型"""

    __tablename__ = 'daily_summaries'

    summary_date = Column(DateTime, nullable=False)  # 总结日期
    content = Column(Text, nullable=True)  # 总结内容

    # 索引
    __table_args__ = (
        Index('idx_daily_summaries_date', 'summary_date'),
    )

    @classmethod
    def get_by_date(cls, session: Session, summary_date: datetime) -> Optional['DailySummary']:
        """根据日期获取日总结"""
        from datetime import date
        if isinstance(summary_date, date):
            # 如果传入的是date对象，转换为datetime
            summary_date = datetime.combine(summary_date, datetime.min.time())

        # 查找当天的总结（忽略时间部分）
        start_of_day = summary_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = summary_date.replace(hour=23, minute=59, second=59, microsecond=999999)

        return session.query(cls).filter(
            cls.summary_date >= start_of_day,
            cls.summary_date <= end_of_day,
            cls.is_deleted == False
        ).first()

    @classmethod
    def create_or_update(cls, session: Session, summary_date: datetime, content: str) -> 'DailySummary':
        """创建或更新日总结"""
        from datetime import date
        if isinstance(summary_date, date):
            # 如果传入的是date对象，转换为datetime
            summary_date = datetime.combine(summary_date, datetime.min.time())

        # 查找是否已存在
        existing = cls.get_by_date(session, summary_date)

        if existing:
            # 更新现有记录
            existing.content = content
            existing.updated_at = datetime.utcnow()
            return existing
        else:
            # 创建新记录
            new_summary = cls(
                summary_date=summary_date.replace(hour=0, minute=0, second=0, microsecond=0),
                content=content
            )
            session.add(new_summary)
            session.flush()
            return new_summary


class WeeklySummary(BaseModel):
    """周总结模型"""

    __tablename__ = 'weekly_summaries'

    week_start_date = Column(DateTime, nullable=False)  # 周开始日期
    content = Column(Text, nullable=True)  # 总结内容

    # 索引
    __table_args__ = (
        Index('idx_weekly_summaries_week_start', 'week_start_date'),
    )

    @classmethod
    def get_by_week_start(cls, session: Session, week_start_date: datetime) -> Optional['WeeklySummary']:
        """根据周开始日期获取周总结"""
        from datetime import date
        if isinstance(week_start_date, date):
            # 如果传入的是date对象，转换为datetime
            week_start_date = datetime.combine(week_start_date, datetime.min.time())

        # 查找当周的总结（精确匹配周开始日期）
        start_of_week = week_start_date.replace(hour=0, minute=0, second=0, microsecond=0)

        return session.query(cls).filter(
            cls.week_start_date == start_of_week,
            cls.is_deleted == False
        ).first()

    @classmethod
    def create_or_update(cls, session: Session, week_start_date: datetime, content: str) -> 'WeeklySummary':
        """创建或更新周总结"""
        from datetime import date
        if isinstance(week_start_date, date):
            # 如果传入的是date对象，转换为datetime
            week_start_date = datetime.combine(week_start_date, datetime.min.time())

        # 查找是否已存在
        existing = cls.get_by_week_start(session, week_start_date)

        if existing:
            # 更新现有记录
            existing.content = content
            existing.updated_at = datetime.utcnow()
            return existing
        else:
            # 创建新记录
            new_summary = cls(
                week_start_date=week_start_date.replace(hour=0, minute=0, second=0, microsecond=0),
                content=content
            )
            session.add(new_summary)
            session.flush()
            return new_summary
