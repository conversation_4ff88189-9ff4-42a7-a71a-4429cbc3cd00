"""
Configuration Management System
"""

import os
import json
import yaml
from pathlib import Path
from typing import Any, Dict, Optional
from dotenv import load_dotenv


class ConfigManager:
    """Configuration manager for the application"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """Initialize configuration manager
        
        Args:
            config_dir: Custom configuration directory path
        """
        self.config_dir = Path(config_dir) if config_dir else self._get_default_config_dir()
        self.config_file = self.config_dir / "config.yaml"
        self.user_config_file = self.config_dir / "user_config.yaml"
        
        # Load environment variables
        load_dotenv()
        
        # Initialize configuration
        self._config = {}
        self._user_config = {}
        self._load_default_config()
        self._load_user_config()
    
    def _get_default_config_dir(self) -> Path:
        """Get default configuration directory"""
        if os.name == 'nt':  # Windows
            config_dir = Path(os.environ.get('APPDATA', '')) / "PersonalManager"
        else:  # Unix-like
            config_dir = Path.home() / ".config" / "personal_manager"
        
        config_dir.mkdir(parents=True, exist_ok=True)
        return config_dir
    
    def _load_default_config(self):
        """Load default configuration"""
        default_config = {
            'app': {
                'name': 'Personal Manager',
                'version': '1.0.0',
                'debug': False,
                'auto_save_interval': 300,  # seconds
            },
            'database': {
                'url': f"sqlite:///{self.config_dir}/personal_manager.db",
                'echo': False,
                'pool_size': 5,
                'max_overflow': 10,
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file': str(self.config_dir / "logs" / "app.log"),
                'max_size': 10485760,  # 10MB
                'backup_count': 5,
            },
            'gui': {
                'theme': 'default',
                'window_size': [1200, 800],
                'window_position': [100, 100],
                'remember_geometry': True,
            },
            'file_manager': {
                'default_path': str(Path.home()),
                'show_hidden': False,
                'auto_refresh': True,
                'thumbnail_size': 128,
            },
            'system_monitor': {
                'update_interval': 2,  # seconds
                'history_length': 100,
                'alerts_enabled': True,
            },
            'backup': {
                'default_location': str(self.config_dir / "backups"),
                'compression': True,
                'encryption': False,
                'schedule': 'daily',
            },
            'security': {
                'password_length': 12,
                'require_special_chars': True,
                'session_timeout': 3600,  # seconds
            },
            'study_floating_widget': {
                'enabled': False,
            }
        }
        
        self._config = default_config
        
        # Save default config if it doesn't exist
        if not self.config_file.exists():
            self.save_config()
    
    def _load_user_config(self):
        """Load user-specific configuration"""
        if self.user_config_file.exists():
            try:
                with open(self.user_config_file, 'r', encoding='utf-8') as f:
                    self._user_config = yaml.safe_load(f) or {}
            except Exception as e:
                print(f"Error loading user config: {e}")
                self._user_config = {}
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation
        
        Args:
            key: Configuration key (e.g., 'database.url')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        # Check environment variables first
        env_key = key.upper().replace('.', '_')
        env_value = os.environ.get(f"PM_{env_key}")
        if env_value is not None:
            return env_value
        
        # Check user config
        value = self._get_nested_value(self._user_config, key)
        if value is not None:
            return value
        
        # Check default config
        value = self._get_nested_value(self._config, key)
        if value is not None:
            return value
        
        return default
    
    def set(self, key: str, value: Any, save: bool = True):
        """Set configuration value
        
        Args:
            key: Configuration key (e.g., 'database.url')
            value: Value to set
            save: Whether to save to file immediately
        """
        self._set_nested_value(self._user_config, key, value)
        if save:
            self.save_user_config()
    
    def _get_nested_value(self, config: Dict, key: str) -> Any:
        """Get nested dictionary value using dot notation"""
        keys = key.split('.')
        value = config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return None
        
        return value
    
    def _set_nested_value(self, config: Dict, key: str, value: Any):
        """Set nested dictionary value using dot notation"""
        keys = key.split('.')
        current = config
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
    
    def save_config(self):
        """Save default configuration to file"""
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)
    
    def save_user_config(self):
        """Save user configuration to file"""
        self.user_config_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.user_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self._user_config, f, default_flow_style=False, allow_unicode=True)
    
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        self._user_config = {}
        if self.user_config_file.exists():
            self.user_config_file.unlink()
    
    @property
    def config_directory(self) -> Path:
        """Get configuration directory path"""
        return self.config_dir

    def save(self):
        """Save configuration (alias for save_user_config)"""
        self.save_user_config()


# Alias for backward compatibility
Config = ConfigManager
