"""
学习时间追踪主界面

提供学习时间记录的完整用户界面，包括：
- 计时器控制
- 科目管理
- 学习记录查看
- 统计数据展示
"""

from datetime import datetime, date, timedelta
from typing import Optional, List

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QComboBox, QTableWidget, QTableWidgetItem,
    QTabWidget, QGroupBox, QLineEdit, QTextEdit, QSpinBox,
    QDateEdit, QMessageBox, QFileDialog, QProgressBar,
    QSplitter, QFrame, QScrollArea, QCheckBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QDate
from PyQt6.QtGui import QFont, QPalette, QColor

from core.logger import LoggerMixin
from .study_service import StudyService
from .study_stats_widget import StudyStatsWidget
from .study_floating_widget_controller import StudyFloatingWidgetController
from models.study_models import StudySubject, StudySession, StudyStatus


def qdate_to_python_date(qdate):
    """将QDate转换为Python date对象"""
    if hasattr(qdate, 'toPython'):
        return qdate.toPython()
    else:
        return date(qdate.year(), qdate.month(), qdate.day())


class StudyTrackerWidget(QWidget, LoggerMixin):
    """学习时间追踪主界面"""
    
    # 信号
    session_started = pyqtSignal(str)  # 会话开始
    session_completed = pyqtSignal(str)  # 会话完成
    
    def __init__(self, parent=None):
        super().__init__(parent)

        try:
            self.study_service = StudyService()

            # 初始化悬浮小组件控制器
            self.floating_widget_controller = StudyFloatingWidgetController(self)
            self.floating_widget_controller.status_changed.connect(self.on_widget_status_changed)
            self.floating_widget_controller.error_occurred.connect(self.on_widget_error)

            # 初始化UI
            self.init_ui()

            # 安全地刷新会话数据
            try:
                self.refresh_sessions()
            except Exception as e:
                self.logger.error(f"Error during initial session refresh: {e}")
                # 设置空的表格
                if hasattr(self, 'sessions_table'):
                    self.sessions_table.setRowCount(0)

            self.logger.info("Study tracker widget initialized")

        except Exception as e:
            self.logger.error(f"Error initializing study tracker widget: {e}")
            # 创建一个最小的UI以防止崩溃
            self.setWindowTitle("学习时间追踪")
            layout = QVBoxLayout(self)
            error_label = QLabel(f"初始化失败: {str(e)}")
            layout.addWidget(error_label)

            # 创建一个空的服务对象
            self.study_service = None
            self.floating_widget_controller = None
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("学习时间追踪")

        # 主布局 - 直接使用数据面板占据整个界面
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 数据展示面板占据整个界面
        data_panel = self.create_data_panel()
        main_layout.addWidget(data_panel)
    

    
    def create_data_panel(self) -> QWidget:
        """创建数据展示面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0)

        # 悬浮小组件控制区域
        control_group = self.create_floating_widget_control()
        layout.addWidget(control_group)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 学习记录标签页
        self.sessions_tab = self.create_sessions_tab()
        self.tab_widget.addTab(self.sessions_tab, "学习记录")

        # 统计分析标签页
        self.stats_widget = StudyStatsWidget()
        self.tab_widget.addTab(self.stats_widget, "统计分析")

        layout.addWidget(self.tab_widget)
        return panel

    def create_floating_widget_control(self) -> QWidget:
        """创建悬浮小组件控制区域"""
        control_group = QGroupBox("学习追踪悬浮小组件")
        control_layout = QHBoxLayout(control_group)

        # 开关复选框
        self.widget_toggle_checkbox = QCheckBox("启用悬浮小组件")
        self.widget_toggle_checkbox.toggled.connect(self.on_widget_toggle_changed)
        control_layout.addWidget(self.widget_toggle_checkbox)

        # 状态标签
        self.widget_status_label = QLabel("状态: 检查中...")
        control_layout.addWidget(self.widget_status_label)

        # 显示按钮
        self.show_widget_btn = QPushButton("显示悬浮窗")
        self.show_widget_btn.clicked.connect(self.show_floating_widget)
        self.show_widget_btn.setEnabled(False)
        control_layout.addWidget(self.show_widget_btn)

        control_layout.addStretch()

        # 加载初始状态
        self.load_widget_toggle_state()

        return control_group
    
    def create_sessions_tab(self) -> QWidget:
        """创建学习记录标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 筛选控件
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("开始日期:"))
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        filter_layout.addWidget(self.start_date_edit)
        
        filter_layout.addWidget(QLabel("结束日期:"))
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        filter_layout.addWidget(self.end_date_edit)
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_sessions)
        filter_layout.addWidget(self.refresh_btn)

        self.export_btn = QPushButton("导出数据")
        self.export_btn.clicked.connect(self.export_data)
        filter_layout.addWidget(self.export_btn)

        filter_layout.addStretch()
        layout.addLayout(filter_layout)
        
        # 学习记录表格
        self.sessions_table = QTableWidget()
        self.sessions_table.setColumnCount(8)
        self.sessions_table.setHorizontalHeaderLabels([
            "日期", "科目", "标题", "开始时间", "时长(分钟)", "状态", "专注度", "操作"
        ])
        
        # 设置表格属性
        self.sessions_table.setAlternatingRowColors(True)
        self.sessions_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.sessions_table.horizontalHeader().setStretchLastSection(True)
        
        layout.addWidget(self.sessions_table)
        
        return tab
    
    # ==================== 悬浮小组件控制 ====================

    def load_widget_toggle_state(self):
        """加载悬浮小组件开关状态"""
        try:
            # 加载保存的设置
            enabled = self.floating_widget_controller.get_widget_enabled_setting()
            self.widget_toggle_checkbox.setChecked(enabled)

            # 更新状态显示
            self.update_widget_status_display()

        except Exception as e:
            self.logger.error(f"加载悬浮小组件开关状态失败: {e}")

    def on_widget_toggle_changed(self, checked: bool):
        """处理悬浮小组件开关变化"""
        try:
            self.widget_status_label.setText("状态: 处理中...")
            self.widget_toggle_checkbox.setEnabled(False)

            # 保存设置
            self.floating_widget_controller.set_widget_enabled_setting(checked)

            if checked:
                # 启用悬浮小组件
                success = self.floating_widget_controller.start_widget()
                if success:
                    self.widget_status_label.setText("状态: 启动中...")
                else:
                    self.widget_toggle_checkbox.setChecked(False)
                    self.widget_status_label.setText("状态: 启动失败")
            else:
                # 停用悬浮小组件
                success = self.floating_widget_controller.stop_widget()
                if success:
                    self.widget_status_label.setText("状态: 已停止")
                else:
                    self.widget_status_label.setText("状态: 停止失败")

            self.widget_toggle_checkbox.setEnabled(True)

        except Exception as e:
            self.logger.error(f"处理悬浮小组件开关变化失败: {e}")
            self.widget_status_label.setText("状态: 操作失败")
            self.widget_toggle_checkbox.setEnabled(True)

    def on_widget_status_changed(self, is_running: bool):
        """悬浮小组件状态变化处理"""
        self.logger.info(f"StudyTrackerWidget received status change: {is_running}")

        try:
            # 检查当前复选框状态
            current_checkbox_state = self.widget_toggle_checkbox.isChecked()

            if is_running:
                self.widget_status_label.setText("状态: 运行中")
                self.show_widget_btn.setEnabled(True)
                self.show_widget_btn.setText("显示悬浮窗")

                # 如果小组件运行中但复选框未勾选，自动勾选
                if not current_checkbox_state:
                    self.widget_toggle_checkbox.setChecked(True)

            else:
                self.widget_status_label.setText("状态: 已停止")
                self.show_widget_btn.setEnabled(False)
                self.show_widget_btn.setText("显示悬浮窗")

                # 如果小组件已停止但复选框仍勾选，自动取消勾选
                if current_checkbox_state:
                    # 临时断开信号连接，避免触发toggle事件
                    self.widget_toggle_checkbox.toggled.disconnect()
                    self.widget_toggle_checkbox.setChecked(False)
                    # 重新连接信号
                    self.widget_toggle_checkbox.toggled.connect(self.on_widget_toggle_changed)

            self.update_widget_status_display()

        except Exception as e:
            self.logger.error(f"处理悬浮小组件状态变化失败: {e}")

    def on_widget_error(self, error_message: str):
        """悬浮小组件错误处理"""
        self.logger.error(f"悬浮小组件错误: {error_message}")
        self.widget_status_label.setText(f"状态: 错误 - {error_message}")
        QMessageBox.warning(self, "悬浮小组件错误", error_message)

    def update_widget_status_display(self):
        """更新悬浮小组件状态显示"""
        try:
            is_running = self.floating_widget_controller.is_running()
            enabled = self.widget_toggle_checkbox.isChecked()

            if enabled and is_running:
                self.widget_status_label.setText("状态: 运行中")
                self.show_widget_btn.setEnabled(True)
            elif enabled and not is_running:
                self.widget_status_label.setText("状态: 启动中...")
                self.show_widget_btn.setEnabled(False)
            else:
                self.widget_status_label.setText("状态: 已停止")
                self.show_widget_btn.setEnabled(False)

        except Exception as e:
            self.logger.error(f"更新悬浮小组件状态显示失败: {e}")

    def show_floating_widget(self):
        """显示悬浮小组件"""
        try:
            success = self.floating_widget_controller.send_show_command()
            if success:
                self.logger.info("已发送显示悬浮小组件命令")
            else:
                QMessageBox.warning(self, "警告", "发送显示命令失败")

        except Exception as e:
            self.logger.error(f"显示悬浮小组件失败: {e}")
            QMessageBox.critical(self, "错误", f"显示悬浮小组件失败: {str(e)}")

    # ==================== 事件处理 ====================

    def export_data(self):
        """导出学习数据"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出学习数据",
            f"学习记录_{datetime.now().strftime('%Y%m%d')}.csv",
            "CSV文件 (*.csv)"
        )

        if file_path:
            start_date = qdate_to_python_date(self.start_date_edit.date())
            end_date = qdate_to_python_date(self.end_date_edit.date())

            if self.study_service.export_to_csv(file_path, start_date, end_date):
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
            else:
                QMessageBox.critical(self, "错误", "数据导出失败！")
    
    # ==================== 数据更新 ====================
    
    def refresh_sessions(self):
        """刷新学习记录"""
        try:
            start_date = qdate_to_python_date(self.start_date_edit.date())
            end_date = qdate_to_python_date(self.end_date_edit.date())

            sessions = self.study_service.get_sessions(
                start_date=start_date,
                end_date=end_date,
                limit=1000
            )

            self.sessions_table.setRowCount(len(sessions))

            for row, session in enumerate(sessions):
                try:
                    # 现在session是字典格式，直接访问键值
                    # 日期
                    date_str = session['start_time'].strftime('%Y-%m-%d') if session['start_time'] else '未知'
                    date_item = QTableWidgetItem(date_str)
                    self.sessions_table.setItem(row, 0, date_item)

                    # 科目
                    subject_name = session.get('subject_name', '未知')
                    subject_item = QTableWidgetItem(subject_name)
                    self.sessions_table.setItem(row, 1, subject_item)
                except Exception as e:
                    self.logger.error(f"Error processing session row {row}: {e}")
                    # 设置默认值
                    self.sessions_table.setItem(row, 0, QTableWidgetItem("未知"))
                    self.sessions_table.setItem(row, 1, QTableWidgetItem("未知"))

                    # 标题
                    title_item = QTableWidgetItem(session.get('title', ''))
                    self.sessions_table.setItem(row, 2, title_item)

                    # 开始时间
                    start_time_str = session['start_time'].strftime('%H:%M') if session['start_time'] else '未知'
                    start_time_item = QTableWidgetItem(start_time_str)
                    self.sessions_table.setItem(row, 3, start_time_item)

                    # 时长
                    duration_item = QTableWidgetItem(str(session.get('duration_minutes', 0)))
                    self.sessions_table.setItem(row, 4, duration_item)

                    # 状态
                    status_text = session.get('status', '未知')
                    status_item = QTableWidgetItem(status_text)
                    self.sessions_table.setItem(row, 5, status_item)

                    # 专注度
                    focus_rating = session.get('focus_rating')
                    focus_item = QTableWidgetItem(str(focus_rating) if focus_rating else "")
                    self.sessions_table.setItem(row, 6, focus_item)

                    # 操作按钮
                    # TODO: 添加编辑、删除按钮
                    action_item = QTableWidgetItem("编辑")
                    self.sessions_table.setItem(row, 7, action_item)

        except Exception as e:
            self.logger.error(f"Error refreshing sessions: {e}")
            # 清空表格并显示错误信息
            self.sessions_table.setRowCount(1)
            self.sessions_table.setItem(0, 0, QTableWidgetItem("加载失败"))
            for col in range(1, 8):
                self.sessions_table.setItem(0, col, QTableWidgetItem(""))
    

