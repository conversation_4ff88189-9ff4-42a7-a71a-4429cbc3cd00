"""
字符串处理工具类

提供字符串处理的常用功能，包括：
- 字符串验证
- 字符串格式化
- 文本清理
- 编码转换
- 模糊匹配
"""

import re
import unicodedata
import hashlib
import base64
from typing import List, Optional, Dict, Union
import difflib


class StringUtils:
    """字符串处理工具类"""
    
    @staticmethod
    def is_empty(text: Optional[str]) -> bool:
        """
        检查字符串是否为空
        
        Args:
            text: 待检查的字符串
            
        Returns:
            是否为空
        """
        return not text or text.strip() == ""
    
    @staticmethod
    def is_email(email: str) -> bool:
        """
        验证邮箱格式
        
        Args:
            email: 邮箱地址
            
        Returns:
            是否为有效邮箱格式
        """
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def is_phone(phone: str) -> bool:
        """
        验证手机号格式（中国大陆）
        
        Args:
            phone: 手机号
            
        Returns:
            是否为有效手机号格式
        """
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone))
    
    @staticmethod
    def is_url(url: str) -> bool:
        """
        验证URL格式
        
        Args:
            url: URL地址
            
        Returns:
            是否为有效URL格式
        """
        pattern = r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'
        return bool(re.match(pattern, url))
    
    @staticmethod
    def is_ip_address(ip: str) -> bool:
        """
        验证IP地址格式
        
        Args:
            ip: IP地址
            
        Returns:
            是否为有效IP地址格式
        """
        pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        return bool(re.match(pattern, ip))
    
    @staticmethod
    def clean_text(text: str) -> str:
        """
        清理文本（移除多余空白字符）
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return ""
        
        # 移除首尾空白
        text = text.strip()
        
        # 将多个空白字符替换为单个空格
        text = re.sub(r'\s+', ' ', text)
        
        return text
    
    @staticmethod
    def remove_html_tags(html: str) -> str:
        """
        移除HTML标签
        
        Args:
            html: HTML文本
            
        Returns:
            纯文本
        """
        if not html:
            return ""
        
        # 移除HTML标签
        clean = re.compile('<.*?>')
        text = re.sub(clean, '', html)
        
        # 解码HTML实体
        import html as html_module
        text = html_module.unescape(text)
        
        return StringUtils.clean_text(text)
    
    @staticmethod
    def truncate(text: str, length: int, suffix: str = "...") -> str:
        """
        截断字符串
        
        Args:
            text: 原始文本
            length: 最大长度
            suffix: 后缀
            
        Returns:
            截断后的文本
        """
        if not text or len(text) <= length:
            return text
        
        return text[:length - len(suffix)] + suffix
    
    @staticmethod
    def camel_to_snake(text: str) -> str:
        """
        驼峰命名转下划线命名
        
        Args:
            text: 驼峰命名字符串
            
        Returns:
            下划线命名字符串
        """
        # 在大写字母前插入下划线
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', text)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    @staticmethod
    def snake_to_camel(text: str, capitalize_first: bool = False) -> str:
        """
        下划线命名转驼峰命名
        
        Args:
            text: 下划线命名字符串
            capitalize_first: 是否首字母大写
            
        Returns:
            驼峰命名字符串
        """
        components = text.split('_')
        if capitalize_first:
            return ''.join(word.capitalize() for word in components)
        else:
            return components[0] + ''.join(word.capitalize() for word in components[1:])
    
    @staticmethod
    def normalize_unicode(text: str) -> str:
        """
        Unicode标准化
        
        Args:
            text: 原始文本
            
        Returns:
            标准化后的文本
        """
        if not text:
            return ""
        
        return unicodedata.normalize('NFKC', text)
    
    @staticmethod
    def remove_accents(text: str) -> str:
        """
        移除重音符号
        
        Args:
            text: 原始文本
            
        Returns:
            移除重音后的文本
        """
        if not text:
            return ""
        
        # 分解Unicode字符
        nfkd_form = unicodedata.normalize('NFD', text)
        # 移除重音符号
        return ''.join(char for char in nfkd_form if unicodedata.category(char) != 'Mn')
    
    @staticmethod
    def similarity(text1: str, text2: str) -> float:
        """
        计算两个字符串的相似度
        
        Args:
            text1: 第一个字符串
            text2: 第二个字符串
            
        Returns:
            相似度（0-1之间）
        """
        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0
        
        return difflib.SequenceMatcher(None, text1, text2).ratio()
    
    @staticmethod
    def fuzzy_match(text: str, candidates: List[str], threshold: float = 0.6) -> List[tuple]:
        """
        模糊匹配
        
        Args:
            text: 目标文本
            candidates: 候选文本列表
            threshold: 相似度阈值
            
        Returns:
            匹配结果列表 [(候选文本, 相似度)]
        """
        matches = []
        for candidate in candidates:
            similarity = StringUtils.similarity(text, candidate)
            if similarity >= threshold:
                matches.append((candidate, similarity))
        
        # 按相似度降序排列
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches
    
    @staticmethod
    def extract_numbers(text: str) -> List[float]:
        """
        从文本中提取数字
        
        Args:
            text: 原始文本
            
        Returns:
            数字列表
        """
        if not text:
            return []
        
        # 匹配整数和小数
        pattern = r'-?\d+\.?\d*'
        matches = re.findall(pattern, text)
        
        numbers = []
        for match in matches:
            try:
                if '.' in match:
                    numbers.append(float(match))
                else:
                    numbers.append(float(int(match)))
            except ValueError:
                continue
        
        return numbers
    
    @staticmethod
    def extract_urls(text: str) -> List[str]:
        """
        从文本中提取URL
        
        Args:
            text: 原始文本
            
        Returns:
            URL列表
        """
        if not text:
            return []
        
        pattern = r'https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?'
        return re.findall(pattern, text)
    
    @staticmethod
    def extract_emails(text: str) -> List[str]:
        """
        从文本中提取邮箱地址
        
        Args:
            text: 原始文本
            
        Returns:
            邮箱地址列表
        """
        if not text:
            return []
        
        pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        return re.findall(pattern, text)
    
    @staticmethod
    def mask_sensitive_info(text: str, mask_char: str = "*") -> str:
        """
        遮蔽敏感信息
        
        Args:
            text: 原始文本
            mask_char: 遮蔽字符
            
        Returns:
            遮蔽后的文本
        """
        if not text:
            return ""
        
        # 遮蔽邮箱
        text = re.sub(r'([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', 
                     lambda m: f"{m.group(1)[:2]}{mask_char * 3}@{m.group(2)}", text)
        
        # 遮蔽手机号
        text = re.sub(r'(1[3-9]\d)(\d{4})(\d{4})', 
                     lambda m: f"{m.group(1)}{mask_char * 4}{m.group(3)}", text)
        
        # 遮蔽身份证号
        text = re.sub(r'(\d{6})(\d{8})(\d{4})', 
                     lambda m: f"{m.group(1)}{mask_char * 8}{m.group(3)}", text)
        
        return text
    
    @staticmethod
    def generate_hash(text: str, algorithm: str = 'md5') -> str:
        """
        生成字符串哈希值
        
        Args:
            text: 原始文本
            algorithm: 哈希算法
            
        Returns:
            哈希值
        """
        if not text:
            return ""
        
        hash_obj = hashlib.new(algorithm)
        hash_obj.update(text.encode('utf-8'))
        return hash_obj.hexdigest()
    
    @staticmethod
    def encode_base64(text: str) -> str:
        """
        Base64编码
        
        Args:
            text: 原始文本
            
        Returns:
            Base64编码后的字符串
        """
        if not text:
            return ""
        
        return base64.b64encode(text.encode('utf-8')).decode('utf-8')
    
    @staticmethod
    def decode_base64(encoded_text: str) -> str:
        """
        Base64解码
        
        Args:
            encoded_text: Base64编码的字符串
            
        Returns:
            解码后的文本
        """
        if not encoded_text:
            return ""
        
        try:
            return base64.b64decode(encoded_text).decode('utf-8')
        except Exception:
            return ""
    
    @staticmethod
    def word_count(text: str) -> Dict[str, int]:
        """
        统计词频
        
        Args:
            text: 原始文本
            
        Returns:
            词频字典
        """
        if not text:
            return {}
        
        # 简单的词频统计（按空格分割）
        words = text.lower().split()
        word_count = {}
        
        for word in words:
            # 移除标点符号
            word = re.sub(r'[^\w]', '', word)
            if word:
                word_count[word] = word_count.get(word, 0) + 1
        
        return word_count
