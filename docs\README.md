# Personal Manager System - 个人管理系统

一个功能强大的Python桌面个人管理系统，集成文件管理、任务调度、系统监控、数据备份等多种功能。

## 🚀 功能特性

### 核心模块
- **文件管理器** - 浏览、搜索、分类、清理重复文件
- **任务管理** - 任务创建、调度、提醒系统
- **系统监控** - CPU、内存、磁盘使用率实时监控
- **应用启动器** - 快捷方式管理和应用程序启动
- **数据备份** - 自动备份和同步功能
- **笔记管理** - 个人知识管理系统
- **密码管理器** - 安全的密码存储和管理

### 技术特性
- 🎨 现代化PyQt6界面
- 🗄️ SQLite数据库 + SQLAlchemy ORM
- 🔧 模块化架构设计
- 📝 完整的日志系统
- ⚙️ 灵活的配置管理
- 🔒 数据加密和安全保护
- 📦 一键打包部署

## 🏗️ 系统架构

```
┌─────────────────┐
│   表现层 (GUI)   │  ← PyQt6界面、事件处理
├─────────────────┤
│  业务逻辑层      │  ← 服务类、业务规则
├─────────────────┤
│  数据访问层      │  ← SQLAlchemy ORM、文件操作
├─────────────────┤
│  基础设施层      │  ← 配置、日志、工具类
└─────────────────┘
```

## 📁 项目结构

```
personal_manager/
├── src/                        # 源代码
│   ├── core/                   # 核心基础设施
│   │   ├── config.py          # 配置管理
│   │   ├── database.py        # 数据库连接
│   │   └── logger.py          # 日志系统
│   ├── models/                # 数据模型
│   │   └── base.py           # 基础模型
│   ├── services/             # 业务逻辑服务
│   ├── modules/              # 功能模块
│   │   ├── file_manager/     # 文件管理器
│   │   ├── task_manager/     # 任务管理
│   │   ├── system_monitor/   # 系统监控
│   │   ├── app_launcher/     # 应用启动器
│   │   ├── backup_manager/   # 备份管理
│   │   ├── note_manager/     # 笔记管理
│   │   └── password_manager/ # 密码管理
│   ├── gui/                  # GUI界面
│   │   ├── main_window.py    # 主窗口
│   │   ├── widgets/          # 自定义控件
│   │   └── dialogs/          # 对话框
│   └── utils/                # 工具类
├── tests/                    # 测试代码
├── resources/                # 资源文件
├── config/                   # 配置文件
├── docs/                     # 文档
├── requirements.txt          # 依赖管理
├── setup.py                  # 安装脚本
└── main.py                   # 程序入口
```

## 🛠️ 技术栈

### 核心技术
- **GUI框架**: PyQt6 - 现代化、功能强大、跨平台
- **数据库**: SQLite + SQLAlchemy ORM
- **任务调度**: APScheduler
- **系统监控**: psutil
- **文件监控**: watchdog
- **加密**: cryptography
- **打包**: PyInstaller

### 开发工具
- **测试**: pytest + pytest-qt
- **代码格式化**: black
- **代码检查**: flake8
- **版本控制**: Git

## 🚀 快速开始

### 环境要求
- Python 3.9+
- Windows 10/11 (主要支持平台)
- 4GB+ RAM
- 1GB+ 磁盘空间

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/yourusername/personal-manager.git
cd personal-manager
```

2. **创建虚拟环境**
```bash
python -m venv venv
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **运行应用**
```bash
python main.py
```

### 开发模式安装
```bash
pip install -e .
```

## 📋 开发计划

### 阶段1：基础框架搭建 (2-3周)
- [x] 项目结构创建
- [x] 配置和日志系统
- [x] 数据库设计和连接
- [x] 主窗口框架
- [ ] 基础工具类

### 阶段2：核心模块开发 (4-6周)
- [ ] 文件管理器基础功能
- [ ] 系统监控模块
- [ ] 任务管理基础功能
- [ ] 基础GUI界面

### 阶段3：高级功能开发 (4-5周)
- [ ] 应用启动器
- [ ] 数据备份功能
- [ ] 笔记管理
- [ ] 密码管理器

### 阶段4：优化和完善 (2-3周)
- [ ] 性能优化
- [ ] 界面美化
- [ ] 错误处理完善
- [ ] 用户体验优化

### 阶段5：测试和部署 (1-2周)
- [ ] 单元测试
- [ ] 集成测试
- [ ] 打包和安装程序制作
- [ ] 文档编写

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定模块测试
pytest tests/test_file_manager.py

# 运行GUI测试
pytest tests/test_gui.py
```

## 📦 打包部署

### 创建可执行文件
```bash
pyinstaller --onefile --windowed main.py
```

### 创建安装程序
使用Inno Setup或NSIS创建Windows安装程序。

## 🔧 配置

应用程序配置文件位于：
- Windows: `%APPDATA%\PersonalManager\config.yaml`
- 用户配置: `%APPDATA%\PersonalManager\user_config.yaml`

主要配置项：
- 数据库连接
- 日志级别
- GUI主题
- 模块设置

## 📝 日志

日志文件位于：
- Windows: `%APPDATA%\PersonalManager\logs\app.log`

支持的日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
- 创建 [Issue](https://github.com/yourusername/personal-manager/issues)
- 发送邮件至 <EMAIL>

## 🙏 致谢

感谢以下开源项目：
- [PyQt6](https://www.riverbankcomputing.com/software/pyqt/)
- [SQLAlchemy](https://www.sqlalchemy.org/)
- [APScheduler](https://apscheduler.readthedocs.io/)
- [psutil](https://psutil.readthedocs.io/)

---

**Personal Manager System** - 让个人管理更简单、更高效！
