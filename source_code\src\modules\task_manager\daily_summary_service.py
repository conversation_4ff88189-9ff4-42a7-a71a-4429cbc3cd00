"""
Daily Summary Service for Task Manager Module

This service provides business logic for daily summary management.
"""

import logging
from datetime import datetime, date, timedelta
from typing import Optional, Dict, List

from core.database import get_session
from models.task_models import DailySummary


class DailySummaryService:
    """Service for managing daily summaries"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def save_daily_summary(self, summary_date: date, content: str) -> bool:
        """Save or update daily summary

        Args:
            summary_date: Date for the summary
            content: Summary content

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with get_session() as session:
                # Convert date to datetime if needed
                if isinstance(summary_date, date):
                    summary_datetime = datetime.combine(summary_date, datetime.min.time())
                else:
                    summary_datetime = summary_date

                # Create or update summary
                summary = DailySummary.create_or_update(
                    session=session,
                    summary_date=summary_datetime,
                    content=content
                )

                session.commit()
                self.logger.info(f"Daily summary saved for date: {summary_date}")
                return True

        except Exception as e:
            self.logger.error(f"Error saving daily summary: {e}")
            return False
    
    def get_daily_summary(self, summary_date: date) -> str:
        """Get daily summary for a specific date
        
        Args:
            summary_date: Date to get summary for
            
        Returns:
            str: Summary content or empty string if not found
        """
        try:
            with get_session() as session:
                # Convert date to datetime if needed
                if isinstance(summary_date, date):
                    summary_datetime = datetime.combine(summary_date, datetime.min.time())
                else:
                    summary_datetime = summary_date
                
                summary = DailySummary.get_by_date(session, summary_datetime)
                
                if summary:
                    return summary.content or ""
                else:
                    return ""
                    
        except Exception as e:
            self.logger.error(f"Error getting daily summary: {e}")
            return ""
    
    def get_summaries_for_week(self, week_start: date) -> Dict[date, str]:
        """Get all daily summaries for a week
        
        Args:
            week_start: Start date of the week
            
        Returns:
            Dict[date, str]: Dictionary mapping dates to summary content
        """
        try:
            summaries = {}
            
            # Get summaries for each day of the week
            for i in range(7):
                current_date = week_start + timedelta(days=i)
                content = self.get_daily_summary(current_date)
                if content:  # Only include non-empty summaries
                    summaries[current_date] = content
            
            return summaries
            
        except Exception as e:
            self.logger.error(f"Error getting weekly summaries: {e}")
            return {}
    
    def delete_daily_summary(self, summary_date: date) -> bool:
        """Delete daily summary for a specific date
        
        Args:
            summary_date: Date to delete summary for
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with get_session() as session:
                # Convert date to datetime if needed
                if isinstance(summary_date, date):
                    summary_datetime = datetime.combine(summary_date, datetime.min.time())
                else:
                    summary_datetime = summary_date
                
                summary = DailySummary.get_by_date(session, summary_datetime)
                
                if summary:
                    summary.is_deleted = True
                    summary.updated_at = datetime.utcnow()
                    session.commit()
                    self.logger.info(f"Daily summary deleted for date: {summary_date}")
                    return True
                else:
                    self.logger.warning(f"No summary found for date: {summary_date}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error deleting daily summary: {e}")
            return False
