"""
桌面任务小部件 - 性能优化版本

提供桌面上的任务快速查看和管理功能
"""

import sys
import os
import traceback
from datetime import datetime, date
from typing import List, Dict, Any, Optional
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# 基础PyQt6导入 - 快速启动必需
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QScrollArea, QFrame, QMenu,
    QMessageBox, QSizePolicy, QDialog, QLineEdit, QTextEdit,
    QComboBox, QDateTimeEdit, QCheckBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPoint, QSize, QDateTime
from PyQt6.QtGui import QFont, QColor, QIcon, QMouseEvent

# 延迟导入标记
_heavy_imports_loaded = False
_task_service = None
_daily_summary_service = None
_logger = None


def load_heavy_imports():
    """延迟加载重型模块"""
    global _heavy_imports_loaded, _task_service, _daily_summary_service

    if _heavy_imports_loaded:
        return

    try:
        # 延迟导入重型模块
        from modules.task_manager.task_service import TaskService
        from modules.task_manager.daily_summary_service import DailySummaryService

        # 初始化服务
        _task_service = TaskService()
        _daily_summary_service = DailySummaryService()

        # 初始化数据库
        init_database()

        _heavy_imports_loaded = True

    except Exception as e:
        print(f"延迟导入失败: {e}")


def init_database():
    """初始化数据库连接"""
    try:
        import core.database as db
        import core.config as config

        config_manager = config.Config()
        db_manager = db.init_db_manager(config_manager)
        print("数据库初始化成功")
    except Exception as e:
        print(f"数据库初始化失败: {e}")


def get_task_service():
    """获取任务服务实例"""
    global _task_service
    if _task_service is None:
        load_heavy_imports()
    return _task_service


def get_daily_summary_service():
    """获取日总结服务实例"""
    global _daily_summary_service
    if _daily_summary_service is None:
        load_heavy_imports()
    return _daily_summary_service


class TaskDetailDialog(QDialog):
    """任务详情对话框"""

    task_updated = pyqtSignal(dict)  # 任务更新信号
    task_deleted = pyqtSignal(int)   # 任务删除信号

    def __init__(self, task_data: Dict, parent=None):
        super().__init__(parent)
        self.task_data = task_data
        self.task_service = get_task_service()
        self.init_ui()
        self.apply_styles()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("任务详情")
        self.setFixedSize(400, 300)
        # 设置窗口标志，确保对话框独立显示，不影响父窗口
        self.setWindowFlags(
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowCloseButtonHint |
            Qt.WindowType.WindowStaysOnTopHint
        )

        layout = QVBoxLayout(self)

        # 任务标题（可编辑）
        title_layout = QHBoxLayout()
        title_layout.addWidget(QLabel("任务:"))
        self.title_edit = QLineEdit(self.task_data.get('title', ''))
        self.title_edit.setPlaceholderText("请输入任务标题...")
        title_layout.addWidget(self.title_edit)
        layout.addLayout(title_layout)

        # 任务描述（可编辑）
        desc_layout = QVBoxLayout()
        desc_layout.addWidget(QLabel("描述:"))
        self.desc_edit = QTextEdit()
        self.desc_edit.setPlainText(self.task_data.get('description', ''))
        self.desc_edit.setPlaceholderText("请输入任务描述...")
        self.desc_edit.setMaximumHeight(80)  # 限制高度
        desc_layout.addWidget(self.desc_edit)
        layout.addLayout(desc_layout)

        # 完成状态
        self.completed_checkbox = QCheckBox("已完成")
        # 根据status字段判断是否完成
        status = self.task_data.get('status', '待处理')
        is_completed = status in ['已完成', '完成', 'completed']
        self.completed_checkbox.setChecked(is_completed)
        layout.addWidget(self.completed_checkbox)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 保存按钮
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_task)
        self.save_btn.setToolTip("保存任务修改")
        button_layout.addWidget(self.save_btn)

        # 删除按钮
        self.delete_btn = QPushButton("删除")
        self.delete_btn.clicked.connect(self.delete_task)
        button_layout.addWidget(self.delete_btn)

        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                color: #ecf0f1;
                border-radius: 10px;
            }
            QLabel {
                color: #ecf0f1;
                font-size: 12px;
                padding: 5px;
            }
            QCheckBox {
                color: #ecf0f1;
                font-size: 12px;
                padding: 5px;
            }
            QLineEdit, QTextEdit {
                background-color: white;
                color: black;
                border: 1px solid #4a6a8b;
                border-radius: 4px;
                padding: 5px;
                font-size: 10pt;
            }
            QLineEdit:focus, QTextEdit:focus {
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
        """)

        # 删除按钮特殊样式
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ec7063, stop:1 #e74c3c);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
        """)

    def save_task(self):
        """保存任务"""
        try:
            task_id = self.task_data.get('id')

            if task_id:
                # 获取编辑后的内容
                new_title = self.title_edit.text().strip()
                new_description = self.desc_edit.toPlainText().strip()
                completed = self.completed_checkbox.isChecked()

                # 验证标题不能为空
                if not new_title:
                    QMessageBox.warning(self, "错误", "任务标题不能为空")
                    return

                new_status = '已完成' if completed else '进行中'

                # 准备更新数据
                update_data = {
                    'title': new_title,
                    'description': new_description
                }

                # 设置状态
                if completed:
                    update_data['status'] = '已完成'
                else:
                    update_data['status'] = '进行中'



                # 使用正确的方法更新任务
                success = self.task_service.update_task(task_id, **update_data)

                if success:
                    # 更新任务数据
                    self.task_data['title'] = new_title
                    self.task_data['description'] = new_description
                    self.task_data['status'] = new_status

                    # 发送更新信号
                    self.task_updated.emit(self.task_data)

                    QMessageBox.information(self, "成功", "任务已保存")
                    self.accept()
                else:
                    QMessageBox.warning(self, "错误", "更新任务失败")
        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "错误", f"保存失败: {e}")

    def delete_task(self):
        """删除任务"""
        reply = QMessageBox.question(
            self, "确认删除",
            "确定要删除这个任务吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                task_id = self.task_data.get('id')
                if task_id:
                    self.task_service.delete_task(task_id)

                    # 发送删除信号
                    self.task_deleted.emit(task_id)

                    QMessageBox.information(self, "成功", "任务已删除")
                    self.accept()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除失败: {e}")


class TaskCardWidget(QFrame):
    """单个任务卡片组件"""
    
    task_clicked = pyqtSignal(dict)  # 任务被点击
    
    def __init__(self, task_data: Dict, parent=None):
        super().__init__(parent)
        self.task_data = task_data
        self.init_ui()
        self.apply_styles()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(4)
        
        # 任务标题
        self.title_label = QLabel(self.task_data.get('title', '未知任务'))
        self.title_label.setFont(QFont('Microsoft YaHei', 9, QFont.Weight.Bold))
        self.title_label.setWordWrap(True)
        layout.addWidget(self.title_label)
        
        # 任务状态和优先级
        info_layout = QHBoxLayout()
        info_layout.setContentsMargins(0, 0, 0, 0)
        
        # 状态标签
        status = self.task_data.get('status', '待处理')
        self.status_label = QLabel(status)
        self.status_label.setFont(QFont('Microsoft YaHei', 8))
        info_layout.addWidget(self.status_label)
        
        info_layout.addStretch()
        
        # 优先级标签
        priority = self.task_data.get('priority', '普通')
        self.priority_label = QLabel(priority)
        self.priority_label.setFont(QFont('Microsoft YaHei', 8))
        info_layout.addWidget(self.priority_label)
        
        layout.addLayout(info_layout)
    
    def apply_styles(self):
        """应用样式"""
        # 根据状态设置颜色
        status = self.task_data.get('status', '待处理')
        priority = self.task_data.get('priority', '普通')
        
        # 状态颜色
        status_colors = {
            '待处理': '#FFA500',
            'pending': '#FFA500',
            '进行中': '#4169E1',
            'in_progress': '#4169E1',
            '已完成': '#32CD32',
            'completed': '#32CD32',
            '已取消': '#DC143C',
            'cancelled': '#DC143C'
        }
        
        # 优先级颜色
        priority_colors = {
            '紧急': '#FF4444',
            'urgent': '#FF4444',
            '高': '#FF8800',
            'high': '#FF8800',
            '普通': '#4CAF50',
            'normal': '#4CAF50',
            '低': '#9E9E9E',
            'low': '#9E9E9E'
        }
        
        status_color = status_colors.get(status, '#FFA500')
        priority_color = priority_colors.get(priority, '#4CAF50')
        
        self.setStyleSheet(f"""
            TaskCardWidget {{
                background: #FCE9DB;
                border: 1px solid #3a5a7b;
                border-radius: 6px;
                margin: 2px;
            }}
            TaskCardWidget:hover {{
                border: 1px solid #4a6a8b;
                background: #F5DFC4;
            }}
            QLabel {{
                color: black;
                background: transparent;
            }}
        """)
        
        # 设置状态标签颜色
        self.status_label.setStyleSheet(f"color: {status_color}; font-weight: bold;")
        self.priority_label.setStyleSheet(f"color: {priority_color}; font-weight: bold;")
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标点击事件 - 显示任务详情对话框"""
        try:
            if event.button() == Qt.MouseButton.LeftButton:
                self.show_task_detail()
            super().mousePressEvent(event)
        except RuntimeError as e:
            # 处理组件已被删除的情况
            if "wrapped C/C++ object" in str(e) and "has been deleted" in str(e):
                # 静默处理已删除组件的点击事件
                return
            else:
                raise  # 重新抛出其他类型的RuntimeError

    def show_task_detail(self):
        """显示任务详情对话框"""
        try:
            # 获取主窗口引用，确保对话框正确显示
            main_widget = self.parent()
            while main_widget and not isinstance(main_widget, DesktopTaskWidget):
                main_widget = main_widget.parent()

            # 创建对话框，使用主窗口作为父窗口
            dialog = TaskDetailDialog(self.task_data, main_widget)
            dialog.task_updated.connect(self.on_task_updated)
            dialog.task_deleted.connect(self.on_task_deleted)

            # 保存对话框引用，防止被垃圾回收
            if not hasattr(main_widget, '_active_dialogs'):
                main_widget._active_dialogs = []
            main_widget._active_dialogs.append(dialog)

            # 对话框关闭时清理引用
            def cleanup_dialog():
                if hasattr(main_widget, '_active_dialogs') and dialog in main_widget._active_dialogs:
                    main_widget._active_dialogs.remove(dialog)
            dialog.finished.connect(cleanup_dialog)

            # 不重复设置窗口标志，使用对话框自己的设置

            # 确保对话框在主窗口前面显示
            if main_widget:
                dialog.move(main_widget.x() + 50, main_widget.y() + 50)
            # 使用exec()确保对话框保持显示
            result = dialog.exec()

        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "错误", f"无法打开任务详情: {e}")

    def on_task_updated(self, updated_task_data):
        """任务更新回调"""
        self.task_data = updated_task_data
        # 更新显示
        self.title_label.setText(updated_task_data.get('title', '未知任务'))
        status = updated_task_data.get('status', '待处理')
        self.status_label.setText(status)
        self.apply_styles()

        # 通知父组件刷新
        self.task_clicked.emit(updated_task_data)

    def on_task_deleted(self, task_id):
        """任务删除回调"""
        # 通知父组件任务已删除
        self.task_clicked.emit({'action': 'deleted', 'id': task_id})
        # 隐藏自己
        self.hide()


class CategoryTasksWidget(QFrame):
    """分类任务显示组件"""

    task_clicked = pyqtSignal(dict)  # 任务被点击
    add_task_requested = pyqtSignal(str)  # 请求添加任务，传递分类名称
    summary_changed = pyqtSignal(str)  # 日总结内容改变

    def __init__(self, category: str, tasks: List[Dict], parent=None):
        super().__init__(parent)
        self.category = category
        self.tasks = tasks
        self.summary_text = None  # 日总结文本框引用
        self.save_timer = None  # 延迟保存定时器
        self.init_ui()
        self.apply_styles()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(3)
        
        # 分类标题
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        
        self.category_label = QLabel(self.category)
        self.category_label.setFont(QFont('Microsoft YaHei', 10, QFont.Weight.Bold))
        title_layout.addWidget(self.category_label)
        
        # 任务数量
        task_count = len(self.tasks)
        self.count_label = QLabel(f"({task_count})")
        self.count_label.setFont(QFont('Microsoft YaHei', 9))
        title_layout.addWidget(self.count_label)

        title_layout.addStretch()

        # 添加任务按钮（日总结分类不显示）
        if self.category != "日总结":
            add_btn = QPushButton("+")
            add_btn.setFixedSize(20, 20)
            add_btn.setFont(QFont('Microsoft YaHei', 10, QFont.Weight.Bold))
            add_btn.setToolTip(f"添加{self.category}任务")
            add_btn.clicked.connect(lambda: self.on_add_task_clicked())
            add_btn.setStyleSheet("""
                QPushButton {
                    background: #FCE9DB;
                    border: 1px solid black;
                    border-radius: 10px;
                    color: black;
                }
                QPushButton:hover {
                    background: #F5DFC4;
                }
                QPushButton:pressed {
                    background: #EEDCC0;
                }
            """)
            title_layout.addWidget(add_btn)

        layout.addLayout(title_layout)
        
        # 日总结分类的特殊处理 - 总是显示可编辑文本框
        if self.category == '日总结':
            self.summary_text = QTextEdit()

            # 检查是否有已存在的日总结内容
            existing_summary = ""
            for task in self.tasks:
                if task.get('is_summary', False):
                    existing_summary = task.get('description', '')
                    break

            if existing_summary:
                self.summary_text.setPlainText(existing_summary)
                self.summary_text.setPlaceholderText("编辑今日总结...")
            else:
                self.summary_text.setPlaceholderText("点击这里编写今日总结...")

            self.summary_text.setMaximumHeight(100)
            self.summary_text.setFont(QFont('Microsoft YaHei', 9))
            self.summary_text.setStyleSheet("""
                QTextEdit {
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid #4a6f4a;
                    border-radius: 4px;
                    color: black;
                    padding: 5px;
                }
                QTextEdit:focus {
                    border: 2px solid #6a8f6a;
                    background: rgba(255, 255, 255, 0.15);
                }
            """)

            # 连接文本改变信号，实现自动保存
            self.summary_text.textChanged.connect(self.on_summary_text_changed)

            layout.addWidget(self.summary_text)
        else:
            # 其他分类显示普通任务列表
            if self.tasks:
                # 过滤掉日总结任务，避免重复显示
                regular_tasks = [task for task in self.tasks if not task.get('is_summary', False)]
                if regular_tasks:
                    for task in regular_tasks:
                        task_card = TaskCardWidget(task)
                        task_card.task_clicked.connect(self.task_clicked.emit)
                        layout.addWidget(task_card)
                else:
                    # 无普通任务提示
                    no_task_label = QLabel("暂无任务")
                    no_task_label.setFont(QFont('Microsoft YaHei', 8))
                    no_task_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    no_task_label.setStyleSheet("color: #888888; font-style: italic;")
                    layout.addWidget(no_task_label)
            else:
                # 无任务提示
                no_task_label = QLabel("暂无任务")
                no_task_label.setFont(QFont('Microsoft YaHei', 8))
                no_task_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                no_task_label.setStyleSheet("color: #888888; font-style: italic;")
                layout.addWidget(no_task_label)
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            CategoryTasksWidget {
                background: #D8ECF4;
                border: 1px solid #2a4a6f;
                border-radius: 8px;
                margin: 3px;
            }
            QLabel {
                color: black;
                background: transparent;
            }
        """)
        
        # 分类标题颜色 - 统一设置为黑色
        self.category_label.setStyleSheet("color: black; font-weight: bold;")

    def on_add_task_clicked(self):
        """添加任务按钮点击处理"""
        self.add_task_requested.emit(self.category)

    def on_summary_text_changed(self):
        """处理日总结文本改变"""
        if self.category != '日总结' or not self.summary_text:
            return

        # 取消之前的定时器
        if self.save_timer:
            self.save_timer.stop()

        # 创建新的延迟保存定时器
        self.save_timer = QTimer()
        self.save_timer.setSingleShot(True)
        self.save_timer.timeout.connect(self.save_summary)
        self.save_timer.start(500)  # 500ms延迟

    def save_summary(self):
        """保存日总结"""
        if self.category != '日总结' or not self.summary_text:
            return

        summary_content = self.summary_text.toPlainText().strip()
        # 发出信号，让父组件处理保存
        self.summary_changed.emit(summary_content)


class AddTaskDialog(QDialog):
    """添加任务对话框"""
    
    task_created = pyqtSignal(dict)  # 任务创建成功
    
    def __init__(self, parent=None, default_category=None):
        super().__init__(parent)
        self.default_category = default_category
        self.setWindowTitle("添加任务")
        self.setModal(True)
        self.resize(400, 300)
        self.init_ui()
        self.apply_styles()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 任务标题
        title_label = QLabel("任务标题:")
        title_label.setFont(QFont('Microsoft YaHei', 9, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("请输入任务标题...")
        layout.addWidget(self.title_edit)
        
        # 任务描述
        desc_label = QLabel("任务描述:")
        desc_label.setFont(QFont('Microsoft YaHei', 9, QFont.Weight.Bold))
        layout.addWidget(desc_label)
        
        self.desc_edit = QTextEdit()
        self.desc_edit.setPlaceholderText("请输入任务描述...")
        self.desc_edit.setMaximumHeight(80)
        layout.addWidget(self.desc_edit)
        
        # 分类选择
        category_label = QLabel("任务分类:")
        category_label.setFont(QFont('Microsoft YaHei', 9, QFont.Weight.Bold))
        layout.addWidget(category_label)
        
        self.category_combo = QComboBox()
        self.category_combo.addItems(['上午', '下午', '晚上', '全天', '其他'])
        # 设置默认分类
        if self.default_category and self.default_category in ['上午', '下午', '晚上', '全天', '其他']:
            self.category_combo.setCurrentText(self.default_category)
        layout.addWidget(self.category_combo)
        
        # 优先级选择
        priority_label = QLabel("优先级:")
        priority_label.setFont(QFont('Microsoft YaHei', 9, QFont.Weight.Bold))
        layout.addWidget(priority_label)
        
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(['低', '普通', '高', '紧急'])
        self.priority_combo.setCurrentText('普通')
        layout.addWidget(self.priority_combo)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept_task)
        self.ok_btn.setDefault(True)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2a4a6b, stop:1 #1e3a5f);
                color: #E0E0E0;
            }
            QLabel {
                color: #E0E0E0;
                font-weight: bold;
            }
            QLineEdit, QTextEdit, QComboBox {
                background: #3a5a7b;
                border: 1px solid #4a6a8b;
                border-radius: 4px;
                padding: 5px;
                color: #E0E0E0;
                font-size: 9pt;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border: 2px solid #5a7a9b;
            }
            /* QComboBox下拉列表样式 - 白色背景 */
            QComboBox QAbstractItemView {
                background-color: white;
                color: black;
                border: 1px solid #4a6a8b;
                border-radius: 4px;
                selection-background-color: #3498db;
                selection-color: white;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                padding: 5px;
                border: none;
                background-color: white;
                color: black;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #3498db;
                color: white;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e3f2fd;
                color: black;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a6a8b, stop:1 #3a5a7b);
                border: 1px solid #5a7a9b;
                border-radius: 4px;
                padding: 8px 16px;
                color: #E0E0E0;
                font-weight: bold;
                min-width: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a7a9b, stop:1 #4a6a8b);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3a5a7b, stop:1 #2a4a6b);
            }
        """)
    
    def accept_task(self):
        """确认创建任务"""
        title = self.title_edit.text().strip()
        if not title:
            QMessageBox.warning(self, "错误", "请输入任务标题")
            return
        
        task_data = {
            'title': title,
            'description': self.desc_edit.toPlainText().strip(),
            'category': self.category_combo.currentText(),
            'priority': self.priority_combo.currentText(),
            'status': '待处理',
            'start_date': datetime.now()
        }
        
        self.task_created.emit(task_data)
        self.accept()


class DesktopTaskWidget(QWidget):
    """桌面任务小部件主类 - 性能优化版本"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 记录启动时间
        self.start_time = datetime.now()

        # 基础状态初始化
        self.current_date = date.today()
        self.tasks_by_category = {}
        self.is_closing = False
        self.restart_requested = False
        self.drag_position = QPoint()
        self.is_dragging = False
        self.task_service = None
        self.daily_summary_service = None

        # 窗口调整大小相关状态
        self.resize_mode = None  # 调整大小模式：None, 'left', 'right', 'top', 'bottom', 'top-left', 'top-right', 'bottom-left', 'bottom-right'
        self.resize_start_pos = QPoint()  # 调整大小开始位置
        self.resize_start_geometry = None  # 调整大小开始时的窗口几何
        self.resize_border_width = 8  # 边缘检测宽度
        self.resize_corner_size = 15  # 角落检测大小

        # 配置管理器
        from core.config import ConfigManager
        self.config_manager = ConfigManager()

        # 显示模式状态
        self.current_display_mode = "全显示"  # 默认为全显示

        # 快速显示界面
        self.init_ui()
        self.setup_window_properties()
        self.apply_styles()

        # 启用鼠标跟踪以便检测鼠标位置
        self.setMouseTracking(True)

        # 延迟初始化重型组件
        QTimer.singleShot(100, self.delayed_initialization)

    def delayed_initialization(self):
        """延迟初始化重型组件"""
        try:
            # 加载重型模块
            load_heavy_imports()

            # 获取任务服务
            self.task_service = get_task_service()

            # 初始化日总结服务
            self.daily_summary_service = get_daily_summary_service()

            # 设置定时器
            self.refresh_timer = QTimer()
            self.refresh_timer.timeout.connect(self.refresh_tasks)
            self.refresh_timer.start(30000)  # 30秒刷新一次，减少频率

            # 设置命令检查定时器
            self.command_timer = QTimer()
            self.command_timer.timeout.connect(self.check_show_command)
            self.command_timer.start(1000)  # 每秒检查一次显示命令

            # 设置异常处理
            self.setup_exception_handling()

            # 加载任务数据
            self.load_tasks()

            # 确保窗口可见
            self.show()
            self.raise_()
            self.activateWindow()

        except Exception as e:
            import traceback
            traceback.print_exc()
            # 显示错误信息但不崩溃
            self.show_error_message("初始化失败，请重试")

    def show_error_message(self, message: str):
        """显示错误信息"""
        error_label = QLabel(f"错误: {message}")
        error_label.setStyleSheet("color: #e74c3c; font-size: 12px; padding: 10px;")
        error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 清空现有布局并显示错误
        if hasattr(self, 'main_layout'):
            for i in reversed(range(self.main_layout.count())):
                self.main_layout.itemAt(i).widget().setParent(None)
            self.main_layout.addWidget(error_label)

    def setup_exception_handling(self):
        """设置异常处理"""
        try:
            # 设置全局异常处理器
            sys.excepthook = self.handle_exception
        except Exception as e:
            print(f"设置异常处理失败: {e}")

    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理未捕获的异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        print(f"未捕获的异常: {error_msg}")

        # 尝试重启小部件
        if not self.is_closing:
            self.restart_widget()

    def restart_widget(self):
        """重启小部件"""
        try:
            print("尝试重启桌面小部件...")
            self.restart_requested = True
            self.close()
        except Exception as e:
            print(f"重启小部件失败: {e}")

    def init_ui(self):
        """初始化UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # 标题栏
        self.create_title_bar()
        main_layout.addWidget(self.title_frame)

        # 日期显示
        self.create_date_display()
        main_layout.addWidget(self.date_frame)

        # 任务显示区域
        self.create_task_area()
        main_layout.addWidget(self.task_scroll)

        # 按钮区域
        self.create_button_area()
        main_layout.addWidget(self.button_frame)

        # 设置显示模式下拉菜单的初始值
        self.display_mode_combo.setCurrentText(self.current_display_mode)

    def create_title_bar(self):
        """创建标题栏"""
        self.title_frame = QFrame()
        title_layout = QHBoxLayout(self.title_frame)
        title_layout.setContentsMargins(5, 5, 5, 5)

        # 标题
        title_label = QLabel("任务小部件")
        title_label.setFont(QFont('Microsoft YaHei', 11, QFont.Weight.Bold))
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 显示模式下拉菜单
        self.display_mode_combo = QComboBox()
        self.display_mode_combo.addItems(['全显示', '半显示'])
        self.display_mode_combo.setFixedSize(80, 20)
        self.display_mode_combo.setFont(QFont('Microsoft YaHei', 8))
        self.display_mode_combo.setToolTip("选择显示模式：\n全显示 - 始终置顶显示\n半显示 - 可被其他应用覆盖")
        self.display_mode_combo.currentTextChanged.connect(self.on_display_mode_changed)
        title_layout.addWidget(self.display_mode_combo)

        # 最小化按钮
        self.minimize_btn = QPushButton("−")
        self.minimize_btn.setFixedSize(20, 20)
        self.minimize_btn.clicked.connect(self.showMinimized)
        title_layout.addWidget(self.minimize_btn)

        # 关闭按钮
        self.close_btn = QPushButton("×")
        self.close_btn.setFixedSize(20, 20)
        self.close_btn.clicked.connect(self.on_close_button_clicked)
        title_layout.addWidget(self.close_btn)

    def create_date_display(self):
        """创建日期显示"""
        self.date_frame = QFrame()
        date_layout = QVBoxLayout(self.date_frame)
        date_layout.setContentsMargins(8, 8, 8, 8)

        # 当前日期
        today = datetime.now()
        weekdays = ['一', '二', '三', '四', '五', '六', '日']
        weekday = weekdays[today.weekday()]

        date_text = f"{today.year}年{today.month:02d}月{today.day:02d}日 星期{weekday}"

        self.date_label = QLabel(date_text)
        self.date_label.setFont(QFont('Microsoft YaHei', 12, QFont.Weight.Bold))
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        date_layout.addWidget(self.date_label)

    def create_task_area(self):
        """创建任务显示区域"""
        self.task_scroll = QScrollArea()
        self.task_scroll.setWidgetResizable(True)
        self.task_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.task_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        self.task_container = QWidget()
        self.task_layout = QVBoxLayout(self.task_container)
        self.task_layout.setContentsMargins(5, 5, 5, 5)
        self.task_layout.setSpacing(5)

        self.task_scroll.setWidget(self.task_container)

    def create_button_area(self):
        """创建按钮区域"""
        self.button_frame = QFrame()
        button_layout = QHBoxLayout(self.button_frame)
        button_layout.setContentsMargins(5, 5, 5, 5)
        button_layout.setSpacing(8)

        # 添加任务按钮
        self.add_task_btn = QPushButton("添加任务")
        self.add_task_btn.clicked.connect(self.show_add_task_dialog)
        button_layout.addWidget(self.add_task_btn)

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_tasks)
        button_layout.addWidget(self.refresh_btn)

        # 强制同步按钮
        self.sync_btn = QPushButton("同步")
        self.sync_btn.clicked.connect(self.force_sync_tasks)
        self.sync_btn.setToolTip("强制同步主应用数据")
        button_layout.addWidget(self.sync_btn)

        # 打开应用按钮
        self.open_app_btn = QPushButton("打开应用")
        self.open_app_btn.clicked.connect(self.open_main_app)
        button_layout.addWidget(self.open_app_btn)

    def setup_window_properties(self):
        """设置窗口属性"""
        # 加载保存的显示模式设置
        saved_display_mode = self.config_manager.get('desktop_widget.display_mode', '全显示')
        self.current_display_mode = saved_display_mode

        # 根据显示模式设置窗口标志
        self.set_display_mode(saved_display_mode)

        # 加载保存的窗口尺寸，或使用默认值（1.5倍原始尺寸）
        saved_width = self.config_manager.get('desktop_widget.window_width', 525)  # 350 * 1.5 = 525
        saved_height = self.config_manager.get('desktop_widget.window_height', 600)  # 400 * 1.5 = 600

        # 设置窗口尺寸限制（也按1.5倍调整）
        self.setMinimumSize(420, 300)  # 最小尺寸：280*1.5=420, 200*1.5=300
        self.setMaximumSize(1200, 1500)  # 最大尺寸：800*1.5=1200, 1000*1.5=1500

        # 设置窗口大小
        self.resize(saved_width, saved_height)

        # 加载保存的窗口位置，或使用默认位置（右上角）
        saved_x = self.config_manager.get('desktop_widget.window_x', None)
        saved_y = self.config_manager.get('desktop_widget.window_y', None)

        if saved_x is not None and saved_y is not None:
            self.move(saved_x, saved_y)
        else:
            # 默认位置（右上角）
            screen = QApplication.primaryScreen().availableGeometry()
            x_pos = screen.width() - saved_width - 20
            y_pos = 50
            self.move(x_pos, y_pos)



    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            DesktopTaskWidget {
                background: #D8ECF4;
                border: 2px solid #3a5a7b;
                border-radius: 10px;
            }
            QFrame {
                background: transparent;
                border: none;
            }
            QLabel {
                color: black;
                background: transparent;
            }
            QPushButton {
                background: #FEF0C7;
                border: 1px solid #5a7a9b;
                border-radius: 5px;
                padding: 6px 12px;
                color: black;
                font-weight: bold;
                font-size: 9pt;
            }
            QPushButton:hover {
                background: #F5E6B8;
                border: 1px solid #6a8aab;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3a5a7b, stop:1 #2a4a6b);
            }
            QComboBox {
                background: #3a5a7b;
                border: 1px solid #4a6a8b;
                border-radius: 4px;
                padding: 2px 5px;
                color: #E0E0E0;
                font-size: 8pt;
            }
            QComboBox:focus {
                border: 2px solid #5a7a9b;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                color: black;
                border: 1px solid #4a6a8b;
                border-radius: 4px;
                selection-background-color: #3498db;
                selection-color: white;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                padding: 3px;
                border: none;
                background-color: white;
                color: black;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #3498db;
                color: white;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e3f2fd;
                color: black;
            }
            QScrollArea {
                background: #FEF0C7;
                border: 1px solid #3a5a7b;
                border-radius: 5px;
            }
            QScrollBar:vertical {
                background: #2a4a6b;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #4a6a8b;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #5a7a9b;
            }
        """)

        # 日期标签特殊样式
        self.date_label.setStyleSheet("""
            color: black;
            background: #FCE9DB;
            border: 1px solid #4a6a8b;
            border-radius: 6px;
            padding: 8px;
        """)

        # 标题栏按钮样式
        button_style = """
            QPushButton {
                background: #4a6a8b;
                border: 1px solid #5a7a9b;
                border-radius: 10px;
                color: #E0E0E0;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background: #5a7a9b;
            }
        """
        self.minimize_btn.setStyleSheet(button_style)
        self.close_btn.setStyleSheet(button_style + "QPushButton:hover { background: #CC4444; }")

    def load_tasks(self):
        """加载当日任务"""
        try:
            # 确保任务服务已初始化
            if self.task_service is None:
                self.task_service = get_task_service()

            # 获取当日所有任务 - 减少查询数量
            all_tasks = self.task_service.get_tasks(limit=100)  # 减少到100个任务

            # 按分类整理任务
            self.tasks_by_category = {
                '上午': [],
                '下午': [],
                '晚上': [],
                '全天': [],
                '其他': [],
                '日总结': []
            }

            for task in all_tasks:
                # 检查任务是否是今天的
                task_date = None
                if task.start_date:
                    task_date = task.start_date.date()
                elif task.due_date:
                    task_date = task.due_date.date()

                if task_date == self.current_date:
                    # 确定任务分类
                    category = self.determine_task_category(task)

                    # 跳过周计划任务（category为None）
                    if category is None:
                        continue

                    # 转换任务数据
                    task_dict = {
                        'id': task.id,
                        'title': task.title,
                        'description': task.description,
                        'status': self.convert_status(task.status),
                        'priority': self.convert_priority(task.priority),
                        'category': category
                    }

                    self.tasks_by_category[category].append(task_dict)

            # 加载日总结数据
            self.load_daily_summary()
            self.update_task_display()

        except Exception as e:

            import traceback
            traceback.print_exc()

    def on_summary_changed(self, summary_content: str):
        """处理日总结内容改变"""
        try:
            if self.daily_summary_service is None:
                print("日总结服务未初始化")
                return

            # 保存日总结到数据库
            success = self.daily_summary_service.save_daily_summary(self.current_date, summary_content)
            if success:


                # 更新本地缓存
                if summary_content.strip():
                    # 更新或添加日总结到任务分类中
                    summary_dict = {
                        'id': f'summary_{self.current_date}',
                        'title': '日总结',
                        'description': summary_content,
                        'status': '已完成',
                        'priority': '普通',
                        'category': '日总结',
                        'is_summary': True
                    }

                    # 清除旧的日总结并添加新的
                    self.tasks_by_category['日总结'] = [task for task in self.tasks_by_category['日总结'] if not task.get('is_summary', False)]
                    self.tasks_by_category['日总结'].append(summary_dict)
                else:
                    # 如果内容为空，移除日总结
                    self.tasks_by_category['日总结'] = [task for task in self.tasks_by_category['日总结'] if not task.get('is_summary', False)]
            else:
                # 如果没有日总结分类，不做任何操作
                pass

        except Exception as e:
            print(f"保存日总结失败: {e}")

    def load_daily_summary(self):
        """加载当日总结"""
        try:
            if self.daily_summary_service is None:
                return

            # 获取当日总结
            summary_content = self.daily_summary_service.get_daily_summary(self.current_date)
            if summary_content:
                # 将日总结作为特殊任务添加到日总结分类
                summary_dict = {
                    'id': f'summary_{self.current_date}',
                    'title': '日总结',
                    'description': summary_content,
                    'status': '已完成',
                    'priority': '普通',
                    'category': '日总结',
                    'is_summary': True  # 标记为总结类型
                }
                self.tasks_by_category['日总结'].append(summary_dict)
        except Exception as e:
            pass

    def determine_task_category(self, task) -> str:
        """确定任务分类"""
        if task.tags:
            tags = task.tags.lower()
            if '日总结' in tags or '总结' in tags:
                return '日总结'
            elif '周计划' in tags:
                return None  # 周计划任务不在小组件中显示
            elif '上午' in tags:
                return '上午'
            elif '下午' in tags:
                return '下午'
            elif '晚上' in tags:
                return '晚上'
            elif '全天' in tags:
                return '全天'

        return '其他'

    def convert_status(self, status) -> str:
        """转换状态"""
        status_map = {
            'pending': '待处理',
            'in_progress': '进行中',
            'completed': '已完成',
            'cancelled': '已取消'
        }
        if hasattr(status, 'value'):
            return status_map.get(status.value, '待处理')
        return status_map.get(status, '待处理')

    def convert_priority(self, priority) -> str:
        """转换优先级"""
        priority_map = {
            'low': '低',
            'normal': '普通',
            'high': '高',
            'urgent': '紧急'
        }
        if hasattr(priority, 'value'):
            return priority_map.get(priority.value, '普通')
        return priority_map.get(priority, '普通')

    def update_task_display(self):
        """更新任务显示"""


        # 暂时禁用窗口交互，防止在UI重建过程中出现异常
        self.setEnabled(False)

        try:
            # 清除现有显示
            for i in reversed(range(self.task_layout.count())):
                child = self.task_layout.itemAt(i).widget()
                if child:
                    # 立即删除而不是延迟删除，避免用户点击已删除的组件
                    child.setParent(None)
                    child.deleteLater()

            # 强制处理待删除的组件
            QApplication.processEvents()

            # 显示各分类任务 - 所有分类都显示，即使为空
            categories = ['上午', '下午', '晚上', '全天', '其他', '日总结']
            for category in categories:
                tasks = self.tasks_by_category.get(category, [])
                category_widget = CategoryTasksWidget(category, tasks)

                category_widget.task_clicked.connect(self.on_task_clicked)
                category_widget.add_task_requested.connect(self.show_add_task_dialog_for_category)
                category_widget.summary_changed.connect(self.on_summary_changed)

                self.task_layout.addWidget(category_widget)

            # 添加弹性空间
            self.task_layout.addStretch()

            # 调整窗口高度
            self.adjust_window_height()



        finally:
            # 重新启用窗口交互
            self.setEnabled(True)

    def adjust_window_height(self):
        """调整窗口高度"""
        # 如果用户正在调整大小，不要自动调整高度
        if self.resize_mode:
            return

        # 计算内容高度
        content_height = 0
        for i in range(self.task_layout.count()):
            item = self.task_layout.itemAt(i)
            if item.widget():
                content_height += item.widget().sizeHint().height()

        # 加上其他组件高度
        total_height = content_height + 150  # 标题栏、日期、按钮等

        # 获取当前窗口宽度，保持不变
        current_width = self.width()

        # 限制高度范围，但不强制设置固定高度
        min_height = max(200, total_height)
        max_height = min(1000, total_height + 200)  # 允许一些额外空间

        # 只有当当前高度不在合理范围内时才调整
        current_height = self.height()
        if current_height < min_height:
            self.resize(current_width, min_height)
        elif current_height > max_height:
            self.resize(current_width, max_height)

    def refresh_tasks(self):
        """刷新任务"""
        try:
            # 防止频繁刷新
            import time
            if hasattr(self, '_last_refresh_time'):
                time_since_last = time.time() - self._last_refresh_time
                if time_since_last < 1.0:  # 1秒内不重复刷新
                    return

            self._last_refresh_time = time.time()
            self.current_date = date.today()
            self.load_tasks()

            # 更新日期显示
            today = datetime.now()
            weekdays = ['一', '二', '三', '四', '五', '六', '日']
            weekday = weekdays[today.weekday()]
            date_text = f"{today.year}年{today.month:02d}月{today.day:02d}日 星期{weekday}"
            self.date_label.setText(date_text)

        except Exception as e:

            import traceback
            traceback.print_exc()

    def force_sync_tasks(self):
        """强制同步任务数据"""
        try:


            # 重新创建TaskService实例以确保获取最新数据
            self.task_service = get_task_service()

            # 刷新任务
            self.refresh_tasks()

            # 显示同步完成提示
            print("任务数据已强制同步")

        except Exception as e:
            print(f"强制同步失败: {e}")

    def show_add_task_dialog(self):
        """显示添加任务对话框"""
        dialog = AddTaskDialog(self)
        dialog.task_created.connect(self.create_task)
        dialog.exec()

    def show_add_task_dialog_for_category(self, category: str):
        """为特定分类显示添加任务对话框"""
        dialog = AddTaskDialog(self, default_category=category)
        dialog.task_created.connect(self.create_task)
        dialog.exec()

    def create_task(self, task_data: Dict):
        """创建新任务"""
        try:
            # 转换分类为tags
            category = task_data.get('category', '其他')
            tags = [category]  # 修复：将分类作为列表传递

            # 转换优先级
            priority_map = {
                '低': 'low',
                '普通': 'normal',
                '高': 'high',
                '紧急': 'urgent'
            }
            priority = priority_map.get(task_data.get('priority', '普通'), 'normal')

            # 创建任务
            task = self.task_service.create_task(
                title=task_data['title'],
                description=task_data.get('description', ''),
                priority=priority,
                start_date=task_data.get('start_date', datetime.now()),
                tags=tags
            )

            if task:
                QMessageBox.information(self, "成功", "任务创建成功！")
                self.refresh_tasks()
            else:
                QMessageBox.warning(self, "错误", "任务创建失败！")

        except Exception as e:
            print(f"创建任务失败: {e}")
            QMessageBox.critical(self, "错误", f"创建任务失败: {str(e)}")

    def open_main_app(self):
        """打开主应用"""
        try:
            # 检查主应用是否已经在运行
            if self.is_main_app_running():
                # 尝试激活已有的应用窗口
                self.activate_main_app_window()
                return

            # 获取主应用路径
            current_dir = Path(__file__).parent.parent.parent.parent
            main_py = current_dir / "main.py"

            if main_py.exists():
                import subprocess
                subprocess.Popen([sys.executable, str(main_py)],
                               cwd=str(current_dir))
            else:
                QMessageBox.warning(self, "错误", "找不到主应用程序")

        except Exception as e:
            print(f"打开主应用失败: {e}")

    def is_main_app_running(self):
        """检查主应用是否正在运行"""
        try:
            # 首先检查锁文件
            lock_file = Path.home() / ".personal_manager_lock"

            if lock_file.exists():
                return True

            # 如果锁文件不存在，尝试使用psutil检测
            try:
                import psutil
                import os

                current_process_name = "python.exe"
                main_py_path = str(Path(__file__).parent.parent.parent.parent / "main.py")

                # 遍历所有进程
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'] and current_process_name in proc.info['name'].lower():
                            cmdline = proc.info['cmdline']
                            if cmdline and len(cmdline) > 1:
                                # 检查命令行参数中是否包含main.py
                                for arg in cmdline:
                                    if 'main.py' in arg and main_py_path in arg:
                                        return True
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        continue

                return False

            except ImportError:
                return False

        except Exception:
            return False

    def activate_main_app_window(self):
        """激活主应用窗口"""
        try:
            if sys.platform == "win32":
                import win32gui
                import win32con

                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)
                        if "Personal Manager" in window_title:
                            windows.append(hwnd)
                    return True

                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)

                if windows:
                    # 激活找到的第一个窗口
                    hwnd = windows[0]
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                    win32gui.SetForegroundWindow(hwnd)
                    return True

            # 如果是其他平台或者没有找到窗口，显示提示
            QMessageBox.information(self, "提示", "主应用已在运行，请查看任务栏")
            return False

        except ImportError:
            # 如果没有win32gui，显示提示
            QMessageBox.information(self, "提示", "主应用已在运行，请查看任务栏")
            return False
        except Exception as e:
            print(f"激活主应用窗口失败: {e}")
            QMessageBox.information(self, "提示", "主应用已在运行，请查看任务栏")
            return False
            QMessageBox.critical(self, "错误", f"打开主应用失败: {str(e)}")

    def on_task_clicked(self, task_data: Dict):
        """任务被点击 - 处理任务更新或删除"""
        try:
            # 检查是否是删除操作
            if task_data.get('action') == 'deleted':
                # 刷新任务列表
                self.refresh_tasks()
                return

            # 普通任务更新，延迟刷新避免卡顿
            QTimer.singleShot(500, self.refresh_tasks)  # 延迟500ms刷新

        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "错误", f"处理任务失败: {e}")



    def on_close_button_clicked(self):
        """关闭按钮点击事件"""

        self.close()

    def check_show_command(self):
        """检查是否有显示命令"""
        try:
            from pathlib import Path
            command_file = Path(__file__).parent.parent.parent.parent.parent / "source_code" / "widget_show_command.txt"

            if command_file.exists():
                with open(command_file, 'r', encoding='utf-8') as f:
                    command = f.read().strip()

                # 删除命令文件
                command_file.unlink()

                if command == "show":

                    self.show_widget()

        except Exception as e:
            # 静默处理错误，避免日志噪音
            pass

    def show_widget(self):
        """显示小部件"""
        self.show()
        self.raise_()
        self.activateWindow()

    def on_display_mode_changed(self, mode: str):
        """显示模式改变事件处理"""
        self.current_display_mode = mode
        self.set_display_mode(mode)

        # 保存设置
        self.config_manager.set('desktop_widget.display_mode', mode)
        self.config_manager.save()

        print(f"显示模式已切换为: {mode}")

    def set_display_mode(self, mode: str):
        """设置显示模式"""
        if mode == "全显示":
            # 全显示模式：始终置顶
            flags = (Qt.WindowType.FramelessWindowHint |
                    Qt.WindowType.WindowStaysOnTopHint |
                    Qt.WindowType.Tool)
        else:  # 半显示
            # 半显示模式：可被其他应用覆盖
            flags = (Qt.WindowType.FramelessWindowHint |
                    Qt.WindowType.Tool)

        # 保存当前位置和大小
        current_geometry = self.geometry()
        was_visible = self.isVisible()

        # 设置新的窗口标志
        self.setWindowFlags(flags)

        # 恢复位置和大小
        self.setGeometry(current_geometry)

        # 如果之前是可见的，重新显示
        if was_visible:
            self.show()

    def quit_application(self):
        """退出应用"""
        QApplication.quit()

    def save_window_geometry(self):
        """保存窗口几何信息"""
        try:
            geometry = self.geometry()
            self.config_manager.set('desktop_widget.window_width', geometry.width(), save=False)
            self.config_manager.set('desktop_widget.window_height', geometry.height(), save=False)
            self.config_manager.set('desktop_widget.window_x', geometry.x(), save=False)
            self.config_manager.set('desktop_widget.window_y', geometry.y(), save=False)
            self.config_manager.save()
        except Exception as e:
            pass  # 静默处理保存错误

    def get_resize_mode(self, pos: QPoint) -> str:
        """根据鼠标位置确定调整大小模式"""
        rect = self.rect()
        x, y = pos.x(), pos.y()

        # 检查是否在角落
        if x <= self.resize_corner_size and y <= self.resize_corner_size:
            return 'top-left'
        elif x >= rect.width() - self.resize_corner_size and y <= self.resize_corner_size:
            return 'top-right'
        elif x <= self.resize_corner_size and y >= rect.height() - self.resize_corner_size:
            return 'bottom-left'
        elif x >= rect.width() - self.resize_corner_size and y >= rect.height() - self.resize_corner_size:
            return 'bottom-right'

        # 检查是否在边缘
        elif x <= self.resize_border_width:
            return 'left'
        elif x >= rect.width() - self.resize_border_width:
            return 'right'
        elif y <= self.resize_border_width:
            return 'top'
        elif y >= rect.height() - self.resize_border_width:
            return 'bottom'

        return None

    def set_cursor_for_resize_mode(self, mode: str):
        """根据调整大小模式设置光标"""
        if mode == 'left' or mode == 'right':
            self.setCursor(Qt.CursorShape.SizeHorCursor)
        elif mode == 'top' or mode == 'bottom':
            self.setCursor(Qt.CursorShape.SizeVerCursor)
        elif mode == 'top-left' or mode == 'bottom-right':
            self.setCursor(Qt.CursorShape.SizeFDiagCursor)
        elif mode == 'top-right' or mode == 'bottom-left':
            self.setCursor(Qt.CursorShape.SizeBDiagCursor)
        else:
            self.setCursor(Qt.CursorShape.ArrowCursor)

    # 鼠标事件处理（支持拖拽和调整大小）
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            pos = event.position().toPoint()
            self.resize_mode = self.get_resize_mode(pos)

            if self.resize_mode:
                # 开始调整大小
                self.resize_start_pos = event.globalPosition().toPoint()
                self.resize_start_geometry = self.geometry()
            else:
                # 开始拖拽移动
                self.is_dragging = True
                self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        pos = event.position().toPoint()

        if event.buttons() == Qt.MouseButton.LeftButton:
            if self.resize_mode:
                # 处理调整大小
                self.handle_resize(event.globalPosition().toPoint())
            elif self.is_dragging:
                # 处理拖拽移动
                self.move(event.globalPosition().toPoint() - self.drag_position)
        else:
            # 鼠标悬停时更新光标
            mode = self.get_resize_mode(pos)
            self.set_cursor_for_resize_mode(mode)

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            if self.resize_mode:
                # 结束调整大小，保存窗口几何
                self.resize_mode = None
                self.save_window_geometry()
            elif self.is_dragging:
                # 结束拖拽移动，保存窗口位置
                self.is_dragging = False
                self.save_window_geometry()
        super().mouseReleaseEvent(event)

    def handle_resize(self, global_pos: QPoint):
        """处理窗口调整大小"""
        if not self.resize_mode or not self.resize_start_geometry:
            return

        # 计算鼠标移动的偏移量
        delta = global_pos - self.resize_start_pos
        dx, dy = delta.x(), delta.y()

        # 获取原始几何信息
        orig_rect = self.resize_start_geometry
        new_x, new_y = orig_rect.x(), orig_rect.y()
        new_width, new_height = orig_rect.width(), orig_rect.height()

        # 根据调整模式计算新的几何信息
        if 'left' in self.resize_mode:
            new_x = orig_rect.x() + dx
            new_width = orig_rect.width() - dx
        elif 'right' in self.resize_mode:
            new_width = orig_rect.width() + dx

        if 'top' in self.resize_mode:
            new_y = orig_rect.y() + dy
            new_height = orig_rect.height() - dy
        elif 'bottom' in self.resize_mode:
            new_height = orig_rect.height() + dy

        # 应用尺寸限制
        min_size = self.minimumSize()
        max_size = self.maximumSize()

        new_width = max(min_size.width(), min(max_size.width(), new_width))
        new_height = max(min_size.height(), min(max_size.height(), new_height))

        # 如果调整左边缘或上边缘，需要重新计算位置以保持右下角不动
        if 'left' in self.resize_mode:
            new_x = orig_rect.x() + orig_rect.width() - new_width
        if 'top' in self.resize_mode:
            new_y = orig_rect.y() + orig_rect.height() - new_height

        # 应用新的几何信息
        self.setGeometry(new_x, new_y, new_width, new_height)

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 确保内容适应新的窗口大小
        self.update()

    def closeEvent(self, event):
        """关闭事件"""
        # 通知主应用端小组件已关闭
        self.notify_main_app_closed()
        event.accept()

    def notify_main_app_closed(self):
        """通知主应用端小组件已关闭"""
        try:
            # 创建状态文件，通知主应用端小组件已关闭
            # 修正路径：确保与 desktop_widget_controller.py 中的路径一致
            status_file = Path(__file__).parent.parent.parent.parent.parent / "widget_status.txt"

            # 确保父目录存在
            status_file.parent.mkdir(parents=True, exist_ok=True)

            with open(status_file, 'w', encoding='utf-8') as f:
                content = f"closed:{datetime.now().isoformat()}"
                f.write(content)
                f.flush()  # 强制刷新到磁盘

        except Exception as e:
            pass


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)  # 不在最后窗口关闭时退出

    # 设置应用信息
    app.setApplicationName("桌面任务小部件")
    app.setApplicationVersion("1.0.0")

    # 创建并显示小部件
    widget = DesktopTaskWidget()
    widget.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
