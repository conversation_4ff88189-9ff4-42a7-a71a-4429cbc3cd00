@echo off
chcp 65001 >nul
title 桌面任务小部件 - 卸载自启动

echo ========================================
echo 桌面任务小部件 - 卸载自启动
echo ========================================
echo.
echo 此脚本将移除桌面任务小部件的所有自启动配置
echo.

cd /d "%~dp0"

echo 正在卸载自启动配置...
echo.

echo 1. 移除注册表自启动...
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "桌面任务小部件" /f >nul 2>&1
if errorlevel 1 (
    echo   注册表项不存在或已移除
) else (
    echo   注册表自启动已移除
)

echo 2. 移除启动文件夹...
set startup_folder=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup
if exist "%startup_folder%\桌面任务小部件.bat" (
    del "%startup_folder%\桌面任务小部件.bat" >nul 2>&1
    echo   启动文件夹项已移除
) else (
    echo   启动文件夹项不存在
)

echo 3. 移除任务计划程序...
schtasks /delete /tn "桌面任务小部件" /f >nul 2>&1
if errorlevel 1 (
    echo   任务计划项不存在或已移除
) else (
    echo   任务计划程序项已移除
)

echo.
echo ========================================
echo 卸载完成！
echo ========================================
echo.
echo 所有自启动配置已移除
echo 桌面小部件将不再自动启动
echo.
echo 当前守护进程状态:
if exist "daemon.pid" (
    echo - 守护进程正在运行
    echo - 如需停止，请运行 stop_daemon.bat
) else (
    echo - 守护进程未运行
)
echo.
echo 如需重新安装自启动，请运行 一键安装自启动.bat
echo.
echo 按任意键退出...
pause >nul
