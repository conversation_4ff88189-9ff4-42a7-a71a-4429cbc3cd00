#!/usr/bin/env python3
"""Debug script to test disk information retrieval"""

import psutil
import traceback

def test_disk_info():
    """Test disk information retrieval"""
    print("Testing disk information retrieval...")
    
    try:
        # Get disk partitions
        partitions = psutil.disk_partitions()
        print(f"Found {len(partitions)} partitions:")
        
        for i, partition in enumerate(partitions):
            print(f"\nPartition {i+1}:")
            print(f"  Device: {repr(partition.device)}")
            print(f"  Mountpoint: {repr(partition.mountpoint)}")
            print(f"  File system: {repr(partition.fstype)}")
            print(f"  Options: {repr(partition.opts)}")
            
            try:
                # Test if we can access this partition
                if partition.mountpoint in ['', None] or not partition.mountpoint.strip():
                    print("  Status: Skipping - empty mountpoint")
                    continue
                
                # Skip certain file systems
                if partition.fstype in ['', 'squashfs', 'tmpfs', 'devtmpfs']:
                    print(f"  Status: Skipping - file system type {partition.fstype}")
                    continue
                
                # Use shutil.disk_usage as fallback for Windows
                import os
                import shutil
                mountpoint = partition.mountpoint
                print(f"  Using shutil.disk_usage for: {repr(mountpoint)}")

                # Try shutil.disk_usage first (more reliable on Windows)
                try:
                    usage = shutil.disk_usage(mountpoint)
                    partition_usage = type('DiskUsage', (), {
                        'total': usage.total,
                        'used': usage.total - usage.free,
                        'free': usage.free
                    })()
                except Exception as shutil_error:
                    print(f"  shutil.disk_usage failed: {shutil_error}")
                    # Fallback to psutil
                    partition_usage = psutil.disk_usage(mountpoint)
                
                if partition_usage.total == 0:
                    print("  Status: Skipping - total size is 0")
                    continue
                
                print(f"  Total: {partition_usage.total:,} bytes")
                print(f"  Used: {partition_usage.used:,} bytes")
                print(f"  Free: {partition_usage.free:,} bytes")
                print(f"  Percentage: {(partition_usage.used / partition_usage.total) * 100:.2f}%")
                print("  Status: OK")
                
            except Exception as e:
                print(f"  Status: Error - {type(e).__name__}: {e}")
                print(f"  Traceback: {traceback.format_exc()}")
                
    except Exception as e:
        print(f"Error getting partitions: {type(e).__name__}: {e}")
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    test_disk_info()
