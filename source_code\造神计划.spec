# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 项目路径
project_root = Path(r"c:\personal_manager_project\source_code")
src_path = project_root / "src"

# 添加源代码路径
sys.path.insert(0, str(src_path))

# 数据文件
datas = [
    (str(project_root / "src"), "src"),
    (str(project_root / "resources"), "resources"),
    (str(project_root / "config"), "config"),
    (str(project_root / "docs"), "docs"),
    (str(project_root / "scripts"), "scripts"),
    (str(project_root / "requirements.txt"), "."),
    (str(project_root / "universal_widget_daemon.py"), "."),
]

# 隐藏导入
hiddenimports = [
    'PyQt6',
    'PyQt6.QtCore',
    'PyQt6.QtWidgets',
    'PyQt6.QtGui',
    'sqlalchemy',
    'psutil',
    'cryptography',
    'keyring',
    'requests',
    'yaml',
    'watchdog',
    'send2trash',
    'apscheduler',
    'pandas',
    'openpyxl',
    'pillow',
    'tqdm',
    'click',
]

# 排除模块
excludes = [
    'matplotlib',
    'numpy',
    'scipy',
    'tkinter',
    'unittest',
    'test',
    'tests',
]

a = Analysis(
    [str(project_root / "main.py")],
    pathex=[str(project_root), str(src_path)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name="造神计划",
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / "resources" / "icon.ico") if (project_root / "resources" / "icon.ico").exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name="造神计划",
)
