"""
学习追踪悬浮小组件守护进程

负责管理学习追踪悬浮小组件的生命周期，包括：
- 启动和监控悬浮小组件
- 处理来自主应用的控制命令
- 崩溃检测和自动重启
"""

import os
import sys
import time
import signal
import argparse
import subprocess
import psutil
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from core.logger import setup_logging
from core.config import Config


class StudyFloatingDaemon:
    """学习追踪悬浮小组件守护进程"""
    
    def __init__(self, manual_start=False):
        self.manual_start = manual_start
        self.is_manual = manual_start
        self.running = True
        self.widget_process = None
        
        # 路径配置
        self.project_root = Path(__file__).parent
        self.widget_script = self.project_root / "start_study_floating_widget.py"
        self.pid_file = self.project_root / "study_floating_daemon.pid"
        self.command_file = self.project_root / "study_floating_command.txt"
        self.status_file = self.project_root.parent / "study_floating_status.txt"
        
        # 配置
        self.config = {
            "check_interval": 10,  # 检查间隔（秒）
            "auto_start_delay": 30,  # 自启动延迟（秒）
            "enable_crash_detection": True,  # 启用崩溃检测
            "max_restart_attempts": 3,  # 最大重启尝试次数
        }
        
        # 初始化日志
        try:
            config_obj = Config()
            setup_logging(config_obj)
            
            import logging
            self.logger = logging.getLogger(__name__)
            self.logger.info("学习追踪悬浮小组件守护进程初始化")
        except Exception as e:
            print(f"日志初始化失败: {e}")
            import logging
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger(__name__)
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def write_pid(self):
        """写入PID文件"""
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(os.getpid()))
            self.logger.info(f"PID文件已写入: {self.pid_file}")
        except Exception as e:
            self.logger.error(f"写入PID文件失败: {e}")
    
    def remove_pid(self):
        """删除PID文件"""
        try:
            if self.pid_file.exists():
                self.pid_file.unlink()
                self.logger.info("PID文件已删除")
        except Exception as e:
            self.logger.error(f"删除PID文件失败: {e}")
    
    def start_widget(self):
        """启动学习追踪悬浮小组件"""
        try:
            if self.widget_process and self.widget_process.poll() is None:
                self.logger.warning("学习追踪悬浮小组件已在运行")
                return True
            
            self.logger.info("启动学习追踪悬浮小组件...")
            
            # 启动小组件进程
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSFSENCODING'] = '0'
            if os.name == 'nt':
                env['PYTHONUTF8'] = '1'
            
            self.widget_process = subprocess.Popen(
                [sys.executable, str(self.widget_script)],
                cwd=str(self.project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            self.logger.info(f"学习追踪悬浮小组件已启动，PID: {self.widget_process.pid}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动学习追踪悬浮小组件失败: {e}")
            return False
    
    def stop_widget(self):
        """停止学习追踪悬浮小组件"""
        try:
            if self.widget_process and self.widget_process.poll() is None:
                self.logger.info("停止学习追踪悬浮小组件...")
                self.widget_process.terminate()
                
                try:
                    self.widget_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.widget_process.kill()
                    self.widget_process.wait()
                
                self.widget_process = None
                self.logger.info("学习追踪悬浮小组件已停止")
            
        except Exception as e:
            self.logger.error(f"停止学习追踪悬浮小组件失败: {e}")
    
    def check_widget_status(self) -> bool:
        """检查学习追踪悬浮小组件状态"""
        try:
            if self.widget_process is None:
                return False
            
            # 检查进程是否还在运行
            if self.widget_process.poll() is not None:
                self.logger.warning("学习追踪悬浮小组件进程已退出")
                self.widget_process = None
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查学习追踪悬浮小组件状态失败: {e}")
            return False
    
    def restart_widget(self):
        """重启学习追踪悬浮小组件"""
        self.logger.info("重启学习追踪悬浮小组件...")
        self.stop_widget()
        time.sleep(2)
        return self.start_widget()
    
    def run(self):
        """运行守护进程"""
        self.logger.info("学习追踪悬浮小组件守护进程启动")
        
        # 写入PID文件
        self.write_pid()
        
        # 开机启动延迟（仅在自动启动时使用）
        if self.manual_start:
            self.logger.info("手动启动，立即启动学习追踪悬浮小组件...")
        else:
            auto_start_delay = self.config.get("auto_start_delay", 30)
            boot_time = psutil.boot_time()
            current_time = time.time()
            time_since_boot = current_time - boot_time
            
            if time_since_boot < 300 and auto_start_delay > 0:
                self.logger.info(f"检测到开机启动，等待{auto_start_delay}秒后启动学习追踪悬浮小组件...")
                time.sleep(auto_start_delay)
            else:
                self.logger.info("自动启动，但系统已运行较长时间，立即启动学习追踪悬浮小组件...")
        
        # 启动学习追踪悬浮小组件
        if not self.start_widget():
            self.logger.error("初始启动失败，但继续运行以监听命令")
        
        # 主循环
        check_interval = self.config.get("check_interval", 10)
        
        while self.running:
            try:
                # 检查命令文件
                self.check_command_file()
                
                # 检查小组件状态
                if not self.check_widget_status():
                    # 检查是否是用户主动关闭
                    status_file = self.project_root.parent / "study_floating_status.txt"
                    user_closed = False

                    if status_file.exists():
                        try:
                            with open(status_file, 'r', encoding='utf-8') as f:
                                content = f.read().strip()
                            if content.startswith('closed:'):
                                user_closed = True
                                self.logger.info("检测到用户主动关闭悬浮小组件，不自动重启")
                                # 不删除状态文件，让主应用端控制器处理
                        except Exception as e:
                            self.logger.warning(f"检查状态文件失败: {e}")

                    if not user_closed:
                        self.logger.warning("检测到学习追踪悬浮小组件异常退出")

                        if self.config.get("enable_crash_detection", True):
                            self.restart_widget()
                    else:
                        # 用户主动关闭，停止守护进程
                        self.logger.info("用户主动关闭，停止守护进程")
                        self.running = False
                
                # 等待下次检查
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                self.logger.info("收到中断信号")
                break
            except Exception as e:
                self.logger.error(f"守护进程异常: {e}")
                time.sleep(check_interval)
        
        # 清理
        self.cleanup()
    
    def check_command_file(self):
        """检查命令文件是否有新的指令"""
        try:
            if self.command_file.exists():
                with open(self.command_file, 'r', encoding='utf-8') as f:
                    command = f.read().strip()
                
                # 删除命令文件
                self.command_file.unlink()
                
                if command == "show_widget":
                    self.logger.info("收到显示小组件命令")
                    self.show_widget_window()
                elif command == "restart_widget":
                    self.logger.info("收到重启小组件命令")
                    self.restart_widget()
                elif command == "manual_takeover":
                    self.logger.info("收到手动接管命令，停止自启动流程并重启")
                    self.manual_takeover()
                    
        except Exception as e:
            self.logger.error(f"检查命令文件失败: {e}")
    
    def manual_takeover(self):
        """手动接管：停止自启动流程并重新启动小组件"""
        try:
            self.logger.info("开始手动接管流程...")
            
            # 停止当前小组件进程
            if self.widget_process and self.widget_process.poll() is None:
                self.logger.info("停止当前小组件进程")
                try:
                    self.widget_process.terminate()
                    self.widget_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.widget_process.kill()
                    self.widget_process.wait()
                except Exception as e:
                    self.logger.error(f"停止小组件进程失败: {e}")
                finally:
                    self.widget_process = None
            
            # 标记为手动模式
            self.is_manual = True
            self.logger.info("切换到手动模式")
            
            # 立即重新启动小组件
            self.logger.info("手动模式下重新启动小组件...")
            self.start_widget()
            
        except Exception as e:
            self.logger.error(f"手动接管失败: {e}")
    
    def show_widget_window(self):
        """显示小组件窗口"""
        try:
            if self.widget_process and self.widget_process.poll() is None:
                # 小组件进程正在运行，尝试通过信号让它显示
                self.logger.info("尝试显示已运行的小组件窗口")
                
                # 创建显示命令文件，让小组件进程读取
                widget_command_file = self.project_root / "study_floating_show_command.txt"
                with open(widget_command_file, 'w', encoding='utf-8') as f:
                    f.write("show")
                
                self.logger.info("已发送显示命令给小组件")
            else:
                # 小组件进程未运行，重新启动
                self.logger.info("小组件未运行，重新启动")
                self.start_widget()
                
        except Exception as e:
            self.logger.error(f"显示小组件窗口失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("清理守护进程资源...")
        
        # 停止学习追踪悬浮小组件
        self.stop_widget()
        
        # 删除PID文件
        self.remove_pid()
        
        # 删除命令文件
        try:
            if self.command_file.exists():
                self.command_file.unlink()
        except Exception as e:
            self.logger.error(f"删除命令文件失败: {e}")
        
        self.logger.info("学习追踪悬浮小组件守护进程已退出")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}")
        self.running = False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="学习追踪悬浮小组件守护进程")
    parser.add_argument("--manual", action="store_true", help="手动启动模式")
    args = parser.parse_args()
    
    daemon = StudyFloatingDaemon(manual_start=args.manual)
    daemon.run()


if __name__ == "__main__":
    main()
