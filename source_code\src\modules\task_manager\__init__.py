"""
Task Manager Module for Personal Manager System

This module provides task management functionality including:
- Task creation, editing, and deletion
- Task scheduling and reminders
- Task categories and priorities
- Task templates and recurring tasks
- Task progress tracking and reporting

COMPLETELY REDESIGNED with new weekly interface featuring:
- Week and date display with system calendar integration
- Weekly task table with inline editing capabilities
- Enhanced user experience with intuitive task management
- Direct cell editing and task manipulation
"""

from .task_service import TaskService

# Import GUI components only when needed
try:
    # MAIN: Import the new unified task manager widget - 造神计划
    from .unified_task_manager import UnifiedTaskManagerWidget as TaskManagerWidget

    # Strategy pattern components
    from .strategies import (
        ThemeStrategy, DefaultTheme, LakeBlueTheme, DarkTheme,
        ViewStrategy, WeeklyView, DailyView, ListView
    )

    # Legacy components (kept for backward compatibility)
    from .weekly_task_manager_widget import WeeklyTaskManagerWidget as LegacyWeeklyTaskManagerWidget
    from .weekly_task_table import WeeklyTaskTable
    from .date_week_display import DateWeekDisplay

    # Lake Blue theme components (available for reference)
    from .lake_blue_schedule_widget import (
        <PERSON><PERSON><PERSON>ScheduleWidget, <PERSON><PERSON><PERSON><PERSON>heme as LegacyLakeBlue<PERSON>hem<PERSON>, StatusBar,
        WeekNavigationBar, MainSchedulePanel, SummaryPanel
    )
    from .lake_blue_task_manager_widget import LakeBlueTaskManagerWidget

    # DEPRECATED: Keep old widgets available for fallback/reference only
    from .new_task_manager_widget import NewTaskManagerWidget as DeprecatedNewTaskManagerWidget
    from .task_manager_widget import TaskManagerWidget as DeprecatedOldTaskManagerWidget
    from .editable_task_table import EditableTaskTable as DeprecatedEditableTaskTable

    __all__ = [
        'TaskManagerWidget',      # MAIN: UnifiedTaskManagerWidget - 造神计划
        'TaskService',
        # Strategy pattern components
        'ThemeStrategy', 'DefaultTheme', 'LakeBlueTheme', 'DarkTheme',
        'ViewStrategy', 'WeeklyView', 'DailyView', 'ListView',
        # Legacy components
        'LegacyWeeklyTaskManagerWidget',
        'WeeklyTaskTable',
        'DateWeekDisplay',
        # Lake Blue theme components
        'LakeBlueScheduleWidget',
        'LegacyLakeBlueTheme',
        'LakeBlueTaskManagerWidget',
        # Deprecated components (for fallback only)
        'DeprecatedNewTaskManagerWidget',
        'DeprecatedOldTaskManagerWidget',
        'DeprecatedEditableTaskTable'
    ]
except ImportError:
    # PyQt6 not available, only export service
    __all__ = ['TaskService']