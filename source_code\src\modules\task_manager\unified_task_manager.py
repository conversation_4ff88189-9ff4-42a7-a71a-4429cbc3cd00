"""
Unified Task Manager Widget

This is the new unified task manager that replaces all previous task manager
implementations. It uses strategy patterns for themes and views, providing
a flexible and maintainable solution.
"""

from datetime import datetime, date
from typing import List, Dict, Optional, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QComboBox, QFrame, QMessageBox, QToolBar, QStatusBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon

from core.logger import LoggerMixin
from core.config import ConfigManager
from .task_service import TaskService
from .strategies.theme_strategy import ThemeStrategy, DefaultTheme, LakeBlueTheme, DarkTheme
from .strategies.view_strategy import ViewStrategy, WeeklyView, DailyView, ListView


class UnifiedTaskManagerWidget(QWidget):
    """统一任务管理器 - 造神计划"""

    # 信号
    task_updated = pyqtSignal(str, dict)  # task_id, updated_data
    task_deleted = pyqtSignal(str)  # task_id

    def __init__(self, parent=None, theme_name: str = "lake_blue", view_name: str = "weekly"):
        super().__init__(parent)

        # 添加日志功能
        self._logger_mixin = LoggerMixin()

        # 初始化服务和配置
        self.task_service = TaskService()
        self.config_manager = ConfigManager()

    @property
    def logger(self):
        """获取日志器"""
        return self._logger_mixin.logger
        
        # 数据
        self.current_tasks = []
        self.categories = []
        
        # 策略模式组件
        self.theme_strategy = self._create_theme_strategy(theme_name)
        self.view_strategy = self._create_view_strategy(view_name)
        
        # 初始化界面
        self.init_ui()
        self.apply_theme()
        self.connect_signals()
        self.load_data()
        
        # 设置定时刷新
        self.setup_auto_refresh()
        
        self.logger.info(f"Unified task manager initialized with theme: {theme_name}, view: {view_name}")
    
    def _create_theme_strategy(self, theme_name: str) -> ThemeStrategy:
        """创建主题策略"""
        theme_map = {
            'default': DefaultTheme,
            'lake_blue': LakeBlueTheme,
            'dark': DarkTheme
        }
        
        theme_class = theme_map.get(theme_name, LakeBlueTheme)
        return theme_class()
    
    def _create_view_strategy(self, view_name: str) -> ViewStrategy:
        """创建视图策略"""
        view_map = {
            'weekly': WeeklyView,
            'daily': DailyView,
            'list': ListView
        }
        
        view_class = view_map.get(view_name, WeeklyView)
        return view_class(self)
    
    def init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建标题栏
        title_layout = self.create_title_bar()
        main_layout.addLayout(title_layout)
        
        # 创建工具栏
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)
        
        # 创建主要内容区域（使用视图策略）
        content_layout = self.view_strategy.create_layout()
        main_layout.addLayout(content_layout)
        
        # 创建状态栏
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
        
        # 设置窗口属性
        self.setWindowTitle("造神计划 - 任务管理")
        self.setMinimumSize(1000, 700)
    
    def create_title_bar(self) -> QHBoxLayout:
        """创建标题栏"""
        title_layout = QHBoxLayout()
        
        # 应用标题
        title_label = QLabel("🚀 造神计划 - 任务管理系统")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 主题切换
        theme_label = QLabel("主题:")
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["湖面蓝", "默认", "深色"])
        self.theme_combo.setCurrentText("湖面蓝")
        self.theme_combo.currentTextChanged.connect(self.change_theme)
        
        # 视图切换
        view_label = QLabel("视图:")
        self.view_combo = QComboBox()
        self.view_combo.addItems(["周视图", "日视图", "列表视图"])
        self.view_combo.setCurrentText("周视图")
        self.view_combo.currentTextChanged.connect(self.change_view)
        
        title_layout.addWidget(theme_label)
        title_layout.addWidget(self.theme_combo)
        title_layout.addWidget(view_label)
        title_layout.addWidget(self.view_combo)
        
        return title_layout
    
    def create_toolbar(self) -> QFrame:
        """创建工具栏"""
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # 主要操作按钮
        self.new_task_btn = QPushButton("➕ 新建任务")
        self.refresh_btn = QPushButton("🔄 刷新")
        self.export_btn = QPushButton("📤 导出")
        self.settings_btn = QPushButton("⚙️ 设置")
        
        # 连接信号
        self.new_task_btn.clicked.connect(self.create_new_task)
        self.refresh_btn.clicked.connect(self.refresh_data)
        self.export_btn.clicked.connect(self.export_tasks)
        self.settings_btn.clicked.connect(self.show_settings)
        
        toolbar_layout.addWidget(self.new_task_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.export_btn)
        toolbar_layout.addWidget(self.settings_btn)
        toolbar_layout.addStretch()
        
        # 桌面小组件控制
        self.widget_toggle_btn = QPushButton("🖥️ 启动桌面小组件")
        self.widget_toggle_btn.clicked.connect(self.toggle_desktop_widget)
        toolbar_layout.addWidget(self.widget_toggle_btn)
        
        return toolbar_frame
    
    def create_status_bar(self) -> QFrame:
        """创建状态栏"""
        status_frame = QFrame()
        status_layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("就绪")
        self.task_count_label = QLabel("任务: 0")
        self.widget_status_label = QLabel("桌面小组件: 未启动")
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.task_count_label)
        status_layout.addWidget(QLabel("|"))
        status_layout.addWidget(self.widget_status_label)
        
        return status_frame
    
    def apply_theme(self):
        """应用当前主题"""
        self.theme_strategy.apply_styles(self)
    
    def change_theme(self, theme_text: str):
        """切换主题"""
        theme_map = {
            "湖面蓝": "lake_blue",
            "默认": "default", 
            "深色": "dark"
        }
        
        theme_name = theme_map.get(theme_text, "lake_blue")
        self.theme_strategy = self._create_theme_strategy(theme_name)
        self.apply_theme()
        
        # 保存主题设置
        self.config_manager.set('task_manager.theme', theme_name)
        self.config_manager.save()
        
        self.logger.info(f"Theme changed to: {theme_name}")
    
    def change_view(self, view_text: str):
        """切换视图"""
        view_map = {
            "周视图": "weekly",
            "日视图": "daily",
            "列表视图": "list"
        }
        
        view_name = view_map.get(view_text, "weekly")
        
        # 重新创建视图策略
        self.view_strategy = self._create_view_strategy(view_name)
        
        # 重新构建界面
        self.rebuild_content_area()
        
        # 保存视图设置
        self.config_manager.set('task_manager.view', view_name)
        self.config_manager.save()
        
        self.logger.info(f"View changed to: {view_name}")
    
    def rebuild_content_area(self):
        """重新构建内容区域"""
        # 这里需要重新构建主要内容区域
        # 简化实现：重新加载数据
        self.load_data()
    
    def setup_auto_refresh(self):
        """设置自动刷新"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # 每分钟刷新一次
    
    def connect_signals(self):
        """连接信号"""
        # 连接视图策略的信号（如果有的话）
        pass
    
    def load_data(self):
        """加载数据"""
        try:
            # 加载分类
            self.categories = self.task_service.get_categories()
            
            # 加载任务
            all_tasks = self.task_service.get_tasks(limit=1000)
            self.current_tasks = all_tasks
            
            # 使用视图策略加载数据
            task_dicts = [self._task_to_dict(task) for task in all_tasks]
            category_dicts = [self._category_to_dict(cat) for cat in self.categories]
            
            self.view_strategy.load_data(task_dicts, category_dicts)
            
            # 更新状态
            self.update_status()
            
            self.logger.info("Task manager data loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load task manager data: {e}")
            self.status_label.setText(f"加载失败: {str(e)}")
    
    def refresh_data(self):
        """刷新数据"""
        self.load_data()
        self.view_strategy.refresh_view()
        self.status_label.setText("数据已刷新")
    
    def update_status(self):
        """更新状态显示"""
        task_count = len(self.current_tasks)
        self.task_count_label.setText(f"任务: {task_count}")
    
    def create_new_task(self):
        """创建新任务"""
        try:
            # 这里可以复用现有的任务对话框
            from .weekly_task_manager_widget import TaskEditDialog
            
            dialog = TaskEditDialog(categories=self.categories, parent=self)
            if dialog.exec() == dialog.DialogCode.Accepted:
                task_data = dialog.get_task_data()
                
                if not task_data['title']:
                    QMessageBox.warning(self, "错误", "任务标题不能为空")
                    return
                
                # 创建任务
                task = self.task_service.create_task(
                    title=task_data['title'],
                    description=task_data['description'],
                    category_id=None,
                    priority=task_data['priority'],
                    due_date=datetime.combine(task_data['due_date'], datetime.min.time()),
                    start_date=datetime.combine(task_data['date'], datetime.min.time()),
                    tags=task_data.get('tags', [])
                )
                
                if task:
                    self.refresh_data()
                    self.status_label.setText("任务创建成功")
                else:
                    QMessageBox.warning(self, "错误", "任务创建失败")
        
        except ImportError:
            QMessageBox.information(self, "提示", "任务创建对话框暂不可用")
        except Exception as e:
            self.logger.error(f"Error creating task: {e}")
            QMessageBox.critical(self, "错误", f"创建任务时发生错误: {str(e)}")
    
    def export_tasks(self):
        """导出任务"""
        QMessageBox.information(self, "提示", "导出功能开发中...")
    
    def show_settings(self):
        """显示设置"""
        QMessageBox.information(self, "提示", "设置功能开发中...")
    
    def toggle_desktop_widget(self):
        """切换桌面小组件"""
        try:
            from ..common.widget_controller import UnifiedWidgetController
            
            if not hasattr(self, 'widget_controller'):
                self.widget_controller = UnifiedWidgetController("desktop_task")
                self.widget_controller.status_changed.connect(self.on_widget_status_changed)
                self.widget_controller.error_occurred.connect(self.on_widget_error)
            
            if self.widget_controller.is_running():
                self.widget_controller.stop_widget()
            else:
                self.widget_controller.start_widget()
        
        except Exception as e:
            self.logger.error(f"Error toggling desktop widget: {e}")
            QMessageBox.warning(self, "错误", f"桌面小组件操作失败: {str(e)}")
    
    def on_widget_status_changed(self, is_running: bool):
        """桌面小组件状态变化"""
        if is_running:
            self.widget_toggle_btn.setText("🖥️ 停止桌面小组件")
            self.widget_status_label.setText("桌面小组件: 运行中")
        else:
            self.widget_toggle_btn.setText("🖥️ 启动桌面小组件")
            self.widget_status_label.setText("桌面小组件: 已停止")
    
    def on_widget_error(self, error_msg: str):
        """桌面小组件错误处理"""
        self.widget_status_label.setText(f"桌面小组件: 错误")
        self.logger.error(f"Desktop widget error: {error_msg}")
    
    def _task_to_dict(self, task) -> Dict:
        """将任务对象转换为字典"""
        return {
            'id': task.id,
            'title': task.title,
            'description': task.description,
            'priority': task.priority.value if task.priority else 'normal',
            'status': task.status.value if task.status else 'pending',
            'start_date': task.start_date.isoformat() if task.start_date else '',
            'due_date': task.due_date.isoformat() if task.due_date else '',
            'tags': task.tags or []
        }
    
    def _category_to_dict(self, category) -> Dict:
        """将分类对象转换为字典"""
        return {
            'id': category.id,
            'name': category.name,
            'description': category.description
        }