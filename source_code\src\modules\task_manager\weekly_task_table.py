"""
Weekly Task Table Component for Task Manager

This component provides a weekly view of tasks in a table format,
with days of the week as columns and task categories/time slots as rows.
"""

from datetime import datetime, timedelta, date
from typing import List, Dict, Optional, Any
from PyQt6.QtWidgets import (
    QTableWidget, QTableWidgetItem, QHeaderView, QMenu, QMessageBox,
    QComboBox, QLineEdit, QTextEdit, QDateTimeEdit, QWidget, QVBoxLayout,
    QHBoxLayout, QLabel, QPushButton, QFrame, QApplication, QAbstractItemView,
    QScrollArea, QDialog, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal, QDate, QDateTime, QTimer, QSize
from PyQt6.QtGui import QFont, QColor, QBrush, QAction, QPalette, QFontMetrics, QTextDocument, QTextOption

from core.logger import LoggerMixin
from models.task_models import Task, TaskStatus, TaskPriority
from .daily_summary_service import DailySummaryService
from .weekly_summary_service import WeeklySummaryService


class TaskDescriptionDialog(QDialog):
    """Dialog for displaying and editing task description and details"""

    task_updated = pyqtSignal(str, dict)  # task_id, updated_data

    def __init__(self, task_data, parent=None):
        super().__init__(parent)
        self.task_data = task_data
        self.init_ui()
        self.apply_styles()

    def init_ui(self):
        """Initialize the dialog UI"""
        self.setWindowTitle("编辑任务详情")
        self.setFixedSize(450, 350)
        self.setModal(True)

        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)

        # Task title (editable)
        title_label = QLabel("任务标题:")
        title_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(title_label)

        self.title_edit = QLineEdit()
        self.title_edit.setText(self.task_data.get('title', ''))
        self.title_edit.setStyleSheet("padding: 5px; border: 1px solid #ccc; border-radius: 3px;")
        layout.addWidget(self.title_edit)

        # Task description (editable)
        desc_label = QLabel("任务描述:")
        desc_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(desc_label)

        self.description_text = QTextEdit()
        self.description_text.setPlainText(self.task_data.get('description', ''))
        self.description_text.setMaximumHeight(120)
        self.description_text.setStyleSheet("border: 1px solid #ccc; border-radius: 3px;")
        layout.addWidget(self.description_text)

        # Task priority (editable)
        priority_label = QLabel("优先级:")
        priority_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(priority_label)

        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["低", "普通", "高", "紧急"])
        self.priority_combo.setStyleSheet("padding: 5px; border: 1px solid #ccc; border-radius: 3px;")

        # Set current priority
        current_priority = self.task_data.get('priority', '普通')
        # Handle enum format
        if hasattr(current_priority, 'value'):
            current_priority = current_priority.value

        # Map English to Chinese
        priority_map = {
            'low': '低', 'normal': '普通', 'high': '高', 'urgent': '紧急',
            'LOW': '低', 'NORMAL': '普通', 'HIGH': '高', 'URGENT': '紧急',
            'TaskPriority.LOW': '低', 'TaskPriority.NORMAL': '普通',
            'TaskPriority.HIGH': '高', 'TaskPriority.URGENT': '紧急'
        }
        chinese_priority = priority_map.get(str(current_priority), str(current_priority))
        index = self.priority_combo.findText(chinese_priority)
        if index >= 0:
            self.priority_combo.setCurrentIndex(index)

        layout.addWidget(self.priority_combo)

        # Task details (read-only info)
        details_label = QLabel("任务信息:")
        details_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(details_label)

        # Status
        status = self.task_data.get('status', '未开始')
        status_info = QLabel(f"状态: {status}")
        status_info.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(status_info)

        # Start date
        start_date = self.task_data.get('start_date', '')
        if start_date:
            date_info = QLabel(f"开始日期: {start_date}")
            date_info.setStyleSheet("color: #666; font-size: 12px;")
            layout.addWidget(date_info)

        # Buttons
        button_layout = QHBoxLayout()

        # Save button
        save_btn = QPushButton("保存")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        save_btn.clicked.connect(self.save_changes)
        button_layout.addWidget(save_btn)

        # Cancel button
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #757575;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #616161;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

    def apply_styles(self):
        """应用对话框样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border-radius: 8px;
            }
            QLabel {
                color: #333333;
                background-color: transparent;
            }
            QLineEdit {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 5px;
            }
            QTextEdit {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 3px;
            }
            QComboBox {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 5px;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                color: #333333;
                border: 1px solid #ccc;
                border-radius: 3px;
                selection-background-color: #e3f2fd;
                selection-color: #333333;
            }
        """)

    def save_changes(self):
        """Save the edited task data"""
        # Get edited values
        new_title = self.title_edit.text().strip()
        new_description = self.description_text.toPlainText().strip()
        new_priority_chinese = self.priority_combo.currentText()

        # Validate title
        if not new_title:
            QMessageBox.warning(self, "警告", "任务标题不能为空！")
            return

        # Map Chinese priority back to English
        priority_reverse_map = {
            '低': 'low',
            '普通': 'normal',
            '高': 'high',
            '紧急': 'urgent'
        }
        new_priority = priority_reverse_map.get(new_priority_chinese, 'normal')

        # Prepare updated data
        updated_data = {
            'title': new_title,
            'description': new_description,
            'priority': new_priority
        }

        # Emit signal with task ID and updated data
        task_id = self.task_data.get('id')
        if task_id:
            self.task_updated.emit(task_id, updated_data)

        # Close dialog
        self.accept()


class TaskItemWidget(QWidget):
    """Widget for individual task item"""

    task_updated = pyqtSignal(str, dict)  # task_id, updated_data
    task_deleted = pyqtSignal(str)  # task_id
    task_completed = pyqtSignal(str)  # task_id
    size_changed = pyqtSignal()  # Signal when widget size changes
    cell_selection_requested = pyqtSignal(str, str)  # task_id, category - 请求选择单元格

    def __init__(self, task_data=None, parent=None):
        super().__init__(parent)
        self.task_data = task_data or {}
        self.is_editing = False
        self.init_ui()
        self.update_display()

    def init_ui(self):
        """Initialize the task item UI"""
        # 设置透明背景和去掉边框 - 使用更高优先级的样式
        self.setAutoFillBackground(False)
        self.setStyleSheet("""
            TaskItemWidget {
                background-color: transparent !important;
                background: transparent !important;
                border: none !important;
                border-radius: 0px !important;
                margin: 0px !important;
                padding: 0px !important;
            }
            QWidget {
                background-color: transparent !important;
                background: transparent !important;
                border: none !important;
            }
            QLabel {
                border: none !important;
                background: transparent !important;
            }
            QPushButton {
                border: none !important;
                background: transparent !important;
            }
        """)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(3, 3, 3, 3)  # 增加边距确保内容不被截断
        layout.setSpacing(5)  # 增加间距
        # 设置布局的垂直对齐为居中
        layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)

        # Task title label/editor
        self.title_label = QLabel()
        self.title_label.setWordWrap(True)
        self.title_label.setMinimumHeight(24)  # 设置最小高度确保文字完全显示
        # 设置尺寸策略，允许垂直方向扩展
        self.title_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        # 设置对齐方式为垂直居中和左对齐
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft)
        # 移除最大高度限制，让标题可以根据内容自动调整高度
        self.title_label.mousePressEvent = self.start_editing
        # 给QLabel更多的stretch factor，确保它能获得足够空间
        layout.addWidget(self.title_label, 1)  # stretch factor = 1

        # Task title editor (hidden by default)
        self.title_editor = QLineEdit()
        self.title_editor.hide()
        self.title_editor.setMinimumHeight(24)  # 与标签保持一致
        self.title_editor.setMaximumHeight(24)
        self.title_editor.editingFinished.connect(self.finish_editing)
        layout.addWidget(self.title_editor)



        # Action buttons container
        self.actions_widget = QWidget()
        actions_layout = QHBoxLayout(self.actions_widget)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(1)
        # 设置按钮容器的垂直对齐为居中
        actions_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)

        # Complete button
        self.complete_btn = QPushButton("✓")
        self.complete_btn.setFixedSize(16, 16)
        self.complete_btn.setToolTip("完成")
        self.complete_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.complete_btn.clicked.connect(self.mark_completed)
        actions_layout.addWidget(self.complete_btn)

        # Incomplete button
        self.incomplete_btn = QPushButton("○")
        self.incomplete_btn.setFixedSize(16, 16)
        self.incomplete_btn.setToolTip("未完成")
        self.incomplete_btn.setStyleSheet("""
            QPushButton {
                background-color: #FFC107;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.incomplete_btn.clicked.connect(self.mark_incomplete)
        actions_layout.addWidget(self.incomplete_btn)

        # Delete button
        self.delete_btn = QPushButton("✕")
        self.delete_btn.setFixedSize(16, 16)
        self.delete_btn.setToolTip("删除")
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        self.delete_btn.clicked.connect(self.delete_task)
        actions_layout.addWidget(self.delete_btn)

        # Description button
        self.desc_btn = QPushButton("ⓘ")
        self.desc_btn.setFixedSize(16, 16)
        self.desc_btn.setToolTip("查看详情")
        self.desc_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.desc_btn.clicked.connect(self.show_description_dialog)
        actions_layout.addWidget(self.desc_btn)

        # 添加按钮容器，设置垂直居中对齐
        layout.addWidget(self.actions_widget, 0, Qt.AlignmentFlag.AlignVCenter)  # stretch factor = 0，不压缩QLabel空间，垂直居中

    def calculate_text_height(self, text, width):
        """Calculate the actual height needed for HTML text with word wrapping"""
        if not text:
            return 24  # Minimum height for empty text

        # 计算可用宽度（极保守的估算，减去更多空间给按钮和边距）
        available_width = max(60, width - 160)  # 160px for buttons and margins

        # 使用QTextDocument来计算HTML文字高度
        doc = QTextDocument()
        doc.setDefaultFont(self.title_label.font())
        doc.setTextWidth(available_width)
        doc.setHtml(text)

        # 获取文档的理想高度
        doc_height = doc.size().height()

        # 基于字符数的估算方法（作为备用）
        # 移除HTML标签来计算纯文字长度
        import re
        plain_text = re.sub(r'<[^>]+>', '', text)
        char_count = len(plain_text)
        font_metrics = QFontMetrics(self.title_label.font())
        line_height = font_metrics.height()

        # 估算行数（每行大约能容纳的字符数）
        # 使用更保守的字符宽度估算
        avg_char_width = font_metrics.averageCharWidth()
        chars_per_line = max(1, available_width // (avg_char_width * 1.2))  # 1.2倍安全系数
        estimated_lines = max(1, (char_count + chars_per_line - 1) // chars_per_line)

        # 对于超长文字，额外增加行数
        if char_count > 50:  # 超过50个字符认为是超长文字
            estimated_lines += 1  # 额外增加一行

        estimated_height = estimated_lines * line_height * 1.3  # 1.3倍行高安全系数

        # 取两种方法的最大值，并添加更大的安全边距
        calculated_height = max(doc_height, estimated_height)

        # 返回计算出的高度，使用极大的安全边距
        return max(24, int(calculated_height) + 50)  # 50px padding for extra safety

    def sizeHint(self):
        """Return the recommended size for this widget"""
        # 获取当前文字内容（包含HTML格式）
        current_text = self.title_label.text()
        if not current_text:
            current_text = self.task_data.get('title', '')

        # 获取父容器的实际宽度，如果没有则使用默认值
        parent_width = self.parent().width() if self.parent() else 200
        widget_width = max(200, parent_width)

        # 计算文字实际需要的高度
        text_height = self.calculate_text_height(current_text, widget_width)

        # 确保最小高度，并添加一些内边距
        widget_height = max(32, text_height)
        return QSize(widget_width, widget_height)

    def minimumSizeHint(self):
        """Return the minimum size for this widget"""
        return QSize(100, 28)

    def start_editing(self, event):
        """Start editing the task title"""
        if not self.task_data.get('id'):  # Don't edit empty tasks
            return

        self.is_editing = True
        self.title_label.hide()
        self.title_editor.setText(self.task_data.get('title', ''))
        self.title_editor.show()
        self.title_editor.setFocus()
        self.title_editor.selectAll()

    def finish_editing(self):
        """Finish editing and save changes"""
        if not self.is_editing:
            return

        new_title = self.title_editor.text().strip()
        if new_title and new_title != self.task_data.get('title', ''):
            self.task_data['title'] = new_title
            if self.task_data.get('id'):
                self.task_updated.emit(self.task_data['id'], {'title': new_title})

        self.is_editing = False
        self.title_editor.hide()
        self.title_label.show()
        self.update_display()

    def update_display(self):
        """Update the display based on task data"""
        title = self.task_data.get('title', '')
        status = self.task_data.get('status', '待处理')
        priority = self.task_data.get('priority', '普通')

        # 不再截断标题，显示完整标题
        display_title = title

        # Convert priority to string if it's an enum
        if hasattr(priority, 'value'):
            priority_str = priority.value
        else:
            priority_str = str(priority)

        # Priority color mapping
        priority_colors = {
            '低': '#4CAF50',      # 绿色
            'low': '#4CAF50',
            'LOW': '#4CAF50',
            'TaskPriority.LOW': '#4CAF50',
            '普通': '#FF8C00',    # 深橙色
            'normal': '#FF8C00',
            'NORMAL': '#FF8C00',
            'TaskPriority.NORMAL': '#FF8C00',
            '中': '#FF8C00',      # 添加"中"优先级映射
            'medium': '#FF8C00',
            'MEDIUM': '#FF8C00',
            '高': '#2196F3',      # 蓝色
            'high': '#2196F3',
            'HIGH': '#2196F3',
            'TaskPriority.HIGH': '#2196F3',
            '紧急': '#F44336',    # 红色
            'urgent': '#F44336',
            'URGENT': '#F44336',
            'TaskPriority.URGENT': '#F44336'
        }

        # Get priority color (try both original priority and string version)
        priority_color = priority_colors.get(priority, priority_colors.get(priority_str, '#FF8C00'))

        # Update title with completion and priority styling
        completed_statuses = ['已完成', 'completed', 'COMPLETED']
        if status in completed_statuses:
            # Gray color with strikethrough for completed tasks
            self.title_label.setText(f"<span style='color: #888888; text-decoration: line-through; font-weight: bold;'>{display_title}</span>")
        else:
            # Priority color for incomplete tasks
            self.title_label.setText(f"<span style='color: {priority_color}; font-weight: bold;'>{display_title}</span>")

        # Update button visibility based on status
        completed_statuses = ['已完成', 'completed', 'COMPLETED']
        if status in completed_statuses:
            self.complete_btn.hide()
            self.incomplete_btn.show()
        else:
            self.complete_btn.show()
            self.incomplete_btn.hide()

        # 更新标签高度以适应内容
        self.update_label_height()

        # 强制更新QLabel的高度以适应内容
        self.update_label_height()

        # 更新widget的尺寸提示
        self.updateGeometry()

        # 发射尺寸变化信号，触发父容器重新计算高度
        self.size_changed.emit()

    def update_label_height(self):
        """Update label height to fit content"""
        # 获取当前显示的HTML文字
        current_text = self.title_label.text()
        if current_text:
            # 计算需要的高度
            parent_width = self.parent().width() if self.parent() else 200
            required_height = self.calculate_text_height(current_text, parent_width)

            # 使用极保守的高度计算，支持超长文字完整显示

            # 强制设置QLabel的固定高度
            self.title_label.setFixedHeight(required_height)

            # 确保widget本身也有足够的高度
            widget_height = required_height + 6  # 6px for margins
            self.setFixedHeight(widget_height)

            # 强制刷新布局
            self.title_label.updateGeometry()
            self.updateGeometry()

            # 延迟刷新以确保布局更新
            def safe_repaint():
                try:
                    if hasattr(self, 'title_label') and self.title_label:
                        self.title_label.repaint()
                except RuntimeError:
                    # Widget已被删除，忽略错误
                    pass
            QTimer.singleShot(100, safe_repaint)

    def mark_completed(self):
        """Mark task as completed"""
        if self.task_data.get('id'):
            task_id = self.task_data['id']
            task_category = self.task_data.get('category', '')
            print(f"DEBUG: TaskItemWidget.mark_completed() - 任务ID: {task_id}")
            print(f"DEBUG: TaskItemWidget.mark_completed() - 任务标题: {self.task_data.get('title', 'N/A')}")
            print(f"DEBUG: TaskItemWidget.mark_completed() - 任务类别: {task_category}")
            print(f"DEBUG: TaskItemWidget.mark_completed() - 任务开始日期: {self.task_data.get('start_date', 'N/A')}")

            # Update local data with Chinese status for UI display
            self.task_data['status'] = '已完成'
            # Send English enum value to service for database update
            self.task_updated.emit(self.task_data['id'], {'status': 'COMPLETED'})

            # 请求选择当前任务所在的单元格
            if task_category:
                print(f"DEBUG: TaskItemWidget.mark_completed() - 发射cell_selection_requested信号: task_id={task_id}, category={task_category}")
                self.cell_selection_requested.emit(task_id, task_category)

            self.update_display()

    def mark_incomplete(self):
        """Mark task as incomplete"""
        if self.task_data.get('id'):
            task_id = self.task_data['id']
            task_category = self.task_data.get('category', '')
            print(f"DEBUG: TaskItemWidget.mark_incomplete() - 任务ID: {task_id}")
            print(f"DEBUG: TaskItemWidget.mark_incomplete() - 任务标题: {self.task_data.get('title', 'N/A')}")
            print(f"DEBUG: TaskItemWidget.mark_incomplete() - 任务类别: {task_category}")
            print(f"DEBUG: TaskItemWidget.mark_incomplete() - 任务开始日期: {self.task_data.get('start_date', 'N/A')}")

            # Update local data with Chinese status for UI display
            self.task_data['status'] = '待处理'
            # Send English enum value to service for database update
            self.task_updated.emit(self.task_data['id'], {'status': 'PENDING'})

            # 请求选择当前任务所在的单元格
            if task_category:
                print(f"DEBUG: TaskItemWidget.mark_incomplete() - 发射cell_selection_requested信号: task_id={task_id}, category={task_category}")
                self.cell_selection_requested.emit(task_id, task_category)

            self.update_display()

    def delete_task(self):
        """Delete the task"""
        if self.task_data.get('id'):
            task_id = self.task_data['id']

            # 通知表格记录当前选择位置
            parent_widget = self.parent()
            while parent_widget:
                if hasattr(parent_widget, 'record_current_selection'):
                    parent_widget.record_current_selection()
                    break
                parent_widget = parent_widget.parent()

            # 发射删除信号，删除完成后会自动恢复到之前选择的单元格
            self.task_deleted.emit(task_id)

    def show_description_dialog(self):
        """Show task description dialog"""
        if self.task_data.get('id'):
            dialog = TaskDescriptionDialog(self.task_data, self)
            dialog.task_updated.connect(self.task_updated.emit)
            dialog.open()


class MultiTaskCellWidget(QWidget):
    """Widget that can contain multiple tasks in a single cell"""

    task_updated = pyqtSignal(str, dict)  # task_id, updated_data
    task_deleted = pyqtSignal(str)  # task_id
    new_task_requested = pyqtSignal(dict)  # task_data with date and category
    height_changed = pyqtSignal()  # Signal when height needs to be recalculated
    cell_selection_requested = pyqtSignal(str, str)  # task_id, category - 请求选择单元格

    def __init__(self, cell_date=None, category=None, tasks_data=None, parent=None):
        super().__init__(parent)
        self.cell_date = cell_date
        self.category = category
        self.tasks_data = tasks_data or []
        self.task_widgets = []
        self.min_height = 80  # Minimum cell height
        self.task_height = 50  # Height per task - 增加以适应新的TaskItemWidget高度
        self.init_ui()
        self.update_tasks()

    def init_ui(self):
        """Initialize the multi-task cell UI"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(2, 2, 2, 2)
        self.layout.setSpacing(1)

        # Container for tasks (no scroll area for dynamic height)
        self.tasks_container = QWidget()
        self.tasks_layout = QVBoxLayout(self.tasks_container)
        self.tasks_layout.setContentsMargins(3, 3, 3, 3)  # 增加边距
        self.tasks_layout.setSpacing(5)  # 增加任务之间的间距，确保任务不重叠

        self.layout.addWidget(self.tasks_container)

        # Add task button (always at the bottom)
        self.add_button = QPushButton("+ 添加任务")
        self.add_button.setMaximumHeight(20)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px dashed #ccc;
                color: #666;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
                border: 1px dashed #999;
            }
        """)
        self.add_button.clicked.connect(self.request_new_task)
        self.layout.addWidget(self.add_button)

    def update_tasks(self):
        """Update the display of tasks"""

        # Clear existing task widgets and disconnect signals
        for widget in self.task_widgets:
            # Disconnect signals before deleting
            try:
                widget.task_updated.disconnect()
                widget.task_deleted.disconnect()
            except:
                pass
            widget.deleteLater()
        self.task_widgets.clear()

        # Sort tasks by priority (紧急、高、普通、低)
        priority_weights = {
            '紧急': 4, 'urgent': 4, 'URGENT': 4,
            '高': 3, 'high': 3, 'HIGH': 3,
            '普通': 2, 'normal': 2, 'NORMAL': 2,
            '低': 1, 'low': 1, 'LOW': 1
        }

        sorted_tasks = sorted(self.tasks_data,
                            key=lambda task: priority_weights.get(task.get('priority', '普通'), 2),
                            reverse=True)

        # Add task widgets for each task (sorted by priority)
        for task_data in sorted_tasks:
            task_widget = TaskItemWidget(task_data)
            task_widget.task_updated.connect(self.task_updated)
            task_widget.task_deleted.connect(self.task_deleted)
            task_widget.size_changed.connect(self.on_task_size_changed)  # 连接尺寸变化信号
            task_widget.cell_selection_requested.connect(self.cell_selection_requested)  # 连接单元格选择请求信号
            self.tasks_layout.addWidget(task_widget)
            self.task_widgets.append(task_widget)

        # Add stretch to push add button to bottom
        self.tasks_layout.addStretch()

        # Update height and emit signal
        self.update_height()
        self.height_changed.emit()

    def request_new_task(self):
        """Request creation of a new task"""
        task_data = {
            'date': self.cell_date,
            'category': self.category,
            'title': '',
            'description': '',
            'status': '待处理',
            'priority': '普通'
        }
        self.new_task_requested.emit(task_data)

    def add_task(self, task_data):
        """Add a new task to this cell"""
        self.tasks_data.append(task_data)
        self.update_tasks()

    def calculate_required_height(self):
        """Calculate the required height for this cell based on actual task widget heights"""
        if not self.tasks_data:
            return self.min_height

        # Calculate height based on actual task widget heights
        total_height = 0

        # 获取当前容器的实际宽度
        container_width = self.width() if self.width() > 0 else 200

        # Add height for each task widget using actual width and HTML content
        for i, task_widget in enumerate(self.task_widgets):
            if task_widget and i < len(self.tasks_data):
                # 获取任务的实际显示文字（包含HTML格式）
                display_text = task_widget.title_label.text()
                if not display_text:
                    display_text = self.tasks_data[i].get('title', '')

                # 使用实际宽度和HTML内容计算任务高度
                widget_height = task_widget.calculate_text_height(display_text, container_width)
                total_height += widget_height

        # Add spacing between tasks
        if len(self.task_widgets) > 1:
            total_height += (len(self.task_widgets) - 1) * 5  # 5px spacing between tasks

        # Add padding for container margins and add button
        total_height += 70  # Container margins + add button + extra padding

        # Ensure minimum height
        required_height = max(self.min_height, total_height)
        return required_height

    def update_height(self):
        """Update the widget's height based on task count"""
        required_height = self.calculate_required_height()
        # 使用固定高度确保容器大小正确
        self.setFixedHeight(required_height)

    def on_task_size_changed(self):
        """Handle task widget size change"""
        # 重新计算并更新高度
        self.update_height()
        self.height_changed.emit()

    def resizeEvent(self, event):
        """Handle resize event to recalculate height when width changes"""
        super().resizeEvent(event)
        # 当宽度变化时，重新计算高度
        if hasattr(self, 'task_widgets') and self.task_widgets:
            self.update_height()
            self.height_changed.emit()


class TaskCellWidget(QWidget):
    """Custom widget for task cells with inline editing capabilities - DEPRECATED"""

    task_updated = pyqtSignal(str, dict)  # task_id, updated_data
    task_deleted = pyqtSignal(str)  # task_id

    def __init__(self, task_data=None, parent=None):
        super().__init__(parent)
        self.task_data = task_data or {}
        self.is_editing = False
        self.init_ui()
        self.update_display()
    
    def init_ui(self):
        """Initialize the cell UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(1)
        
        # Task title label/editor
        self.title_label = QLabel()
        self.title_label.setWordWrap(True)
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.title_label.mousePressEvent = self.start_editing
        layout.addWidget(self.title_label)
        
        # Task title editor (hidden by default)
        self.title_editor = QLineEdit()
        self.title_editor.hide()
        self.title_editor.editingFinished.connect(self.finish_editing)
        layout.addWidget(self.title_editor)
        
        # Status indicator
        self.status_label = QLabel()
        self.status_label.setMaximumHeight(15)
        layout.addWidget(self.status_label)
    
    def start_editing(self, event):
        """Start editing the task"""
        if not self.is_editing:
            self.is_editing = True
            self.title_label.hide()
            self.title_editor.setText(self.task_data.get('title', ''))
            self.title_editor.show()
            self.title_editor.setFocus()
            self.title_editor.selectAll()
    
    def finish_editing(self):
        """Finish editing and save changes"""
        if self.is_editing:
            new_title = self.title_editor.text().strip()
            if new_title != self.task_data.get('title', ''):
                self.task_data['title'] = new_title
                if self.task_data.get('id'):
                    self.task_updated.emit(self.task_data['id'], {'title': new_title})
            
            self.is_editing = False
            self.title_editor.hide()
            self.title_label.show()
            self.update_display()
    
    def update_display(self):
        """Update the display based on task data"""
        title = self.task_data.get('title', '点击添加任务...')
        self.title_label.setText(title)
        
        # Set status color
        status = self.task_data.get('status', '待处理')
        status_colors = {
            '待处理': '#FFA500',
            'pending': '#FFA500',
            '进行中': '#4169E1',
            'in_progress': '#4169E1',
            '已完成': '#32CD32',
            'completed': '#32CD32',
            '已取消': '#DC143C',
            'cancelled': '#DC143C'
        }
        
        color = status_colors.get(status, '#808080')
        self.status_label.setStyleSheet(f"background-color: {color}; border-radius: 2px;")
        
        # Set priority styling with transparent background and purple text for high priority
        priority = self.task_data.get('priority', '普通')
        if priority in ['urgent', '紧急']:
            self.setStyleSheet("""
                TaskCellWidget {
                    background-color: transparent;
                    border-left: 3px solid red;
                }
                QLabel {
                    color: red;
                    font-weight: bold;
                }
            """)
        elif priority in ['high', '高']:
            self.setStyleSheet("""
                TaskCellWidget {
                    background-color: transparent;
                    border-left: 3px solid purple;
                }
                QLabel {
                    color: purple;
                    font-weight: bold;
                }
            """)
        else:
            self.setStyleSheet("""
                TaskCellWidget {
                    background-color: transparent;
                    border-left: 3px solid transparent;
                }
                QLabel {
                    color: #333;
                }
            """)


class DailySummaryCellWidget(QWidget):
    """Widget for daily summary text input"""

    text_changed = pyqtSignal(date, str)  # date, summary_text
    height_changed = pyqtSignal()  # Signal when height needs to be recalculated

    def __init__(self, cell_date=None, summary_text="", parent=None):
        super().__init__(parent)
        self.cell_date = cell_date
        self.init_ui()
        self.set_summary_text(summary_text)

    def init_ui(self):
        """Initialize the daily summary cell UI"""
        # Remove all margins and spacing to fit perfectly in cell
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)

        # Make widget background same as Monday morning cell - use gradient
        self.setStyleSheet("""
            DailySummaryCellWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #CAF0F8,
                    stop:0.5 #F0F8FF,
                    stop:1 #CAF0F8) !important;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)

        # Text edit for summary input
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("输入今日总结...")

        # Enable scrollbars when content exceeds cell height to prevent overflow
        self.text_edit.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.text_edit.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # Enable word wrap
        self.text_edit.setWordWrapMode(QTextOption.WrapMode.WordWrap)

        # Make text edit completely fill the cell with no borders or margins - 透明背景
        self.text_edit.setStyleSheet("""
            QTextEdit {
                border: none;
                border-radius: 0px;
                padding: 0px;
                margin: 0px;
                font-size: 12px;
                background-color: transparent !important;
                background: transparent !important;
                outline: none;
            }
            QTextEdit:focus {
                border: 1px solid #4CAF50;
                background-color: transparent !important;
                background: transparent !important;
            }
        """)

        # Connect text change signal
        self.text_edit.textChanged.connect(self.on_text_changed)

        # Add text edit with no margins
        self.layout.addWidget(self.text_edit)

        # Set initial height - will be dynamically adjusted based on content
        self.base_widget_height = 180  # 60 * 3
        self.base_text_height = 165    # 55 * 3
        self.line_height_increment = 20  # Increased for more visible changes

        # Set initial height
        self.setFixedHeight(self.base_widget_height)
        self.text_edit.setFixedHeight(self.base_text_height)

    def set_summary_text(self, text):
        """Set the summary text"""
        if text != self.text_edit.toPlainText():
            self.text_edit.setPlainText(text)
            # No need to update height since we use fixed height

    def get_summary_text(self):
        """Get the current summary text"""
        return self.text_edit.toPlainText()

    def calculate_required_height(self):
        """Calculate the required height based on text content"""
        text = self.text_edit.toPlainText().strip()

        if not text:
            # Empty text, use base height
            return self.base_widget_height, self.base_text_height

        # Force document to use the current widget width for line wrapping
        doc = self.text_edit.document()
        current_width = self.text_edit.width() - 20  # Account for margins
        if current_width > 0:
            doc.setTextWidth(current_width)

        # Get line count from document after setting width
        line_count = doc.lineCount()

        # Also count manual line breaks
        manual_lines = text.count('\n') + 1

        # Use the maximum of document line count and manual line count
        effective_line_count = max(line_count, manual_lines)

        # Calculate height: base + (additional_lines * increment)
        additional_lines = max(0, effective_line_count - 1)
        widget_height = self.base_widget_height + (additional_lines * self.line_height_increment)
        text_height = self.base_text_height + (additional_lines * self.line_height_increment)

        # Set reasonable limits
        widget_height = max(self.base_widget_height, min(widget_height, 500))  # Max 500px
        text_height = max(self.base_text_height, min(text_height, 485))  # Max 485px
        return widget_height, text_height

    def update_height(self):
        """Update the widget height based on content"""
        widget_height, text_height = self.calculate_required_height()
        self.setFixedHeight(widget_height)
        self.text_edit.setFixedHeight(text_height)

        # Force immediate update
        self.updateGeometry()
        self.text_edit.updateGeometry()

        # Notify parent table to sync all daily summary heights
        if hasattr(self, 'parent_table') and self.parent_table:
            self.parent_table.sync_daily_summary_heights()

    def on_text_changed(self):
        """Handle text change"""
        if self.cell_date:
            self.text_changed.emit(self.cell_date, self.get_summary_text())

        # Update height based on content
        self.update_height()


class WeeklySummaryCellWidget(QWidget):
    """Widget for weekly summary text input"""

    text_changed = pyqtSignal(date, str)  # week_start_date, summary_text
    height_changed = pyqtSignal()  # Signal when height needs to be recalculated

    def __init__(self, week_start_date=None, summary_text="", parent=None):
        super().__init__(parent)
        self.week_start_date = week_start_date
        self.init_ui()
        self.set_summary_text(summary_text)

    def init_ui(self):
        """Initialize the weekly summary cell UI"""
        # Remove all margins and spacing to fit perfectly in cell
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)

        # Make widget background same as Monday morning cell - use gradient
        self.setStyleSheet("""
            WeeklySummaryCellWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #CAF0F8,
                    stop:0.5 #F0F8FF,
                    stop:1 #CAF0F8) !important;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)

        # Text edit for summary input
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("输入本周总结...")

        # Enable scrollbars when content exceeds cell height to prevent overflow
        self.text_edit.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.text_edit.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # Enable word wrap
        self.text_edit.setWordWrapMode(QTextOption.WrapMode.WordWrap)

        # Make text edit completely fill the cell with no borders or margins - 透明背景
        self.text_edit.setStyleSheet("""
            QTextEdit {
                border: none;
                border-radius: 0px;
                padding: 0px;
                margin: 0px;
                font-size: 12px;
                background-color: transparent !important;
                background: transparent !important;
                outline: none;
            }
            QTextEdit:focus {
                border: 1px solid #4CAF50;
                background-color: transparent !important;
                background: transparent !important;
            }
        """)

        # Connect text change signal
        self.text_edit.textChanged.connect(self.on_text_changed)

        # Add text edit with no margins
        self.layout.addWidget(self.text_edit)

        # Set initial height - will be dynamically adjusted based on content
        self.base_widget_height = 180  # Same as daily summary
        self.base_text_height = 165    # Same as daily summary
        self.line_height_increment = 20  # Same as daily summary

        # Set initial height
        self.setFixedHeight(self.base_widget_height)
        self.text_edit.setFixedHeight(self.base_text_height)

    def set_summary_text(self, text):
        """Set the summary text"""
        if text != self.text_edit.toPlainText():
            self.text_edit.setPlainText(text)

    def get_summary_text(self):
        """Get the current summary text"""
        return self.text_edit.toPlainText()

    def calculate_required_height(self):
        """Calculate the required height based on text content"""
        text = self.text_edit.toPlainText().strip()

        if not text:
            # Empty text, use base height
            return self.base_widget_height, self.base_text_height

        # Force document to use the current widget width for line wrapping
        doc = self.text_edit.document()
        current_width = self.text_edit.width() - 20  # Account for margins
        if current_width > 0:
            doc.setTextWidth(current_width)

        # Get line count from document after setting width
        line_count = doc.lineCount()

        # Also count manual line breaks
        manual_lines = text.count('\n') + 1

        # Use the maximum of document line count and manual line count
        effective_line_count = max(line_count, manual_lines)

        # Calculate height: base + (additional_lines * increment)
        additional_lines = max(0, effective_line_count - 1)
        widget_height = self.base_widget_height + (additional_lines * self.line_height_increment)
        text_height = self.base_text_height + (additional_lines * self.line_height_increment)

        # Set reasonable limits (higher than daily summary since it's for a whole week)
        widget_height = max(self.base_widget_height, min(widget_height, 1000))  # Max 1000px as requested
        text_height = max(self.base_text_height, min(text_height, 995))  # Max 995px
        return widget_height, text_height

    def update_height(self):
        """Update the widget height based on content"""
        widget_height, text_height = self.calculate_required_height()
        self.setFixedHeight(widget_height)
        self.text_edit.setFixedHeight(text_height)

        # Force immediate update
        self.updateGeometry()
        self.text_edit.updateGeometry()

        # Notify parent table to sync all weekly summary heights
        if hasattr(self, 'parent_table') and self.parent_table:
            self.parent_table.sync_weekly_summary_heights()

    def on_text_changed(self):
        """Handle text change"""
        if self.week_start_date:
            self.text_changed.emit(self.week_start_date, self.get_summary_text())

        # Update height based on content
        self.update_height()


class WeeklyPlanCellWidget(QWidget):
    """Widget for weekly planning that spans across all days with task functionality"""

    task_updated = pyqtSignal(str, dict)  # task_id, updated_data
    task_deleted = pyqtSignal(str)  # task_id
    new_task_requested = pyqtSignal(dict)  # task_data with category
    height_changed = pyqtSignal()  # Signal when height needs to be recalculated
    cell_selection_requested = pyqtSignal(str, str)  # task_id, category - 请求选择单元格

    def __init__(self, tasks_data=None, parent=None):
        super().__init__(parent)
        self.category = "周计划"
        self.tasks_data = tasks_data or []
        self.task_widgets = []
        self.min_height = 120  # 设置为普通单元格的两倍高度
        self.task_height = 50
        self.init_ui()
        self.update_tasks()
        # 设置最小高度为普通单元格的两倍
        self.setMinimumHeight(self.min_height)

    def init_ui(self):
        """Initialize the weekly plan cell UI"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(2, 2, 2, 2)
        self.layout.setSpacing(1)

        # Set widget style to match other cells
        self.setStyleSheet("""
            WeeklyPlanCellWidget {
                background-color: white;
                border: 1px solid #ddd;
            }
        """)

        # Container for tasks (no title label)
        self.tasks_container = QWidget()
        self.tasks_layout = QVBoxLayout(self.tasks_container)
        self.tasks_layout.setContentsMargins(3, 3, 3, 3)
        self.tasks_layout.setSpacing(5)

        self.layout.addWidget(self.tasks_container)

        # Add task button
        self.add_button = QPushButton("+ 添加任务")
        self.add_button.setMaximumHeight(20)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px dashed #ccc;
                color: #666;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
                border: 1px dashed #999;
            }
        """)
        self.add_button.clicked.connect(self.request_new_task)
        self.layout.addWidget(self.add_button)

    def calculate_required_height(self):
        """Calculate the required height for this weekly plan cell"""
        if not self.tasks_data:
            return self.min_height

        # Calculate height based on number of tasks
        task_count = len(self.tasks_data)
        required_height = self.min_height + (task_count * self.task_height)

        return max(self.min_height, required_height)

    def update_tasks(self):
        """Update the display of tasks"""
        # Clear existing task widgets
        for widget in self.task_widgets:
            try:
                widget.task_updated.disconnect()
                widget.task_deleted.disconnect()
            except:
                pass
            widget.setParent(None)
            widget.deleteLater()

        self.task_widgets.clear()

        # Enhanced priority weights mapping to handle all possible formats
        priority_weights = {
            # Chinese
            '紧急': 4, '高': 3, '普通': 2, '低': 1,
            # English lowercase
            'urgent': 4, 'high': 3, 'normal': 2, 'low': 1,
            # English uppercase
            'URGENT': 4, 'HIGH': 3, 'NORMAL': 2, 'LOW': 1,
            # Enum string formats
            'TaskPriority.URGENT': 4, 'TaskPriority.HIGH': 3,
            'TaskPriority.NORMAL': 2, 'TaskPriority.LOW': 1,
            # Additional mappings
            'medium': 2, 'MEDIUM': 2, '中': 2
        }

        def get_priority_weight(task):
            """Get priority weight for sorting, with enhanced handling"""
            priority = task.get('priority', '普通')

            # Convert priority to string if it's an enum
            if hasattr(priority, 'value'):
                priority_str = priority.value
            else:
                priority_str = str(priority)

            # Try both original priority and string version
            weight = priority_weights.get(priority, priority_weights.get(priority_str, 2))
            return weight

        sorted_tasks = sorted(self.tasks_data, key=get_priority_weight, reverse=True)

        # Add task widgets for each task (sorted by priority)
        for task_data in sorted_tasks:
            task_widget = TaskItemWidget(task_data)
            task_widget.task_updated.connect(self.task_updated.emit)
            task_widget.task_deleted.connect(self.task_deleted.emit)
            task_widget.cell_selection_requested.connect(self.cell_selection_requested.emit)  # 连接单元格选择请求信号

            self.tasks_layout.addWidget(task_widget)
            self.task_widgets.append(task_widget)

        # Calculate and set height
        self.calculate_height()

    def calculate_height(self):
        """Calculate required height based on tasks"""
        title_height = 25  # Title label height
        button_height = 25  # Add button height
        task_count = len(self.tasks_data)

        if task_count == 0:
            total_height = max(self.min_height, title_height + button_height + 20)
        else:
            tasks_height = task_count * self.task_height + (task_count - 1) * 5  # 5px spacing
            total_height = title_height + tasks_height + button_height + 20  # 20px margins

        self.setMinimumHeight(total_height)
        self.height_changed.emit()

    def request_new_task(self):
        """Request to create a new task for weekly plan"""
        task_data = {
            'category': self.category,
            'date': date.today()  # Use today's date as default
        }
        self.new_task_requested.emit(task_data)

    def show_task_details(self, task_data):
        """Show task details dialog"""
        from .weekly_task_manager_widget import TaskDescriptionDialog
        dialog = TaskDescriptionDialog(task_data, self)
        dialog.task_updated.connect(self.task_updated.emit)
        dialog.open()


class WeeklyTaskTable(QTableWidget, LoggerMixin):
    """Weekly task table with inline editing capabilities"""

    # Signals
    task_updated = pyqtSignal(str, dict)  # task_id, updated_data
    task_deleted = pyqtSignal(str)  # task_id
    new_task_requested = pyqtSignal(dict)  # task_data with date and category
    week_changed = pyqtSignal(date, date)  # start_date, end_date
    cell_selection_requested = pyqtSignal(str, str)  # task_id, category - 请求选择单元格
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_week_start = None
        self.current_week_end = None
        self.tasks_data = {}  # {date: {category: [tasks]}}
        self.daily_summaries = {}  # {date: summary_text}
        self.weekly_summaries = {}  # {week_start_date: summary_text}
        self.weekly_plan_tasks_by_week = {}  # {week_start_date: [tasks]}
        self.categories = ["上午", "下午", "晚上", "全天", "其他", "日总结", "周总结", "周计划"]  # Default categories

        # 记录删除操作前的选择位置
        self.previous_selection = (-1, -1)  # (row, col)

        # Initialize summary services
        self.daily_summary_service = DailySummaryService()
        self.weekly_summary_service = WeeklySummaryService()

        self.setup_table()
        self.setup_context_menu()
        self.set_current_week()
    
    def setup_table(self):
        """Setup table properties and structure"""
        # Set table dimensions
        self.setRowCount(len(self.categories))
        self.setColumnCount(7)  # 7 days of the week
        
        # Set headers
        self.set_week_headers()
        self.set_category_headers()
        
        # Table properties
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectItems)
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        
        # Header properties
        self.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.verticalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Fixed)
        self.verticalHeader().setDefaultSectionSize(80)

        # Store row heights for dynamic adjustment
        self.row_heights = [80] * len(self.categories)
        
        # Enable drag and drop
        self.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)
        
        # Style - use gradient background to match main widget style
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                alternate-background-color: #CAF0F8;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #CAF0F8,
                    stop:0.5 #F0F8FF,
                    stop:1 #CAF0F8);
            }
            QTableWidget::item {
                padding: 2px;
                border: 1px solid #e0e0e0;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #CAF0F8,
                    stop:0.5 #F0F8FF,
                    stop:1 #CAF0F8) !important;
            }
            QTableWidget::item:selected {
                background-color: #e6f3ff;
            }
            QHeaderView::section {
                background-color: white;
                padding: 5px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """)
    
    def set_week_headers(self):
        """Set column headers for days of the week"""
        if not self.current_week_start:
            return
        
        weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        headers = []
        
        current_date = self.current_week_start
        for i in range(7):
            day_name = weekdays[i]
            date_str = current_date.strftime("%m/%d")
            headers.append(f"{day_name}\n{date_str}")
            current_date += timedelta(days=1)
        
        self.setHorizontalHeaderLabels(headers)
    
    def set_category_headers(self):
        """Set row headers for task categories"""
        self.setVerticalHeaderLabels(self.categories)
    
    def set_current_week(self, week_offset=0):
        """Set the current week to display
        
        Args:
            week_offset: Number of weeks to offset from current week
        """
        today = date.today()
        days_since_monday = today.weekday()
        week_start = today - timedelta(days=days_since_monday)
        week_start += timedelta(weeks=week_offset)
        week_end = week_start + timedelta(days=6)
        
        self.current_week_start = week_start
        self.current_week_end = week_end
        
        self.set_week_headers()
        self.populate_table()
        self.week_changed.emit(week_start, week_end)
    
    def populate_table(self):
        """Populate table with task data"""
        # Load summaries from database for current week
        self.load_daily_summaries()
        self.load_weekly_summaries()

        # Clear existing widgets
        for row in range(self.rowCount()):
            for col in range(self.columnCount()):
                self.setCellWidget(row, col, None)

        # Set background color for all cells first
        for row in range(self.rowCount()):
            for col in range(self.columnCount()):
                item = QTableWidgetItem()
                item.setBackground(QColor("#CAF0F8"))
                self.setItem(row, col, item)

        # Add multi-task cells
        for row in range(self.rowCount()):
            category = self.categories[row]

            # Special handling for "周计划" row
            if category == "周计划":
                # Get weekly plan tasks (not date-specific)
                weekly_tasks = self.get_weekly_plan_tasks()

                # Create weekly plan widget that spans all columns
                weekly_plan_widget = WeeklyPlanCellWidget(tasks_data=weekly_tasks)
                weekly_plan_widget.task_updated.connect(self.task_updated)
                weekly_plan_widget.task_deleted.connect(self.task_deleted)
                weekly_plan_widget.new_task_requested.connect(self.new_task_requested)
                weekly_plan_widget.height_changed.connect(self.update_row_heights)
                weekly_plan_widget.cell_selection_requested.connect(self.on_cell_selection_requested)  # 连接单元格选择请求信号

                self.setCellWidget(row, 0, weekly_plan_widget)
                # Span across all 7 columns
                self.setSpan(row, 0, 1, 7)
            elif category == "日总结":
                # Special handling for "日总结" row - create text input cells for each day
                for col in range(self.columnCount()):
                    cell_date = self.current_week_start + timedelta(days=col)
                    summary_text = self.daily_summaries.get(cell_date, "")

                    # Create daily summary cell widget
                    summary_widget = DailySummaryCellWidget(
                        cell_date=cell_date,
                        summary_text=summary_text
                    )
                    # Set parent table reference for height synchronization
                    summary_widget.parent_table = self
                    summary_widget.text_changed.connect(self.on_daily_summary_changed)
                    summary_widget.height_changed.connect(self.update_row_heights)

                    self.setCellWidget(row, col, summary_widget)

                    # Force widget background color immediately after setting
                    summary_widget.setAutoFillBackground(True)
                    palette = summary_widget.palette()
                    palette.setColor(summary_widget.backgroundRole(), QColor("#CAF0F8"))
                    summary_widget.setPalette(palette)
                    summary_widget.update()
                    summary_widget.repaint()
            elif category == "周总结":
                # Special handling for "周总结" row - create text input cell spanning all days
                summary_text = self.weekly_summaries.get(self.current_week_start, "")

                # Create weekly summary cell widget
                weekly_summary_widget = WeeklySummaryCellWidget(
                    week_start_date=self.current_week_start,
                    summary_text=summary_text
                )
                # Set parent table reference for height synchronization
                weekly_summary_widget.parent_table = self
                weekly_summary_widget.text_changed.connect(self.on_weekly_summary_changed)
                weekly_summary_widget.height_changed.connect(self.update_row_heights)

                self.setCellWidget(row, 0, weekly_summary_widget)
                # Span across all 7 columns
                self.setSpan(row, 0, 1, 7)

                # Force widget background color immediately after setting
                weekly_summary_widget.setAutoFillBackground(True)
                palette = weekly_summary_widget.palette()
                palette.setColor(weekly_summary_widget.backgroundRole(), QColor("#CAF0F8"))
                weekly_summary_widget.setPalette(palette)
                weekly_summary_widget.update()
                weekly_summary_widget.repaint()
            else:
                # Regular task cells for other categories
                for col in range(self.columnCount()):
                    cell_date = self.current_week_start + timedelta(days=col)

                    # Get all tasks for this date and category
                    tasks = self.get_tasks_for_cell(cell_date, category)

                    # Create multi-task cell widget
                    cell_widget = MultiTaskCellWidget(
                        cell_date=cell_date,
                        category=category,
                        tasks_data=tasks
                    )
                    cell_widget.task_updated.connect(self.task_updated)
                    cell_widget.task_deleted.connect(self.task_deleted)
                    cell_widget.new_task_requested.connect(self.new_task_requested)
                    cell_widget.height_changed.connect(self.update_row_heights)
                    cell_widget.cell_selection_requested.connect(self.on_cell_selection_requested)  # 连接单元格选择请求信号

                    self.setCellWidget(row, col, cell_widget)

        # Update row heights after all cells are created
        self.update_row_heights()

        # Reapply background settings after table population
        self.reapply_background_settings()
    
    def get_tasks_for_cell(self, cell_date: date, category: str) -> List[Dict]:
        """Get tasks for a specific date and category"""
        # Weekly plan doesn't have date-specific tasks
        if category == "周计划":
            return []
        date_tasks = self.tasks_data.get(cell_date, {})
        return date_tasks.get(category, [])

    def get_weekly_plan_tasks(self) -> List[Dict]:
        """Get weekly plan tasks for current week"""
        if not self.current_week_start:
            return []

        week_key = self.current_week_start.strftime('%Y-%m-%d')
        tasks = self.weekly_plan_tasks_by_week.get(week_key, [])

        # 确保所有任务都有category字段
        for task in tasks:
            if 'category' not in task or not task['category']:
                task['category'] = '周计划'

        return tasks

    def load_daily_summaries(self):
        """Load daily summaries from database for current week"""
        if not self.current_week_start:
            self.logger.warning("No current week start date set")
            return

        # Clear existing summaries
        self.daily_summaries.clear()

        # Load summaries for each day of the week
        for i in range(7):
            current_date = self.current_week_start + timedelta(days=i)
            summary_text = self.daily_summary_service.get_daily_summary(current_date)
            if summary_text:
                self.daily_summaries[current_date] = summary_text
                self.logger.debug(f"Loaded daily summary for {current_date}: {len(summary_text)} chars")
            else:
                self.logger.debug(f"No daily summary found for {current_date}")

        self.logger.info(f"Loaded {len(self.daily_summaries)} daily summaries for week starting {self.current_week_start}")

    def load_weekly_summaries(self):
        """Load weekly summaries from database for current week"""
        if not self.current_week_start:
            self.logger.warning("No current week start date set")
            return

        # Clear existing summaries
        self.weekly_summaries.clear()

        # Load summary for current week
        summary_text = self.weekly_summary_service.get_weekly_summary(self.current_week_start)
        if summary_text:
            self.weekly_summaries[self.current_week_start] = summary_text
            self.logger.debug(f"Loaded weekly summary for week starting {self.current_week_start}: {len(summary_text)} chars")
        else:
            self.logger.debug(f"No weekly summary found for week starting {self.current_week_start}")

        self.logger.info(f"Loaded weekly summary for week starting {self.current_week_start}")

    def on_daily_summary_changed(self, cell_date: date, summary_text: str):
        """Handle daily summary text change"""
        # Update local cache
        self.daily_summaries[cell_date] = summary_text

        # Save to database
        success = self.daily_summary_service.save_daily_summary(cell_date, summary_text)
        if success:
            self.logger.debug(f"Daily summary saved for {cell_date}")
        else:
            self.logger.error(f"Failed to save daily summary for {cell_date}")

    def on_weekly_summary_changed(self, week_start_date: date, summary_text: str):
        """Handle weekly summary text change"""
        # Update local cache
        self.weekly_summaries[week_start_date] = summary_text

        # Save to database
        success = self.weekly_summary_service.save_weekly_summary(week_start_date, summary_text)
        if success:
            self.logger.debug(f"Weekly summary saved for week starting {week_start_date}")
        else:
            self.logger.error(f"Failed to save weekly summary for week starting {week_start_date}")

    def refresh_cell(self, cell_date: date, category: str):
        """Refresh a specific cell with updated task data"""
        # Special handling for weekly plan category
        if category == "周计划":
            self.refresh_weekly_plan_cell()
            return

        # Special handling for daily summary category
        if category == "日总结":
            self.refresh_daily_summary_cell(cell_date)
            return

        # Find the cell position
        col = (cell_date - self.current_week_start).days
        row = self.categories.index(category) if category in self.categories else -1

        if 0 <= row < self.rowCount() and 0 <= col < self.columnCount():
            # 重新从数据库查询最新的任务数据，而不是使用缓存
            from .weekly_task_manager_widget import WeeklyTaskManagerWidget
            parent_widget = self.parent()
            while parent_widget and not isinstance(parent_widget, WeeklyTaskManagerWidget):
                parent_widget = parent_widget.parent()

            if parent_widget and hasattr(parent_widget, 'task_service'):
                # 直接从数据库查询所有任务，然后过滤
                all_tasks = parent_widget.task_service.get_tasks(limit=1000)
                # 过滤出当前日期和类别的任务
                tasks = []
                for task in all_tasks:
                    # 检查任务日期是否匹配
                    task_date = None
                    if task.start_date:
                        task_date = task.start_date.date()

                    if task_date == cell_date:
                        task_category = self.determine_task_category(task)
                        if task_category == category:
                            tasks.append({
                                'id': task.id,
                                'title': task.title,
                                'description': task.description,
                                'status': task.status.value if task.status else 'pending',
                                'priority': task.priority.value if task.priority else 'medium',
                                'category': task_category,
                                'start_date': task.start_date,
                                'due_date': task.due_date,
                                'tags': task.tags
                            })
            else:
                # 回退到缓存数据
                tasks = self.get_tasks_for_cell(cell_date, category)

            # Get the existing cell widget and update its tasks
            cell_widget = self.cellWidget(row, col)
            if isinstance(cell_widget, MultiTaskCellWidget):
                cell_widget.tasks_data = tasks
                cell_widget.update_tasks()
                # Force immediate update of the cell widget
                cell_widget.update()
                cell_widget.repaint()

            # Update row heights after cell refresh
            self.update_row_heights()
            # Force immediate update of the table
            self.update()
            self.repaint()

    def add_task_to_cell(self, cell_date: date, category: str, task_data: dict):
        """Add a new task to a specific cell"""
        # Special handling for weekly plan tasks
        if category == "周计划":
            self.add_weekly_plan_task(task_data)
            return

        # Add to internal data structure
        if cell_date not in self.tasks_data:
            self.tasks_data[cell_date] = {}
        if category not in self.tasks_data[cell_date]:
            self.tasks_data[cell_date][category] = []

        self.tasks_data[cell_date][category].append(task_data)

        # Refresh the cell display
        self.refresh_cell(cell_date, category)

        # Update row heights after adding task
        self.update_row_heights()

    def add_weekly_plan_task(self, task_data: dict):
        """Add a new task to weekly plan for current week"""
        if not self.current_week_start:
            return

        week_key = self.current_week_start.strftime('%Y-%m-%d')
        if week_key not in self.weekly_plan_tasks_by_week:
            self.weekly_plan_tasks_by_week[week_key] = []

        self.weekly_plan_tasks_by_week[week_key].append(task_data)

        # Refresh the weekly plan cell
        self.refresh_weekly_plan_cell()

    def refresh_weekly_plan_cell(self):
        """Refresh the weekly plan cell"""
        # Find the weekly plan row
        row = self.categories.index("周计划") if "周计划" in self.categories else -1

        if 0 <= row < self.rowCount():
            # Get the weekly plan widget and update its tasks
            cell_widget = self.cellWidget(row, 0)
            if isinstance(cell_widget, WeeklyPlanCellWidget):
                current_week_tasks = self.get_weekly_plan_tasks()
                cell_widget.tasks_data = current_week_tasks
                cell_widget.update_tasks()
                # Force immediate update
                cell_widget.update()
                cell_widget.repaint()

        # Update row heights
        self.update_row_heights()
        self.update()
        self.repaint()

    def refresh_daily_summary_cell(self, cell_date: date):
        """Refresh a specific daily summary cell"""
        # Find the daily summary row
        row = self.categories.index("日总结") if "日总结" in self.categories else -1
        col = (cell_date - self.current_week_start).days

        if 0 <= row < self.rowCount() and 0 <= col < self.columnCount():
            # Get the daily summary widget and update its text
            cell_widget = self.cellWidget(row, col)
            if isinstance(cell_widget, DailySummaryCellWidget):
                summary_text = self.daily_summaries.get(cell_date, "")
                cell_widget.set_summary_text(summary_text)
                # Force immediate update
                cell_widget.update()
                cell_widget.repaint()

        # Update row heights
        self.update_row_heights()
        self.update()
        self.repaint()
    
    def setup_context_menu(self):
        """Setup context menu for table"""
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def show_context_menu(self, position):
        """Show context menu"""
        item = self.itemAt(position)
        if item is None:
            return
        
        menu = QMenu(self)
        
        # Add task action
        add_action = QAction("添加任务", self)
        add_action.triggered.connect(lambda: self.add_task_at_position(item.row(), item.column()))
        menu.addAction(add_action)
        
        # Edit task action
        edit_action = QAction("编辑任务", self)
        edit_action.triggered.connect(lambda: self.edit_task_at_position(item.row(), item.column()))
        menu.addAction(edit_action)
        
        menu.addSeparator()
        
        # Delete task action
        delete_action = QAction("删除任务", self)
        delete_action.triggered.connect(lambda: self.delete_task_at_position(item.row(), item.column()))
        menu.addAction(delete_action)
        
        menu.exec(self.mapToGlobal(position))
    
    def add_task_at_position(self, row: int, col: int):
        """Add task at specific position"""
        cell_date = self.current_week_start + timedelta(days=col)
        category = self.categories[row]
        
        task_data = {
            'date': cell_date,
            'category': category,
            'title': '新任务',
            'description': '',
            'status': '待处理',
            'priority': '普通'
        }
        
        self.new_task_requested.emit(task_data)
    
    def edit_task_at_position(self, row: int, col: int):
        """Edit task at specific position"""
        cell_widget = self.cellWidget(row, col)
        if cell_widget and cell_widget.task_data.get('title'):
            cell_widget.start_editing(None)
    
    def delete_task_at_position(self, row: int, col: int):
        """Delete task at specific position"""
        cell_widget = self.cellWidget(row, col)
        if cell_widget and cell_widget.task_data.get('id'):
            reply = QMessageBox.question(
                self, "确认删除", "确定要删除这个任务吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.task_deleted.emit(cell_widget.task_data['id'])
    
    def update_tasks_data(self, tasks: List[Task]):
        """Update table with new task data"""
        self.tasks_data.clear()
        # Don't clear all weekly plan tasks, only clear current week's tasks
        if self.current_week_start:
            week_key = self.current_week_start.strftime('%Y-%m-%d')
            self.weekly_plan_tasks_by_week[week_key] = []

        for task in tasks:
            # Determine category first
            category = self.determine_task_category(task)

            # Convert status and priority to Chinese
            status_map = {
                'pending': '待处理',
                'in_progress': '进行中',
                'completed': '已完成',
                'cancelled': '已取消'
            }
            priority_map = {
                'low': '低',
                'normal': '普通',
                'high': '高',
                'urgent': '紧急'
            }

            task_dict = {
                'id': task.id,
                'title': task.title,
                'description': task.description,
                'status': status_map.get(task.status.value if task.status else 'pending', '待处理'),
                'priority': priority_map.get(task.priority.value if task.priority else 'normal', '普通'),
                'due_date': task.due_date,
                'start_date': task.start_date,
                'category': category
            }

            # Special handling for weekly plan tasks
            if category == "周计划":
                if self.current_week_start:
                    week_key = self.current_week_start.strftime('%Y-%m-%d')
                    if week_key not in self.weekly_plan_tasks_by_week:
                        self.weekly_plan_tasks_by_week[week_key] = []
                    self.weekly_plan_tasks_by_week[week_key].append(task_dict)
                continue

            # For regular tasks, use start_date if available, otherwise fall back to due_date
            if task.start_date:
                task_date = task.start_date.date()
            elif task.due_date:
                task_date = task.due_date.date()
            else:
                continue

            # Only show tasks within current week
            if not (self.current_week_start <= task_date <= self.current_week_end):
                continue

            if task_date not in self.tasks_data:
                self.tasks_data[task_date] = {}
            if category not in self.tasks_data[task_date]:
                self.tasks_data[task_date][category] = []

            # Add date to task_dict for regular tasks
            task_dict['date'] = task_date
            self.tasks_data[task_date][category].append(task_dict)

        self.populate_table()



    def update_single_task_data(self, task, task_date, category):
        """Update a single task's data in the internal structure"""
        # Convert status and priority to Chinese
        status_map = {
            'pending': '待处理',
            'in_progress': '进行中',
            'completed': '已完成',
            'cancelled': '已取消'
        }
        priority_map = {
            'low': '低',
            'normal': '普通',
            'high': '高',
            'urgent': '紧急'
        }

        task_dict = {
            'id': task.id,
            'title': task.title,
            'description': task.description,
            'status': status_map.get(task.status.value if task.status else 'pending', '待处理'),
            'priority': priority_map.get(task.priority.value if task.priority else 'normal', '普通'),
            'due_date': task.due_date,
            'start_date': task.start_date,
            'date': task_date,
            'category': category
        }

        # Ensure the date and category exist in tasks_data
        if task_date not in self.tasks_data:
            self.tasks_data[task_date] = {}
        if category not in self.tasks_data[task_date]:
            self.tasks_data[task_date][category] = []

        # Find and update the existing task or add it if not found
        task_found = False
        for i, existing_task in enumerate(self.tasks_data[task_date][category]):
            if existing_task['id'] == task.id:
                self.tasks_data[task_date][category][i] = task_dict
                task_found = True
                break

        if not task_found:
            self.tasks_data[task_date][category].append(task_dict)

    def update_task_status_only(self, task_id: str, updated_data: dict):
        """Update only the status of a specific task without rebuilding the cell"""
        # Find the task widget and update its display directly
        for row in range(self.rowCount()):
            for col in range(self.columnCount()):
                cell_widget = self.cellWidget(row, col)
                if isinstance(cell_widget, MultiTaskCellWidget):
                    for task_widget in cell_widget.task_widgets:
                        if hasattr(task_widget, 'task_data') and task_widget.task_data.get('id') == task_id:
                            # Update the task data
                            for key, value in updated_data.items():
                                if key == 'status':
                                    # Convert status to Chinese for display
                                    status_map = {
                                        'PENDING': '待处理',
                                        'IN_PROGRESS': '进行中',
                                        'COMPLETED': '已完成',
                                        'CANCELLED': '已取消'
                                    }
                                    task_widget.task_data[key] = status_map.get(value, '待处理')
                                else:
                                    task_widget.task_data[key] = value

                            # Update the display of this specific task widget
                            task_widget.update_display()

                            # Also update the internal tasks_data structure
                            for date_tasks in self.tasks_data.values():
                                for category_tasks in date_tasks.values():
                                    for task_dict in category_tasks:
                                        if task_dict.get('id') == task_id:
                                            for key, value in updated_data.items():
                                                if key == 'status':
                                                    status_map = {
                                                        'PENDING': '待处理',
                                                        'IN_PROGRESS': '进行中',
                                                        'COMPLETED': '已完成',
                                                        'CANCELLED': '已取消'
                                                    }
                                                    task_dict[key] = status_map.get(value, '待处理')
                                                else:
                                                    task_dict[key] = value

                                            # Clear any selection to avoid unwanted highlighting
                                            self.clearSelection()
                                            self.setCurrentCell(-1, -1)
                                            return

    def determine_task_category(self, task: Task) -> str:
        """Determine task category based on task properties"""
        # This is a simple implementation - could be enhanced with more logic
        if task.tags:
            tags = task.tags.lower()
            if '日总结' in tags or '总结' in tags:
                return "日总结"
            elif '周计划' in tags:
                return "周计划"
            elif '上午' in tags:
                return "上午"
            elif '下午' in tags:
                return "下午"
            elif '晚上' in tags:
                return "晚上"
            elif '全天' in tags:
                return "全天"

        # Default category
        return "其他"
    
    def navigate_week(self, offset: int):
        """Navigate to different week
        
        Args:
            offset: Number of weeks to move (positive for future, negative for past)
        """
        current_offset = 0
        if self.current_week_start:
            today = date.today()
            days_since_monday = today.weekday()
            this_week_start = today - timedelta(days=days_since_monday)
            current_offset = (self.current_week_start - this_week_start).days // 7
        
        self.set_current_week(current_offset + offset)
    
    def get_current_week_info(self) -> Dict[str, Any]:
        """Get current week information"""
        return {
            'start_date': self.current_week_start,
            'end_date': self.current_week_end,
            'week_number': self.current_week_start.isocalendar()[1] if self.current_week_start else None
        }

    def record_current_selection(self):
        """记录当前选择的单元格位置"""
        current_row = self.currentRow()
        current_col = self.currentColumn()
        self.previous_selection = (current_row, current_col)

    def restore_previous_selection(self):
        """恢复到之前选择的单元格位置"""
        if self.previous_selection != (-1, -1):
            row, col = self.previous_selection
            if 0 <= row < self.rowCount() and 0 <= col < self.columnCount():
                self.setCurrentCell(row, col)
                self.selectRow(row)

    def on_cell_selection_requested(self, task_id: str, category: str):
        """处理单元格选择请求"""
        try:
            print(f"DEBUG: on_cell_selection_requested() - 接收到请求: task_id={task_id}, category={category}")

            # 1. 识别任务所在的单元格
            target_row, target_col = self.find_task_cell_position(task_id, category)
            print(f"DEBUG: on_cell_selection_requested() - 计算得到的位置: ({target_row}, {target_col})")

            if target_row >= 0 and target_col >= 0:
                # 记录当前选择位置（调试用）
                current_row = self.currentRow()
                current_col = self.currentColumn()
                print(f"DEBUG: on_cell_selection_requested() - 当前选择位置: ({current_row}, {current_col})")

                # 2. 将单元格选择定位到这个单元格上
                print(f"DEBUG: on_cell_selection_requested() - 设置新选择位置: ({target_row}, {target_col})")

                # 临时阻止选择变化信号
                self.blockSignals(True)
                self.setCurrentCell(target_row, target_col)
                self.blockSignals(False)

                # 验证设置是否成功
                new_row = self.currentRow()
                new_col = self.currentColumn()
                print(f"DEBUG: on_cell_selection_requested() - 设置后的实际位置: ({new_row}, {new_col})")

                # 3. 延迟刷新整个表格，并确保选择位置不变
                def delayed_refresh():
                    # 在刷新前再次确保选择位置正确
                    self.blockSignals(True)
                    self.setCurrentCell(target_row, target_col)
                    self.blockSignals(False)

                    check_row = self.currentRow()
                    check_col = self.currentColumn()
                    print(f"DEBUG: delayed_refresh() - 刷新前确保选择位置: ({check_row}, {check_col})")

                    self.refresh_entire_table()

                    # 刷新后再次确保选择位置正确
                    self.blockSignals(True)
                    self.setCurrentCell(target_row, target_col)
                    self.blockSignals(False)

                    final_row = self.currentRow()
                    final_col = self.currentColumn()
                    print(f"DEBUG: delayed_refresh() - 最终选择位置: ({final_row}, {final_col})")

                    # 4. 最终全局更新 - 重新从数据库加载数据并更新整个表格
                    def final_global_update():
                        print(f"DEBUG: final_global_update() - 执行真正的全局更新（重新加载数据）")

                        # 获取父级 WeeklyTaskManagerWidget 并调用其 load_data() 方法
                        parent_widget = self.parent()
                        while parent_widget and not hasattr(parent_widget, 'load_data'):
                            parent_widget = parent_widget.parent()

                        if parent_widget and hasattr(parent_widget, 'load_data'):
                            print(f"DEBUG: final_global_update() - 找到父级widget，调用load_data()")
                            parent_widget.load_data()
                        else:
                            print(f"DEBUG: final_global_update() - 未找到父级widget，使用备用刷新方法")
                            # 备用方法：只刷新UI
                            self.update()
                            self.repaint()

                        # 最后一次确保选择位置正确
                        self.blockSignals(True)
                        self.setCurrentCell(target_row, target_col)
                        self.blockSignals(False)

                        ultimate_row = self.currentRow()
                        ultimate_col = self.currentColumn()
                        print(f"DEBUG: final_global_update() - 最终最终选择位置: ({ultimate_row}, {ultimate_col})")

                    QTimer.singleShot(50, final_global_update)

                QTimer.singleShot(100, delayed_refresh)
            else:
                print(f"DEBUG: on_cell_selection_requested() - 无效位置，跳过选择")

        except Exception as e:
            print(f"DEBUG: on_cell_selection_requested() - 异常: {e}")
            # 静默处理错误
            pass

    def find_task_cell_position(self, task_id: str, category: str) -> tuple:
        """根据任务ID和类别找到任务所在的单元格位置"""
        try:
            print(f"DEBUG: find_task_cell_position() - 查找任务: task_id={task_id}, category={category}")
            print(f"DEBUG: find_task_cell_position() - 可用类别: {self.categories}")

            # 找到类别对应的行
            if category in self.categories:
                target_row = self.categories.index(category)
                print(f"DEBUG: find_task_cell_position() - 找到类别行: {target_row}")

                # 对于周计划，直接返回第一列
                if category == "周计划":
                    print(f"DEBUG: find_task_cell_position() - 周计划任务，返回位置: ({target_row}, 0)")
                    return target_row, 0

                # 对于其他类别，需要找到任务所在的具体日期列
                print(f"DEBUG: find_task_cell_position() - 搜索日期列，总列数: {self.columnCount()}")
                for col in range(self.columnCount()):
                    cell_date = self.current_week_start + timedelta(days=col)
                    print(f"DEBUG: find_task_cell_position() - 检查列 {col}, 日期: {cell_date}")

                    # 检查这个日期和类别下是否有对应的任务
                    tasks = self.get_tasks_for_cell(cell_date, category)
                    print(f"DEBUG: find_task_cell_position() - 列 {col} 的任务数量: {len(tasks)}")
                    for task in tasks:
                        if task.get('id') == task_id:
                            print(f"DEBUG: find_task_cell_position() - 找到任务，返回位置: ({target_row}, {col})")
                            return target_row, col
            else:
                print(f"DEBUG: find_task_cell_position() - 类别 '{category}' 不在可用类别中")

            print(f"DEBUG: find_task_cell_position() - 未找到任务，返回 (-1, -1)")
            return -1, -1  # 未找到

        except Exception as e:
            print(f"DEBUG: find_task_cell_position() - 异常: {e}")
            return -1, -1

    def refresh_entire_table(self):
        """刷新整个表格"""
        try:
            # 记录刷新前的选择位置
            before_row = self.currentRow()
            before_col = self.currentColumn()
            print(f"DEBUG: refresh_entire_table() - 刷新前选择位置: ({before_row}, {before_col})")

            # 强制更新表格显示
            self.update_row_heights()
            self.update()
            self.repaint()

            # 记录刷新后的选择位置
            after_row = self.currentRow()
            after_col = self.currentColumn()
            print(f"DEBUG: refresh_entire_table() - 刷新后选择位置: ({after_row}, {after_col})")

            if (before_row, before_col) != (after_row, after_col):
                print(f"DEBUG: refresh_entire_table() - 警告：选择位置发生了变化！")

        except Exception as e:
            print(f"DEBUG: refresh_entire_table() - 异常: {e}")
            # 静默处理错误
            pass

    def update_row_heights(self):
        """Update row heights based on the tallest cell in each row"""
        # 保存当前选择位置
        saved_row = self.currentRow()
        saved_col = self.currentColumn()
        print(f"DEBUG: update_row_heights() - 保存选择位置: ({saved_row}, {saved_col})")

        for row in range(self.rowCount()):
            category = self.categories[row] if row < len(self.categories) else ""

            # Special handling for weekly plan row
            if category == "周计划":
                max_height = 120  # Set minimum height for weekly plan to double normal height
                # Check if there's a WeeklyPlanCellWidget in this row
                cell_widget = self.cellWidget(row, 0)  # Weekly plan is always in column 0
                if isinstance(cell_widget, WeeklyPlanCellWidget):
                    if hasattr(cell_widget, 'calculate_required_height'):
                        required_height = cell_widget.calculate_required_height()
                        max_height = max(max_height, required_height)
            elif category == "日总结":
                max_height = 80  # Set minimum height for daily summary
                # Find the maximum required height in this row for daily summary cells
                for col in range(self.columnCount()):
                    cell_widget = self.cellWidget(row, col)
                    if isinstance(cell_widget, DailySummaryCellWidget):
                        widget_height, text_height = cell_widget.calculate_required_height()
                        max_height = max(max_height, widget_height)
            elif category == "周总结":
                max_height = 120  # Set minimum height for weekly summary
                # Check if there's a WeeklySummaryCellWidget in this row
                cell_widget = self.cellWidget(row, 0)  # Weekly summary is always in column 0
                if isinstance(cell_widget, WeeklySummaryCellWidget):
                    widget_height, text_height = cell_widget.calculate_required_height()
                    max_height = max(max_height, widget_height)
            else:
                max_height = 80  # Minimum height for regular rows
                # Find the maximum required height in this row
                for col in range(self.columnCount()):
                    cell_widget = self.cellWidget(row, col)
                    if isinstance(cell_widget, MultiTaskCellWidget):
                        required_height = cell_widget.calculate_required_height()
                        max_height = max(max_height, required_height)

            # Set the row height
            self.setRowHeight(row, max_height)
            self.row_heights[row] = max_height

            # Update all cell widgets in this row to use the same height
            for col in range(self.columnCount()):
                cell_widget = self.cellWidget(row, col)
                if isinstance(cell_widget, MultiTaskCellWidget):
                    cell_widget.setMinimumHeight(max_height)
                    cell_widget.setMaximumHeight(max_height)
                elif isinstance(cell_widget, WeeklyPlanCellWidget):
                    cell_widget.setMinimumHeight(max_height)
                    cell_widget.setMaximumHeight(max_height)
                elif isinstance(cell_widget, DailySummaryCellWidget):
                    cell_widget.setMinimumHeight(max_height)
                    cell_widget.setMaximumHeight(max_height)
                elif isinstance(cell_widget, WeeklySummaryCellWidget):
                    cell_widget.setMinimumHeight(max_height)
                    cell_widget.setMaximumHeight(max_height)

        # 检查选择位置是否发生了变化
        current_row = self.currentRow()
        current_col = self.currentColumn()
        print(f"DEBUG: update_row_heights() - 更新后选择位置: ({current_row}, {current_col})")

        # 如果选择位置发生了变化，恢复到原来的位置
        if (saved_row, saved_col) != (current_row, current_col):
            print(f"DEBUG: update_row_heights() - 检测到选择位置变化，恢复到: ({saved_row}, {saved_col})")
            if saved_row >= 0 and saved_col >= 0:
                self.setCurrentCell(saved_row, saved_col)
                print(f"DEBUG: update_row_heights() - 恢复后的实际位置: ({self.currentRow()}, {self.currentColumn()})")
        else:
            print(f"DEBUG: update_row_heights() - 选择位置未发生变化")

    def update_weekly_plan_task(self, task_id: str, updated_data: dict):
        """Update a weekly plan task for current week"""
        if not self.current_week_start:
            return

        week_key = self.current_week_start.strftime('%Y-%m-%d')
        if week_key not in self.weekly_plan_tasks_by_week:
            return

        # Find and update the task
        for i, task in enumerate(self.weekly_plan_tasks_by_week[week_key]):
            if task.get('id') == task_id:
                self.weekly_plan_tasks_by_week[week_key][i].update(updated_data)
                break

        # Refresh the weekly plan cell
        self.refresh_weekly_plan_cell()

    def delete_weekly_plan_task(self, task_id: str):
        """Delete a weekly plan task for current week"""
        if not self.current_week_start:
            return

        week_key = self.current_week_start.strftime('%Y-%m-%d')
        if week_key not in self.weekly_plan_tasks_by_week:
            return

        # Find and remove the task
        self.weekly_plan_tasks_by_week[week_key] = [
            task for task in self.weekly_plan_tasks_by_week[week_key]
            if task.get('id') != task_id
        ]

        # Refresh the weekly plan cell
        self.refresh_weekly_plan_cell()

    def sync_daily_summary_heights(self):
        """Synchronize all daily summary cell heights to match the tallest one"""
        try:
            # Find the daily summary row
            daily_summary_row = -1
            for row in range(self.rowCount()):
                if row < len(self.categories) and self.categories[row] == "日总结":
                    daily_summary_row = row
                    break

            if daily_summary_row == -1:
                return  # No daily summary row found

            # Find the maximum height among all daily summary cells
            max_widget_height = 60  # Base height
            max_text_height = 55    # Base text height

            for col in range(self.columnCount()):
                cell_widget = self.cellWidget(daily_summary_row, col)
                if isinstance(cell_widget, DailySummaryCellWidget):
                    widget_height, text_height = cell_widget.calculate_required_height()
                    max_widget_height = max(max_widget_height, widget_height)
                    max_text_height = max(max_text_height, text_height)

            # Set all daily summary cells to the same height
            for col in range(self.columnCount()):
                cell_widget = self.cellWidget(daily_summary_row, col)
                if isinstance(cell_widget, DailySummaryCellWidget):
                    cell_widget.setFixedHeight(max_widget_height)
                    cell_widget.text_edit.setFixedHeight(max_text_height)

            # Update the table row height to match
            self.setRowHeight(daily_summary_row, max_widget_height)

        except Exception as e:
            # Log error silently
            pass

    def reapply_background_settings(self):
        """Reapply background settings to all cells after table population"""
        try:
            # Set background color for all cells
            for row in range(self.rowCount()):
                for col in range(self.columnCount()):
                    item = QTableWidgetItem()
                    item.setBackground(QColor("#CAF0F8"))
                    self.setItem(row, col, item)

            # Force update of all widgets to apply gradient background
            for row in range(self.rowCount()):
                for col in range(self.columnCount()):
                    widget = self.cellWidget(row, col)
                    if widget:
                        # Force widget to reapply its stylesheet
                        widget.setStyleSheet(widget.styleSheet())
                        widget.update()
                        widget.repaint()

            # Force table update
            self.update()
            self.repaint()

        except Exception as e:
            # Log error silently
            pass

    def sync_weekly_summary_heights(self):
        """Synchronize weekly summary cell height"""
        try:
            # Find the weekly summary row
            weekly_summary_row = -1
            for row in range(self.rowCount()):
                if row < len(self.categories) and self.categories[row] == "周总结":
                    weekly_summary_row = row
                    break

            if weekly_summary_row == -1:
                return  # No weekly summary row found

            # Get the weekly summary cell widget (it spans all columns)
            cell_widget = self.cellWidget(weekly_summary_row, 0)
            if isinstance(cell_widget, WeeklySummaryCellWidget):
                widget_height, text_height = cell_widget.calculate_required_height()

                # Set the widget height
                cell_widget.setFixedHeight(widget_height)
                cell_widget.text_edit.setFixedHeight(text_height)

                # Update the table row height to match
                self.setRowHeight(weekly_summary_row, widget_height)

        except Exception as e:
            self.logger.error(f"Error syncing weekly summary heights: {e}")
        self.refresh_weekly_plan_cell()
