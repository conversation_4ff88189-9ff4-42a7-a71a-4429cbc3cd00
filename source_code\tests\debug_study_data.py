"""
调试学习数据问题
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 初始化核心组件
from core.database import init_db_manager, get_session
from core.config import Config
from models.study_models import StudySession, StudyStatus
from datetime import datetime, date


def debug_study_data():
    """调试学习数据"""
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        print("\n🔍 调试学习数据问题")
        print("=" * 50)
        
        # 查看今日所有会话
        today = date.today()
        start_datetime = datetime.combine(today, datetime.min.time())
        end_datetime = datetime.combine(today, datetime.max.time())
        
        with get_session() as session:
            sessions = session.query(StudySession).filter(
                StudySession.start_time >= start_datetime,
                StudySession.start_time <= end_datetime,
                StudySession.is_deleted == False
            ).all()
            
            print(f"今日会话总数: {len(sessions)}")
            print("\n会话详情:")
            
            total_recorded_minutes = 0
            
            for i, s in enumerate(sessions, 1):
                print(f"\n会话 {i}:")
                print(f"  ID: {s.id}")
                print(f"  状态: {s.status}")
                print(f"  开始时间: {s.start_time}")
                print(f"  结束时间: {s.end_time}")
                print(f"  记录时长: {s.duration_minutes}分钟")
                print(f"  休息时长: {s.break_minutes}分钟")
                
                if s.status == StudyStatus.COMPLETED:
                    total_recorded_minutes += s.duration_minutes
                    
                # 计算实际时长
                if s.start_time and s.end_time:
                    actual_duration = s.end_time - s.start_time
                    actual_minutes = int(actual_duration.total_seconds() // 60)
                    print(f"  实际总时长: {actual_minutes}分钟")
                    print(f"  净学习时长: {actual_minutes - s.break_minutes}分钟")
                elif s.start_time:
                    current_duration = datetime.now() - s.start_time
                    current_minutes = int(current_duration.total_seconds() // 60)
                    print(f"  当前总时长: {current_minutes}分钟")
            
            print(f"\n已完成会话总时长: {total_recorded_minutes}分钟")
            
            # 检查是否有异常数据
            print("\n🔍 检查异常数据:")
            
            # 查找负时长的会话
            negative_sessions = session.query(StudySession).filter(
                StudySession.duration_minutes < 0
            ).all()
            
            if negative_sessions:
                print(f"发现 {len(negative_sessions)} 个负时长会话:")
                for s in negative_sessions:
                    print(f"  会话 {s.id}: {s.duration_minutes}分钟")
            else:
                print("未发现负时长会话")
            
            # 查找异常大的时长
            large_sessions = session.query(StudySession).filter(
                StudySession.duration_minutes > 1440  # 超过24小时
            ).all()
            
            if large_sessions:
                print(f"发现 {len(large_sessions)} 个异常大时长会话:")
                for s in large_sessions:
                    print(f"  会话 {s.id}: {s.duration_minutes}分钟 ({s.duration_minutes//60}小时)")
            else:
                print("未发现异常大时长会话")
            
            # 查找休息时间异常的会话
            large_break_sessions = session.query(StudySession).filter(
                StudySession.break_minutes > 1440  # 超过24小时休息
            ).all()
            
            if large_break_sessions:
                print(f"发现 {len(large_break_sessions)} 个异常大休息时长会话:")
                for s in large_break_sessions:
                    print(f"  会话 {s.id}: 休息{s.break_minutes}分钟 ({s.break_minutes//60}小时)")
            else:
                print("未发现异常大休息时长会话")
        
        print("\n" + "=" * 50)
        print("🔧 建议修复方案:")
        print("1. 清理异常数据（负时长、超大时长）")
        print("2. 修复时长计算逻辑")
        print("3. 重新计算统计数据")
            
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()


def clean_abnormal_data():
    """清理异常数据"""
    try:
        config = Config()
        db_manager = init_db_manager(config)
        
        print("\n🧹 清理异常数据...")
        
        with get_session() as session:
            # 删除负时长的会话
            negative_count = session.query(StudySession).filter(
                StudySession.duration_minutes < 0
            ).count()
            
            if negative_count > 0:
                session.query(StudySession).filter(
                    StudySession.duration_minutes < 0
                ).delete()
                print(f"删除了 {negative_count} 个负时长会话")
            
            # 修复异常大时长的会话（设为合理值）
            large_sessions = session.query(StudySession).filter(
                StudySession.duration_minutes > 1440
            ).all()
            
            for s in large_sessions:
                if s.start_time and s.end_time:
                    # 重新计算正确的时长
                    actual_duration = s.end_time - s.start_time
                    actual_minutes = int(actual_duration.total_seconds() // 60)
                    if actual_minutes <= 1440:  # 如果重新计算的时长合理
                        s.duration_minutes = max(0, actual_minutes - s.break_minutes)
                        print(f"修复会话 {s.id} 时长: {s.duration_minutes}分钟")
                    else:
                        # 如果仍然异常，删除
                        session.delete(s)
                        print(f"删除异常会话 {s.id}")
                else:
                    # 没有结束时间的异常会话，删除
                    session.delete(s)
                    print(f"删除无效会话 {s.id}")
            
            session.commit()
            print("✅ 数据清理完成")
            
    except Exception as e:
        print(f"清理失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_study_data()
    
    # 询问是否清理数据
    response = input("\n是否清理异常数据? (y/n): ")
    if response.lower() == 'y':
        clean_abnormal_data()
        print("\n重新检查数据:")
        debug_study_data()
