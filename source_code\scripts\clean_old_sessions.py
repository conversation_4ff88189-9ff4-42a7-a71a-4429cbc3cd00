"""
清理旧的活动会话
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 初始化核心组件
from core.database import init_db_manager, get_session
from core.config import Config
from models.study_models import StudySession, StudyStatus
from datetime import datetime


def clean_old_sessions():
    """清理旧的活动会话"""
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        print("\n🧹 清理旧的活动会话...")
        
        with get_session() as session:
            # 查找所有活动和暂停的会话
            active_sessions = session.query(StudySession).filter(
                StudySession.status.in_([StudyStatus.ACTIVE, StudyStatus.PAUSED])
            ).all()
            
            print(f"发现 {len(active_sessions)} 个活动/暂停会话")
            
            for s in active_sessions:
                print(f"处理会话 {s.id[:8]}...")
                print(f"  状态: {s.status}")
                print(f"  开始时间: {s.start_time}")
                
                # 计算会话时长
                if s.start_time:
                    duration = datetime.now() - s.start_time
                    duration_minutes = int(duration.total_seconds() // 60)
                    
                    # 如果会话时长合理（小于24小时），标记为完成
                    if duration_minutes <= 1440:
                        s.status = StudyStatus.COMPLETED
                        s.end_time = datetime.now()
                        s.duration_minutes = duration_minutes
                        print(f"  → 标记为完成，时长: {duration_minutes}分钟")
                    else:
                        # 如果会话时长异常，删除
                        session.delete(s)
                        print(f"  → 删除异常会话（时长: {duration_minutes}分钟）")
                else:
                    # 没有开始时间的会话，删除
                    session.delete(s)
                    print(f"  → 删除无效会话（无开始时间）")
            
            session.commit()
            print("✅ 旧会话清理完成")
            
            # 重新检查
            remaining_active = session.query(StudySession).filter(
                StudySession.status.in_([StudyStatus.ACTIVE, StudyStatus.PAUSED])
            ).count()
            
            print(f"剩余活动/暂停会话: {remaining_active}")
            
    except Exception as e:
        print(f"清理失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    clean_old_sessions()
