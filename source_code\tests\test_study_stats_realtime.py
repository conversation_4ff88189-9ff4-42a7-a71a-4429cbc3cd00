"""
测试学习统计实时更新功能
"""

import sys
from pathlib import Path
import time

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config
from modules.study_tracker.study_service import StudyService


def test_study_stats_realtime():
    """测试学习统计实时更新"""
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建学习服务
        study_service = StudyService()
        
        # 确保有科目
        subjects = study_service.get_subjects()
        if not subjects:
            print("创建默认科目...")
            subject = study_service.create_subject(
                name="统计测试科目",
                description="用于测试统计功能的科目",
                color="#9b59b6"
            )
            if subject:
                subject_id = subject.id
                print(f"创建科目成功，ID: {subject_id}")
            else:
                print("创建科目失败")
                return
        else:
            subject_id = subjects[0]['id']
            print(f"使用现有科目，ID: {subject_id}")
        
        print("\n📊 测试学习统计实时更新功能")
        print("=" * 50)
        
        # 1. 检查初始统计
        print("1. 检查初始今日统计...")
        initial_stats = study_service.get_daily_statistics()
        print(f"   初始统计: {initial_stats.get('total_minutes', 0)}分钟, {initial_stats.get('total_sessions', 0)}次")
        
        # 2. 开始学习会话
        print("\n2. 开始学习会话...")
        session = study_service.start_session(
            subject_id=subject_id,
            title="统计测试会话",
            planned_duration=5
        )
        
        if session:
            print("✅ 学习会话创建成功")
            
            # 3. 监控统计变化
            print("\n3. 监控统计变化（10秒）...")
            for i in range(10):
                # 获取当前统计
                current_stats = study_service.get_daily_statistics()
                total_minutes = current_stats.get('total_minutes', 0)
                total_sessions = current_stats.get('total_sessions', 0)
                
                # 获取当前会话时长
                current_seconds = study_service.get_current_duration_seconds()
                current_minutes = current_seconds // 60
                
                print(f"   第{i+1}秒:")
                print(f"     当前会话时长: {current_seconds}秒 ({current_minutes}分钟)")
                print(f"     今日统计: {total_minutes}分钟, {total_sessions}次")
                
                # 检查统计是否包含当前会话
                if current_minutes > 0 and total_minutes >= current_minutes:
                    print(f"     ✅ 统计正确包含当前会话")
                elif current_minutes > 0:
                    print(f"     ❌ 统计未正确包含当前会话")
                else:
                    print(f"     ⏳ 等待会话时长达到1分钟...")
                
                time.sleep(1)
            
            # 4. 暂停测试
            print("\n4. 测试暂停功能...")
            pause_success = study_service.pause_session()
            if pause_success:
                print("✅ 暂停成功")
                
                # 等待2秒，检查统计是否保持
                print("   等待2秒，检查统计是否保持...")
                pause_stats = study_service.get_daily_statistics()
                pause_minutes = pause_stats.get('total_minutes', 0)
                print(f"   暂停时统计: {pause_minutes}分钟")
                
                time.sleep(2)
                
                after_pause_stats = study_service.get_daily_statistics()
                after_pause_minutes = after_pause_stats.get('total_minutes', 0)
                print(f"   2秒后统计: {after_pause_minutes}分钟")
                
                if pause_minutes == after_pause_minutes:
                    print("   ✅ 暂停期间统计保持不变")
                else:
                    print("   ❌ 暂停期间统计发生变化")
                
                # 恢复学习
                print("\n5. 恢复学习...")
                resume_success = study_service.resume_session()
                if resume_success:
                    print("✅ 恢复成功")
                    
                    # 继续2秒
                    for i in range(2):
                        current_stats = study_service.get_daily_statistics()
                        total_minutes = current_stats.get('total_minutes', 0)
                        current_seconds = study_service.get_current_duration_seconds()
                        
                        print(f"   恢复第{i+1}秒: 会话{current_seconds}秒, 统计{total_minutes}分钟")
                        time.sleep(1)
                else:
                    print("❌ 恢复失败")
            else:
                print("❌ 暂停失败")
            
            # 6. 完成会话
            print("\n6. 完成学习会话...")
            final_stats_before = study_service.get_daily_statistics()
            final_minutes_before = final_stats_before.get('total_minutes', 0)
            
            success = study_service.complete_session()
            if success:
                print("✅ 学习会话已完成")
                
                # 检查完成后的统计
                final_stats_after = study_service.get_daily_statistics()
                final_minutes_after = final_stats_after.get('total_minutes', 0)
                final_sessions_after = final_stats_after.get('total_sessions', 0)
                
                print(f"   完成前统计: {final_minutes_before}分钟")
                print(f"   完成后统计: {final_minutes_after}分钟, {final_sessions_after}次")
                
                if final_sessions_after > initial_stats.get('total_sessions', 0):
                    print("   ✅ 会话计数正确增加")
                else:
                    print("   ❌ 会话计数未正确增加")
            else:
                print("❌ 停止会话失败")
                
        else:
            print("❌ 学习会话创建失败")
        
        print("\n" + "=" * 50)
        print("📊 测试总结:")
        print("✅ 预期结果: 统计应实时包含当前正在进行的会话")
        print("✅ 预期结果: 暂停期间统计应保持不变")
        print("✅ 预期结果: 恢复后统计应继续更新")
        print("✅ 预期结果: 完成后会话计数应增加")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_study_stats_realtime()
