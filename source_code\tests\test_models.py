"""
数据模型测试

测试所有数据模型的基本功能
"""

import pytest
import os
import sys
from datetime import datetime, timedelta

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.database import DatabaseManager
from core.config import ConfigManager
from models import *


class TestModels:
    """数据模型测试类"""
    
    @pytest.fixture(scope="class")
    def db_manager(self):
        """数据库管理器fixture"""
        config = ConfigManager()
        # 使用内存数据库进行测试
        config.config['database']['url'] = 'sqlite:///:memory:'
        db_manager = DatabaseManager(config)
        db_manager.create_tables()
        return db_manager
    
    @pytest.fixture
    def session(self, db_manager):
        """数据库会话fixture"""
        with db_manager.get_session() as session:
            yield session
            session.rollback()
    
    def test_base_model(self, session):
        """测试基础模型"""
        # 测试Setting模型
        Setting.set_setting(session, "test.key", "test_value", "测试设置", "test")
        setting = Setting.get_setting(session, "test.key")
        assert setting is not None
        assert setting.value == "test_value"
        assert setting.description == "测试设置"
        
        # 测试软删除
        setting.soft_delete()
        session.add(setting)
        session.flush()
        
        deleted_setting = Setting.get_setting(session, "test.key")
        assert deleted_setting is None
    
    def test_file_models(self, session):
        """测试文件管理模型"""
        # 测试文件标签
        tag = FileTag(name="测试标签", color="#FF0000", description="这是一个测试标签")
        session.add(tag)
        session.flush()
        
        # 测试文件标签关联
        file_path = "C:\\test\\file.txt"
        relation = FileTagRelation.add_tag_to_file(session, file_path, "测试标签")
        assert relation is not None
        
        # 获取文件标签
        tags = FileTagRelation.get_file_tags(session, file_path)
        assert len(tags) == 1
        assert tags[0].name == "测试标签"
        
        # 测试文件收藏
        bookmark = FileBookmark(
            name="测试文件",
            path=file_path,
            description="测试文件收藏",
            is_folder=False
        )
        session.add(bookmark)
        session.flush()
        
        found_bookmark = FileBookmark.get_by_path(session, file_path)
        assert found_bookmark is not None
        assert found_bookmark.name == "测试文件"
        
        # 测试文件历史
        history = FileHistory.record_access(
            session, file_path, "open", "file.txt", 1024, "text"
        )
        assert history.access_count == 1
        
        # 再次访问
        FileHistory.record_access(session, file_path, "open")
        session.refresh(history)
        assert history.access_count == 2
    
    def test_task_models(self, session):
        """测试任务管理模型"""
        # 测试任务分类
        category = TaskCategory(
            name="测试分类",
            description="测试任务分类",
            color="#00FF00"
        )
        session.add(category)
        session.flush()
        
        # 测试任务
        task = Task(
            title="测试任务",
            description="这是一个测试任务",
            priority=TaskPriority.HIGH,
            category_id=category.id,
            due_date=datetime.utcnow() + timedelta(days=7),
            estimated_hours=120
        )
        task.tag_list = ["测试", "重要"]
        session.add(task)
        session.flush()
        
        assert task.title == "测试任务"
        assert task.priority == TaskPriority.HIGH
        assert task.tag_list == ["测试", "重要"]
        assert task.days_until_due is not None
        assert not task.is_overdue
        
        # 测试任务状态变更
        task.start_task()
        assert task.status == TaskStatus.IN_PROGRESS
        assert task.start_date is not None
        
        task.mark_completed()
        assert task.status == TaskStatus.COMPLETED
        assert task.completed_date is not None
        assert task.progress == 100
        
        # 测试任务提醒
        reminder = TaskReminder(
            task_id=task.id,
            reminder_time=datetime.utcnow() + timedelta(hours=1),
            message="任务提醒测试"
        )
        session.add(reminder)
        session.flush()
        
        assert reminder.task_id == task.id
        assert not reminder.is_sent
    
    def test_system_models(self, session):
        """测试系统监控模型"""
        # 测试系统指标
        metrics_data = {
            "cpu_percent": 45.5,
            "memory_percent": 60.2,
            "disk_percent": 75.8,
            "memory_total": 8589934592,  # 8GB
            "memory_used": 5167382528,   # ~4.8GB
            "network_bytes_sent": 1024000,
            "network_bytes_recv": 2048000
        }
        
        metrics = SystemMetrics.record_metrics(session, metrics_data)
        assert metrics.cpu_percent == 45.5
        assert metrics.memory_percent == 60.2
        
        # 测试进程监控
        process_data = {
            "pid": 1234,
            "name": "test_process",
            "exe_path": "C:\\test\\process.exe",
            "cpu_percent": 25.0,
            "memory_percent": 15.5,
            "status": "running"
        }
        
        process = ProcessMonitor.record_process(session, process_data)
        assert process.pid == 1234
        assert process.name == "test_process"
        
        # 添加到监控列表
        success = ProcessMonitor.add_to_monitor(session, 1234, 80.0, 70.0)
        assert success
        session.refresh(process)
        assert process.is_monitored
        assert process.alert_cpu_threshold == 80.0
        
        # 测试系统告警
        alert = SystemAlert.create_alert(
            session,
            "cpu",
            "warning",
            "CPU使用率过高",
            "CPU使用率达到85%",
            "system_monitor",
            {"cpu_percent": 85.0}
        )
        assert alert.alert_type == "cpu"
        assert alert.severity == "warning"
        assert not alert.is_acknowledged
        
        alert.acknowledge()
        assert alert.is_acknowledged
        assert alert.acknowledged_at is not None
    
    def test_app_models(self, session):
        """测试应用和笔记模型"""
        # 测试应用
        app = Application(
            name="test_app",
            display_name="测试应用",
            description="这是一个测试应用",
            exe_path="C:\\test\\app.exe",
            category="测试工具"
        )
        app.tag_list = ["测试", "工具"]
        session.add(app)
        session.flush()
        
        assert app.name == "test_app"
        assert app.tag_list == ["测试", "工具"]
        assert app.launch_count == 0
        
        # 记录启动
        app.record_launch()
        assert app.launch_count == 1
        assert app.last_launched is not None
        
        # 测试笔记分类
        note_category = NoteCategory(
            name="测试笔记分类",
            description="测试笔记分类",
            color="#0000FF"
        )
        session.add(note_category)
        session.flush()
        
        # 测试笔记
        note = Note(
            title="测试笔记",
            content="这是一个测试笔记的内容。\n包含多行文本。",
            category_id=note_category.id,
            content_type="text"
        )
        note.tag_list = ["测试", "笔记"]
        note.update_word_count()
        session.add(note)
        session.flush()
        
        assert note.title == "测试笔记"
        assert note.tag_list == ["测试", "笔记"]
        assert note.word_count > 0
        assert note.reading_time > 0
        
        # 测试密码条目
        password_entry = PasswordEntry(
            title="测试网站",
            username="test_user",
            email="<EMAIL>",
            password_encrypted="encrypted_password_here",
            url="https://test.example.com",
            category="网站"
        )
        password_entry.tag_list = ["测试", "网站"]
        session.add(password_entry)
        session.flush()
        
        assert password_entry.title == "测试网站"
        assert password_entry.tag_list == ["测试", "网站"]
        assert password_entry.access_count == 0
        
        # 记录访问
        password_entry.record_access()
        assert password_entry.access_count == 1
        assert password_entry.last_accessed is not None


def test_model_relationships(session):
    """测试模型关联关系"""
    # 创建任务分类和任务
    category = TaskCategory(name="关系测试分类")
    session.add(category)
    session.flush()
    
    task = Task(
        title="关系测试任务",
        category_id=category.id
    )
    session.add(task)
    session.flush()
    
    # 测试关联关系
    assert task.category is not None
    assert task.category.name == "关系测试分类"
    assert len(category.tasks) == 1
    assert category.tasks[0].title == "关系测试任务"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
