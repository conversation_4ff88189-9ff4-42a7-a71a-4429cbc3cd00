#!/usr/bin/env python3
"""
Universal Widget Daemon - 造神计划
统一的桌面小组件守护进程启动脚本

Usage:
    python universal_widget_daemon.py desktop_task --manual
    python universal_widget_daemon.py study_floating --manual
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Import and run the universal daemon
from modules.common.universal_daemon import main

if __name__ == "__main__":
    main()