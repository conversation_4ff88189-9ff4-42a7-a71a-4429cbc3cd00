# 学习时间追踪悬浮小组件控制指南

## 概述

学习时间追踪悬浮小组件现在采用与任务管理模块相同的控制方式，通过主界面中的开关来管理悬浮小组件的启动和停止。悬浮小组件提供完整的学习计时功能，包括开始、暂停、继续和停止学习会话。

## 功能特性

### 🎛️ 界面控制
- **开关复选框**: 启用/禁用悬浮小组件
- **状态显示**: 实时显示悬浮小组件运行状态
- **显示按钮**: 手动显示已运行的悬浮小组件

### 🔧 后台管理
- **守护进程**: 自动管理悬浮小组件的生命周期
- **崩溃检测**: 自动重启异常退出的悬浮小组件
- **命令通信**: 通过文件通信机制控制悬浮小组件

### 📊 完整功能
- **学习计时**: 完整的学习时间记录功能
- **多状态控制**: 开始、暂停、继续、停止学习会话
- **数据同步**: 与主应用数据实时同步
- **悬浮显示**: 可拖拽的桌面悬浮窗口
- **智能科目**: 自动创建默认学习科目

## 使用方法

### 1. 启动学习时间追踪模块
1. 打开 Personal Manager 主应用
2. 在左侧导航栏点击"学习时间追踪"
3. 进入学习时间追踪主界面

### 2. 启用悬浮小组件
1. 在主界面顶部找到"学习追踪悬浮小组件"控制区域
2. 勾选"启用悬浮小组件"复选框
3. 等待状态显示为"运行中"
4. 悬浮小组件将自动显示在桌面上

### 3. 控制悬浮小组件
- **显示悬浮窗**: 点击"显示悬浮窗"按钮
- **隐藏悬浮窗**: 直接关闭悬浮窗口
- **停止悬浮小组件**: 取消勾选"启用悬浮小组件"

### 4. 使用悬浮小组件进行学习
1. **开始学习**: 点击绿色"开始"按钮
2. **暂停学习**: 点击橙色"暂停"按钮
3. **继续学习**: 暂停后点击"继续"按钮
4. **停止学习**: 点击红色"停止"按钮完成学习会话
5. **返回主界面**: 点击蓝色"主界面"按钮

### 4. 状态说明
- **检查中...**: 正在检查悬浮小组件状态
- **处理中...**: 正在启动或停止悬浮小组件
- **启动中...**: 悬浮小组件正在启动
- **运行中**: 悬浮小组件正常运行
- **已停止**: 悬浮小组件已停止
- **启动失败**: 悬浮小组件启动失败
- **停止失败**: 悬浮小组件停止失败

## 技术架构

### 核心组件

#### 1. StudyFloatingWidgetController
- 位置: `src/modules/study_tracker/study_floating_widget_controller.py`
- 功能: 管理悬浮小组件的启动、停止和状态监控
- 特性: 
  - 进程状态检查
  - 命令发送机制
  - 配置管理

#### 2. 守护进程 (study_floating_daemon.py)
- 位置: `study_floating_daemon.py`
- 功能: 后台管理悬浮小组件生命周期
- 特性:
  - 自动启动悬浮小组件
  - 崩溃检测和重启
  - 命令文件监听

#### 3. 启动脚本 (start_study_floating_widget.py)
- 位置: `start_study_floating_widget.py`
- 功能: 独立启动悬浮小组件
- 特性:
  - 延迟加载重型模块
  - 命令检查机制
  - 状态通知

#### 4. 悬浮小组件 (StudyFloatingWidget)
- 位置: `src/modules/study_tracker/study_floating_widget.py`
- 功能: 桌面悬浮计时器界面
- 特性:
  - 完整的学习计时功能
  - 命令响应机制
  - 拖拽和折叠功能

### 通信机制

#### 文件通信
```
主应用 → 守护进程: study_floating_command.txt
守护进程 → 悬浮小组件: study_floating_show_command.txt
悬浮小组件 → 主应用: study_floating_status.txt
```

#### 命令类型
- `manual_takeover`: 手动接管命令
- `show_widget`: 显示悬浮小组件
- `restart_widget`: 重启悬浮小组件
- `show`: 显示窗口命令

### 进程管理

#### 进程层次
```
主应用 (main.py)
├── StudyTrackerWidget
│   └── StudyFloatingWidgetController
└── 守护进程 (study_floating_daemon.py)
    └── 悬浮小组件 (start_study_floating_widget.py)
        └── StudyFloatingWidget
```

#### 状态监控
- 每2秒检查一次悬浮小组件状态
- 通过PID文件和进程列表双重检查
- 状态文件通知机制

## 配置选项

### 默认配置
```yaml
study_floating_widget:
  enabled: false
```

### 守护进程配置
```python
config = {
    "check_interval": 10,  # 检查间隔（秒）
    "auto_start_delay": 30,  # 自启动延迟（秒）
    "enable_crash_detection": True,  # 启用崩溃检测
    "max_restart_attempts": 3,  # 最大重启尝试次数
}
```

## 故障排除

### 常见问题

#### 1. 悬浮小组件启动失败
**可能原因**:
- Python 环境问题
- 依赖模块缺失
- 权限不足

**解决方案**:
- 检查 Python 环境和依赖
- 查看日志文件获取详细错误信息
- 以管理员身份运行应用程序

#### 2. 悬浮小组件无响应
**可能原因**:
- 进程异常退出
- 文件通信机制故障
- 系统资源不足

**解决方案**:
- 重新启用悬浮小组件
- 重启主应用程序
- 检查系统资源使用情况

#### 3. 状态显示异常
**可能原因**:
- 状态检查机制故障
- 文件权限问题
- 进程同步问题

**解决方案**:
- 刷新主界面
- 重新启动应用程序
- 检查文件系统权限

### 调试工具

#### 日志文件
- 主应用日志: `%APPDATA%\PersonalManager\logs\app.log`
- 包含悬浮小组件控制器的详细日志信息

#### 状态文件
- PID文件: `study_floating_daemon.pid`
- 命令文件: `study_floating_command.txt`
- 状态文件: `study_floating_status.txt`

#### 手动测试
```bash
# 手动启动守护进程
python study_floating_daemon.py --manual

# 手动启动悬浮小组件
python start_study_floating_widget.py
```

## 与任务管理模块的对比

### 相同点
- 使用相同的控制器架构
- 采用守护进程管理机制
- 文件通信方式
- 状态监控和显示
- 界面控制组件

### 不同点
- 专门针对学习时间追踪功能
- 独立的配置和文件路径
- 学习数据的特殊处理
- 与学习服务的集成

## 未来改进

### 计划功能
1. **自启动支持**: 系统启动时自动启用悬浮小组件
2. **多显示器支持**: 在多显示器环境中的位置管理
3. **主题定制**: 悬浮小组件的外观定制
4. **快捷操作**: 更多的快捷操作按钮

### 性能优化
1. **资源使用优化**: 减少内存和CPU占用
2. **启动速度优化**: 更快的启动响应
3. **稳定性改进**: 更好的异常处理和恢复

---

**注意**: 此控制方式与任务管理模块保持一致，提供了统一的用户体验。如果您熟悉任务管理模块的桌面小组件控制，那么学习追踪悬浮小组件的使用方式完全相同。
