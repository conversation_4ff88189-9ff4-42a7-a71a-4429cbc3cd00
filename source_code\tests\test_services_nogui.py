#!/usr/bin/env python3
"""
Service layer test script without GUI dependencies

This script tests the service layer functionality without requiring PyQt6.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_task_service():
    """Test task service functionality"""
    print("Testing task service...")
    
    try:
        # Initialize database first
        from core.config import Config<PERSON>anager
        from core.database import init_db_manager
        
        config = ConfigManager()
        init_db_manager(config=config)
        
        # Test task service
        from modules.task_manager.task_service import TaskService
        task_service = TaskService()
        
        # Test category creation with unique name
        import uuid
        unique_name = f"Test Service Category {str(uuid.uuid4())[:8]}"
        category = task_service.create_category(unique_name, "Test description")
        assert category is not None, "Failed to create category"
        # Access name while still in session context
        category_name = category.name
        category_id = category.id
        print(f"✓ Created category: {category_name}")
        
        # Test task creation
        task = task_service.create_task(
            title="Test Service Task",
            description="Test task created by service",
            category_id=category_id,
            priority="HIGH"
        )
        assert task is not None, "Failed to create task"
        # Access attributes while still in session context
        task_title = task.title
        task_id = task.id
        print(f"✓ Created task: {task_title}")
        
        # Test task retrieval
        retrieved_task = task_service.get_task(task_id)
        assert retrieved_task is not None, "Failed to retrieve task"
        assert retrieved_task.title == "Test Service Task", "Task title mismatch"
        print("✓ Task retrieval working")

        # Test task update
        success = task_service.update_task(task_id, status="IN_PROGRESS")
        assert success, "Failed to update task"
        print("✓ Task update working")

        # Test task completion
        success = task_service.complete_task(task_id)
        assert success, "Failed to complete task"
        print("✓ Task completion working")

        # Test task search
        search_results = task_service.search_tasks("Test Service")
        assert len(search_results) >= 0, "Search should return results"
        print("✓ Task search working")

        # Test getting tasks by category
        category_tasks = task_service.get_tasks(category_id=category_id)
        assert len(category_tasks) >= 0, "Should be able to get tasks by category"
        print("✓ Category filtering working")
        
        print("✓ Task service working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Task service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_service():
    """Test system service functionality"""
    print("\nTesting system service...")
    
    try:
        # Test system service (basic functionality)
        from modules.system_monitor.system_service import SystemService
        system_service = SystemService()
        
        # Test basic system info retrieval
        system_info = system_service.get_system_info()
        assert isinstance(system_info, dict), "System info should be a dictionary"
        assert 'platform' in system_info, "System info should contain platform"
        print(f"✓ System platform: {system_info.get('platform')}")
        
        # Test CPU info
        cpu_info = system_service.get_cpu_info()
        assert isinstance(cpu_info, dict), "CPU info should be a dictionary"
        print(f"✓ CPU cores: {cpu_info.get('cores', 'unknown')}")
        
        # Test memory info
        memory_info = system_service.get_memory_info()
        assert isinstance(memory_info, dict), "Memory info should be a dictionary"
        print(f"✓ Total memory: {memory_info.get('total', 'unknown')} bytes")
        
        # Test disk info
        disk_info = system_service.get_disk_info()
        assert isinstance(disk_info, list), "Disk info should be a list"
        print(f"✓ Found {len(disk_info)} disk(s)")
        
        print("✓ System service working correctly")
        return True
        
    except ImportError as e:
        print(f"⚠ System service requires psutil: {e}")
        return True  # Don't fail the test if psutil is not available
    except Exception as e:
        print(f"✗ System service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_service():
    """Test file service functionality"""
    print("\nTesting file service...")
    
    try:
        # Initialize database first
        from core.database import get_session
        
        # Test file service (basic functionality)
        from modules.file_manager.file_service import FileService
        file_service = FileService()
        
        # Test basic file operations (without actual file system operations)
        home_path = str(Path.home())
        print(f"✓ File service initialized, home path: {home_path}")
        
        # Test bookmark creation with unique path
        import tempfile
        import os
        temp_dir = tempfile.mkdtemp()
        try:
            success = file_service.add_bookmark(temp_dir, "Test Bookmark", "Test bookmark description")
            assert success, "Failed to create bookmark"
            print("✓ Created bookmark")
        finally:
            # Clean up temp directory
            try:
                os.rmdir(temp_dir)
            except:
                pass

        # Test bookmark retrieval
        bookmarks = file_service.get_bookmarks()
        assert len(bookmarks) >= 0, "Should be able to get bookmarks"
        print(f"✓ Found {len(bookmarks)} bookmark(s)")

        # Test basic file operations
        print("✓ Basic file operations working")
        
        print("✓ File service working correctly")
        return True
        
    except ImportError as e:
        print(f"⚠ File service requires additional dependencies: {e}")
        return True  # Don't fail the test if dependencies are not available
    except Exception as e:
        print(f"✗ File service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all service tests"""
    print("Personal Manager System - Service Test Suite (No GUI)")
    print("=" * 60)
    
    tests = [
        test_task_service,
        test_system_service,
        test_file_service
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Service Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All service tests passed! The service layer is working correctly.")
        return 0
    else:
        print("❌ Some service tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
