"""
DEPRECATED: Editable Task Table Component for Task Manager

This component has been COMPLETELY REPLACED by WeeklyTaskTable.
This file is kept for reference only and should not be used in new code.

Use WeeklyTaskTable instead for the new weekly task management interface.
"""

from datetime import datetime, date
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QComboBox, QDateTimeEdit, QLineEdit, QTextEdit, QCheckBox,
    QWidget, QHBoxLayout, QPushButton, QMessageBox, QMenu,
    QVBoxLayout, QLabel, QSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QDateTime, QDate
from PyQt6.QtGui import QFont, QColor, QBrush, QAction

from core.logger import LoggerMixin
from models.task_models import Task, TaskStatus, TaskPriority


class EditableTaskTable(QTableWidget, LoggerMixin):
    """Editable table widget for tasks with inline editing capabilities"""
    
    # Signals
    task_updated = pyqtSignal(str, dict)  # task_id, updated_data
    task_deleted = pyqtSignal(str)  # task_id
    new_task_requested = pyqtSignal(dict)  # task_data
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.tasks = []
        self.categories = []
        self.setup_table()
        self.setup_context_menu()
    
    def setup_table(self):
        """Setup table properties and headers"""
        # Set column count and headers
        headers = [
            "标题", "描述", "状态", "优先级", "分类", 
            "截止日期", "进度", "创建时间", "操作"
        ]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # Set table properties
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.setSortingEnabled(True)
        
        # Set header properties
        header = self.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # Title
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Interactive)  # Description
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Status
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Priority
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Interactive)  # Category
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Interactive)  # Due Date
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Progress
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Interactive)  # Created
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.ResizeToContents)  # Actions
        
        # Set column widths
        self.setColumnWidth(1, 200)  # Description
        self.setColumnWidth(4, 120)  # Category
        self.setColumnWidth(5, 150)  # Due Date
        self.setColumnWidth(7, 150)  # Created
        
        # Enable editing
        self.setEditTriggers(QAbstractItemView.EditTrigger.DoubleClicked | 
                           QAbstractItemView.EditTrigger.EditKeyPressed)
        
        # Connect signals
        self.itemChanged.connect(self.on_item_changed)
        self.cellDoubleClicked.connect(self.on_cell_double_clicked)
    
    def setup_context_menu(self):
        """Setup context menu for table"""
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def show_context_menu(self, position):
        """Show context menu at position"""
        item = self.itemAt(position)
        if item is None:
            return
        
        menu = QMenu(self)
        
        # Edit action
        edit_action = QAction("编辑任务", self)
        edit_action.triggered.connect(lambda: self.edit_task_at_row(item.row()))
        menu.addAction(edit_action)
        
        # Delete action
        delete_action = QAction("删除任务", self)
        delete_action.triggered.connect(lambda: self.delete_task_at_row(item.row()))
        menu.addAction(delete_action)
        
        menu.addSeparator()
        
        # Mark complete action
        complete_action = QAction("标记完成", self)
        complete_action.triggered.connect(lambda: self.mark_task_complete(item.row()))
        menu.addAction(complete_action)
        
        menu.exec(self.mapToGlobal(position))
    
    def update_tasks(self, tasks: List[Task], categories: List = None):
        """Update table with new task data"""
        self.tasks = tasks
        if categories is not None:
            self.categories = categories
        
        # Clear existing rows
        self.setRowCount(0)
        
        # Add tasks
        for task in tasks:
            self.add_task_row(task)
        
        # Add empty row for new task
        self.add_new_task_row()
    
    def add_task_row(self, task: Task):
        """Add a task row to the table"""
        row = self.rowCount()
        self.insertRow(row)
        
        # Title
        title_item = QTableWidgetItem(task.title or "")
        title_item.setData(Qt.ItemDataRole.UserRole, task.id)
        self.setItem(row, 0, title_item)
        
        # Description
        desc_item = QTableWidgetItem(task.description or "")
        self.setItem(row, 1, desc_item)
        
        # Status
        status_combo = QComboBox()
        status_combo.addItems(["pending", "in_progress", "completed", "cancelled"])
        status_combo.setCurrentText(task.status.value if task.status else "pending")
        status_combo.currentTextChanged.connect(
            lambda text, r=row: self.on_status_changed(r, text)
        )
        self.setCellWidget(row, 2, status_combo)
        
        # Priority
        priority_combo = QComboBox()
        priority_combo.addItems(["low", "normal", "high", "urgent"])
        priority_combo.setCurrentText(task.priority.value if task.priority else "normal")
        priority_combo.currentTextChanged.connect(
            lambda text, r=row: self.on_priority_changed(r, text)
        )
        self.setCellWidget(row, 3, priority_combo)
        
        # Category
        category_combo = QComboBox()
        category_combo.addItem("无分类", None)
        for category in self.categories:
            category_combo.addItem(category.name, category.id)
        if task.category_id:
            index = category_combo.findData(task.category_id)
            if index >= 0:
                category_combo.setCurrentIndex(index)
        category_combo.currentTextChanged.connect(
            lambda text, r=row: self.on_category_changed(r, text)
        )
        self.setCellWidget(row, 4, category_combo)
        
        # Due Date
        due_date_edit = QDateTimeEdit()
        due_date_edit.setCalendarPopup(True)
        due_date_edit.setDisplayFormat("yyyy-MM-dd hh:mm")
        if task.due_date:
            due_date_edit.setDateTime(QDateTime.fromSecsSinceEpoch(int(task.due_date.timestamp())))
        else:
            due_date_edit.setDateTime(QDateTime.currentDateTime())
        due_date_edit.dateTimeChanged.connect(
            lambda dt, r=row: self.on_due_date_changed(r, dt)
        )
        self.setCellWidget(row, 5, due_date_edit)
        
        # Progress
        progress_spin = QSpinBox()
        progress_spin.setRange(0, 100)
        progress_spin.setSuffix("%")
        progress_spin.setValue(task.progress or 0)
        progress_spin.valueChanged.connect(
            lambda value, r=row: self.on_progress_changed(r, value)
        )
        self.setCellWidget(row, 6, progress_spin)
        
        # Created time
        created_item = QTableWidgetItem(
            task.created_at.strftime("%Y-%m-%d %H:%M") if task.created_at else ""
        )
        created_item.setFlags(created_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
        self.setItem(row, 7, created_item)
        
        # Actions
        self.create_action_buttons(row)
        
        # Set row color based on status
        self.set_row_color(row, task.status.value if task.status else "pending")
    
    def add_new_task_row(self):
        """Add an empty row for creating new tasks"""
        row = self.rowCount()
        self.insertRow(row)
        
        # Title (placeholder)
        title_item = QTableWidgetItem("点击输入新任务...")
        title_item.setData(Qt.ItemDataRole.UserRole, "NEW_TASK")
        title_item.setForeground(QBrush(QColor(128, 128, 128)))
        self.setItem(row, 0, title_item)
        
        # Empty cells for other columns
        for col in range(1, 8):
            item = QTableWidgetItem("")
            if col == 7:  # Created time - not editable
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.setItem(row, col, item)
        
        # Add button for new task
        add_button = QPushButton("添加")
        add_button.clicked.connect(lambda: self.create_new_task(row))
        self.setCellWidget(row, 8, add_button)
    
    def create_action_buttons(self, row):
        """Create action buttons for a task row"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)
        
        # Edit button
        edit_btn = QPushButton("编辑")
        edit_btn.setMaximumWidth(50)
        edit_btn.clicked.connect(lambda: self.edit_task_at_row(row))
        layout.addWidget(edit_btn)
        
        # Delete button
        delete_btn = QPushButton("删除")
        delete_btn.setMaximumWidth(50)
        delete_btn.clicked.connect(lambda: self.delete_task_at_row(row))
        layout.addWidget(delete_btn)
        
        self.setCellWidget(row, 8, widget)
    
    def set_row_color(self, row, status):
        """Set row background color based on task status"""
        colors = {
            "pending": QColor(255, 255, 255),      # White
            "in_progress": QColor(255, 248, 220),  # Light yellow
            "completed": QColor(240, 255, 240),    # Light green
            "cancelled": QColor(255, 240, 240)     # Light red
        }
        
        color = colors.get(status, QColor(255, 255, 255))
        for col in range(self.columnCount()):
            item = self.item(row, col)
            if item:
                item.setBackground(QBrush(color))
    
    def on_item_changed(self, item):
        """Handle item changes"""
        if item.data(Qt.ItemDataRole.UserRole) == "NEW_TASK":
            return
        
        task_id = self.get_task_id_from_row(item.row())
        if not task_id:
            return
        
        column = item.column()
        value = item.text()
        
        # Map column to field
        field_map = {
            0: "title",
            1: "description"
        }
        
        field = field_map.get(column)
        if field:
            self.task_updated.emit(task_id, {field: value})
    
    def on_status_changed(self, row, status):
        """Handle status change"""
        task_id = self.get_task_id_from_row(row)
        if task_id:
            self.task_updated.emit(task_id, {"status": status})
            self.set_row_color(row, status)
    
    def on_priority_changed(self, row, priority):
        """Handle priority change"""
        task_id = self.get_task_id_from_row(row)
        if task_id:
            self.task_updated.emit(task_id, {"priority": priority})
    
    def on_category_changed(self, row, category_name):
        """Handle category change"""
        task_id = self.get_task_id_from_row(row)
        if task_id:
            # Find category ID by name
            category_id = None
            for category in self.categories:
                if category.name == category_name:
                    category_id = category.id
                    break
            self.task_updated.emit(task_id, {"category_id": category_id})
    
    def on_due_date_changed(self, row, qdt):
        """Handle due date change"""
        task_id = self.get_task_id_from_row(row)
        if task_id:
            due_date = qdt.toPython()
            self.task_updated.emit(task_id, {"due_date": due_date})
    
    def on_progress_changed(self, row, progress):
        """Handle progress change"""
        task_id = self.get_task_id_from_row(row)
        if task_id:
            self.task_updated.emit(task_id, {"progress": progress})
    
    def on_cell_double_clicked(self, row, column):
        """Handle cell double click"""
        if column in [2, 3, 4, 5, 6, 8]:  # Skip combo boxes and buttons
            return
        
        task_id = self.get_task_id_from_row(row)
        if task_id == "NEW_TASK":
            self.create_new_task(row)
    
    def get_task_id_from_row(self, row):
        """Get task ID from row"""
        item = self.item(row, 0)
        return item.data(Qt.ItemDataRole.UserRole) if item else None
    
    def create_new_task(self, row):
        """Create new task from row data"""
        title = self.item(row, 0).text().strip()
        if not title or title == "点击输入新任务...":
            QMessageBox.warning(self, "错误", "请输入任务标题")
            return
        
        description = self.item(row, 1).text().strip()
        
        task_data = {
            "title": title,
            "description": description,
            "status": "pending",
            "priority": "normal",
            "category_id": None,
            "due_date": datetime.now(),
            "progress": 0,
            "tags": []  # Add tags field
        }
        
        self.new_task_requested.emit(task_data)
    
    def edit_task_at_row(self, row):
        """Edit task at specific row"""
        task_id = self.get_task_id_from_row(row)
        if task_id and task_id != "NEW_TASK":
            # Emit signal to open edit dialog
            pass
    
    def delete_task_at_row(self, row):
        """Delete task at specific row"""
        task_id = self.get_task_id_from_row(row)
        if task_id and task_id != "NEW_TASK":
            reply = QMessageBox.question(
                self, "确认删除", "确定要删除这个任务吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.task_deleted.emit(task_id)
    
    def mark_task_complete(self, row):
        """Mark task as complete"""
        task_id = self.get_task_id_from_row(row)
        if task_id and task_id != "NEW_TASK":
            self.task_updated.emit(task_id, {"status": "completed", "progress": 100})
