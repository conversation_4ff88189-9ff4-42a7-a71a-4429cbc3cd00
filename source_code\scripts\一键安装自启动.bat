@echo off
chcp 65001 >nul
title 桌面任务小部件 - 一键安装自启动

echo ========================================
echo 桌面任务小部件 - 一键安装自启动
echo ========================================
echo.
echo 此脚本将配置桌面任务小部件开机自动启动
echo 安装后，桌面小部件将在系统启动时自动运行
echo 并通过守护进程确保持续运行，无需手动干预
echo.

cd /d "%~dp0"

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请确保已安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo 检查依赖包...
python -c "import PyQt6, psutil" >nul 2>&1
if errorlevel 1 (
    echo 正在安装必要的依赖包...
    pip install PyQt6 psutil
    if errorlevel 1 (
        echo 依赖包安装失败
        echo 请手动安装: pip install PyQt6 psutil
        pause
        exit /b 1
    )
)

echo.
echo 选择安装方式:
echo 1. 注册表自启动（推荐）
echo 2. 启动文件夹
echo 3. 任务计划程序
echo 4. 全部安装
echo 5. 取消
echo.
set /p choice="请选择 (1-5): "

if "%choice%"=="1" goto registry
if "%choice%"=="2" goto startup_folder
if "%choice%"=="3" goto task_scheduler
if "%choice%"=="4" goto install_all
if "%choice%"=="5" goto cancel
goto invalid_choice

:registry
echo.
echo 正在安装注册表自启动...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "桌面任务小部件" /t REG_SZ /d "\"%~dp0start_daemon.bat\"" /f >nul 2>&1
if errorlevel 1 (
    echo 注册表自启动安装失败
) else (
    echo 注册表自启动安装成功
)
goto test_install

:startup_folder
echo.
echo 正在安装启动文件夹...
set startup_folder=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup
echo @echo off > "%startup_folder%\桌面任务小部件.bat"
echo cd /d "%~dp0" >> "%startup_folder%\桌面任务小部件.bat"
echo start "" "%~dp0start_daemon.bat" >> "%startup_folder%\桌面任务小部件.bat"
echo 启动文件夹安装成功
goto test_install

:task_scheduler
echo.
echo 正在安装任务计划程序...
schtasks /create /tn "桌面任务小部件" /tr "\"%~dp0start_daemon.bat\"" /sc onlogon /rl limited /f >nul 2>&1
if errorlevel 1 (
    echo 任务计划程序安装失败
) else (
    echo 任务计划程序安装成功
)
goto test_install

:install_all
echo.
echo 正在安装所有自启动方式...

echo 1. 注册表自启动...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "桌面任务小部件" /t REG_SZ /d "\"%~dp0start_daemon.bat\"" /f >nul 2>&1

echo 2. 启动文件夹...
set startup_folder=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup
echo @echo off > "%startup_folder%\桌面任务小部件.bat"
echo cd /d "%~dp0" >> "%startup_folder%\桌面任务小部件.bat"
echo start "" "%~dp0start_daemon.bat" >> "%startup_folder%\桌面任务小部件.bat"

echo 3. 任务计划程序...
schtasks /create /tn "桌面任务小部件" /tr "\"%~dp0start_daemon.bat\"" /sc onlogon /rl limited /f >nul 2>&1

echo 全部安装完成
goto test_install

:test_install
echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 自启动已配置完成，桌面小部件将在下次开机时自动启动
echo.
echo 现在是否立即启动桌面小部件测试？
echo 1. 是，立即启动
echo 2. 否，稍后手动启动
echo.
set /p test_choice="请选择 (1-2): "

if "%test_choice%"=="1" (
    echo.
    echo 正在启动桌面小部件...
    call start_daemon.bat
) else (
    echo.
    echo 您可以稍后运行 start_daemon.bat 来启动桌面小部件
)

echo.
echo 使用说明:
echo - 桌面小部件将在开机时自动启动
echo - 守护进程会确保小部件持续运行
echo - 如需停止，请运行 stop_daemon.bat
echo - 如需卸载自启动，请运行 卸载自启动.bat
echo.
goto end

:invalid_choice
echo 无效选择，请重新运行脚本
goto end

:cancel
echo 安装已取消
goto end

:end
echo 按任意键退出...
pause >nul
