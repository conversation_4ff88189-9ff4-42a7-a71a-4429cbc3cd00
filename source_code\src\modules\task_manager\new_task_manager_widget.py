"""
DEPRECATED: New Task Manager Widget for Personal Manager System

This widget has been COMPLETELY REPLACED by WeeklyTaskManagerWidget.
This file is kept for reference only and should not be used in new code.

Use WeeklyTaskManagerWidget instead for the new weekly task management interface.
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLineEdit, QComboBox, QLabel,
    QMessageBox, QSplitter, QFrame, QGroupBox,
    QToolBar, QSpacerItem, QSizePolicy, QCheckBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QAction, QIcon

from core.logger import LoggerMixin
from .task_service import TaskService
from .date_week_display import DateWeekDisplay
from .editable_task_table import EditableTaskTable


class NewTaskManagerWidget(QWidget, LoggerMixin):
    """New main task manager widget with enhanced features"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.task_service = TaskService()
        self.current_tasks = []
        self.categories = []
        self.init_ui()
        self.connect_signals()
        self.load_data()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Create header section
        self.create_header_section()
        layout.addWidget(self.header_frame)
        
        # Create toolbar
        self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # Create main content
        self.create_main_content()
        layout.addWidget(self.main_content)
        
        # Set window properties
        self.setWindowTitle("任务管理")
    
    def create_header_section(self):
        """Create header section with date/week display and quick stats"""
        self.header_frame = QFrame()
        self.header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_layout = QHBoxLayout(self.header_frame)
        header_layout.setContentsMargins(10, 10, 10, 10)
        
        # Date and week display
        self.date_week_display = DateWeekDisplay()
        header_layout.addWidget(self.date_week_display)
        
        # Quick stats
        self.create_quick_stats()
        header_layout.addWidget(self.stats_group)
        
        # Spacer
        header_layout.addStretch()
        
        # Quick actions
        self.create_quick_actions()
        header_layout.addWidget(self.quick_actions_group)
    
    def create_quick_stats(self):
        """Create quick statistics display"""
        self.stats_group = QGroupBox("任务统计")
        stats_layout = QGridLayout(self.stats_group)
        
        # Total tasks
        self.total_tasks_label = QLabel("总任务: 0")
        stats_layout.addWidget(self.total_tasks_label, 0, 0)
        
        # Pending tasks
        self.pending_tasks_label = QLabel("待处理: 0")
        stats_layout.addWidget(self.pending_tasks_label, 0, 1)
        
        # In progress tasks
        self.in_progress_tasks_label = QLabel("进行中: 0")
        stats_layout.addWidget(self.in_progress_tasks_label, 1, 0)
        
        # Completed tasks
        self.completed_tasks_label = QLabel("已完成: 0")
        stats_layout.addWidget(self.completed_tasks_label, 1, 1)
        
        # Overdue tasks
        self.overdue_tasks_label = QLabel("逾期: 0")
        self.overdue_tasks_label.setStyleSheet("color: red; font-weight: bold;")
        stats_layout.addWidget(self.overdue_tasks_label, 2, 0, 1, 2)
    
    def create_quick_actions(self):
        """Create quick action buttons"""
        self.quick_actions_group = QGroupBox("快速操作")
        actions_layout = QVBoxLayout(self.quick_actions_group)
        
        # Add new task button
        self.add_task_btn = QPushButton("添加任务")
        self.add_task_btn.clicked.connect(self.add_new_task)
        actions_layout.addWidget(self.add_task_btn)
        
        # Refresh button
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)
    
    def create_toolbar(self):
        """Create toolbar with filters and search"""
        self.toolbar = QFrame()
        self.toolbar.setFrameStyle(QFrame.Shape.StyledPanel)
        toolbar_layout = QHBoxLayout(self.toolbar)
        toolbar_layout.setContentsMargins(10, 5, 10, 5)
        
        # Search
        toolbar_layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索任务...")
        self.search_edit.textChanged.connect(self.search_tasks)
        toolbar_layout.addWidget(self.search_edit)
        
        # Status filter
        toolbar_layout.addWidget(QLabel("状态:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["全部", "待处理", "进行中", "已完成", "已取消"])
        self.status_filter.currentTextChanged.connect(self.filter_by_status)
        toolbar_layout.addWidget(self.status_filter)
        
        # Priority filter
        toolbar_layout.addWidget(QLabel("优先级:"))
        self.priority_filter = QComboBox()
        self.priority_filter.addItems(["全部", "低", "普通", "高", "紧急"])
        self.priority_filter.currentTextChanged.connect(self.filter_by_priority)
        toolbar_layout.addWidget(self.priority_filter)
        
        # Category filter
        toolbar_layout.addWidget(QLabel("分类:"))
        self.category_filter = QComboBox()
        self.category_filter.currentTextChanged.connect(self.filter_by_category)
        toolbar_layout.addWidget(self.category_filter)
        
        # Spacer
        toolbar_layout.addStretch()
        
        # View options
        self.show_completed_checkbox = QComboBox()
        self.show_completed_checkbox.addItems(["显示已完成", "隐藏已完成"])
        self.show_completed_checkbox.currentTextChanged.connect(self.toggle_completed_tasks)
        toolbar_layout.addWidget(self.show_completed_checkbox)
    
    def create_main_content(self):
        """Create main content area with editable task table"""
        self.main_content = QFrame()
        content_layout = QVBoxLayout(self.main_content)
        content_layout.setContentsMargins(5, 5, 5, 5)
        
        # Task table
        self.task_table = EditableTaskTable()
        content_layout.addWidget(self.task_table)
        
        # Status bar
        self.status_label = QLabel("就绪")
        content_layout.addWidget(self.status_label)
    
    def connect_signals(self):
        """Connect signals from components"""
        # Task table signals
        self.task_table.task_updated.connect(self.on_task_updated)
        self.task_table.task_deleted.connect(self.on_task_deleted)
        self.task_table.new_task_requested.connect(self.on_new_task_requested)
    
    def load_data(self):
        """Load task and category data"""
        try:
            # Load categories
            self.categories = self.task_service.get_categories()
            self.update_category_filter()
            
            # Load tasks
            self.current_tasks = self.task_service.get_tasks()
            self.task_table.update_tasks(self.current_tasks, self.categories)
            
            # Update statistics
            self.update_statistics()
            
            self.status_label.setText(f"已加载 {len(self.current_tasks)} 个任务")
            self.logger.info(f"Loaded {len(self.current_tasks)} tasks")
            
        except Exception as e:
            self.logger.error(f"Error loading data: {e}")
            QMessageBox.warning(self, "错误", f"加载数据时出错: {str(e)}")
    
    def update_category_filter(self):
        """Update category filter dropdown"""
        self.category_filter.clear()
        self.category_filter.addItem("全部")
        for category in self.categories:
            self.category_filter.addItem(category.name)
    
    def update_statistics(self):
        """Update task statistics display"""
        total = len(self.current_tasks)
        pending = sum(1 for task in self.current_tasks if task.status.value == "pending")
        in_progress = sum(1 for task in self.current_tasks if task.status.value == "in_progress")
        completed = sum(1 for task in self.current_tasks if task.status.value == "completed")
        
        # Calculate overdue tasks
        now = datetime.now()
        overdue = sum(1 for task in self.current_tasks 
                     if task.due_date and task.due_date < now and task.status.value != "completed")
        
        self.total_tasks_label.setText(f"总任务: {total}")
        self.pending_tasks_label.setText(f"待处理: {pending}")
        self.in_progress_tasks_label.setText(f"进行中: {in_progress}")
        self.completed_tasks_label.setText(f"已完成: {completed}")
        self.overdue_tasks_label.setText(f"逾期: {overdue}")
    
    def search_tasks(self, query: str):
        """Search tasks by query"""
        if not query.strip():
            self.current_tasks = self.task_service.get_tasks()
        else:
            self.current_tasks = self.task_service.search_tasks(query)
        
        self.task_table.update_tasks(self.current_tasks, self.categories)
        self.update_statistics()
    
    def filter_by_status(self, status_text: str):
        """Filter tasks by status"""
        status_map = {
            "全部": None,
            "待处理": "pending",
            "进行中": "in_progress",
            "已完成": "completed",
            "已取消": "cancelled"
        }
        
        status = status_map.get(status_text)
        if status:
            self.current_tasks = self.task_service.get_tasks(status=status)
        else:
            self.current_tasks = self.task_service.get_tasks()
        
        self.task_table.update_tasks(self.current_tasks, self.categories)
        self.update_statistics()
    
    def filter_by_priority(self, priority_text: str):
        """Filter tasks by priority"""
        priority_map = {
            "全部": None,
            "低": "low",
            "普通": "normal",
            "高": "high",
            "紧急": "urgent"
        }
        
        priority = priority_map.get(priority_text)
        if priority:
            self.current_tasks = self.task_service.get_tasks(priority=priority)
        else:
            self.current_tasks = self.task_service.get_tasks()
        
        self.task_table.update_tasks(self.current_tasks, self.categories)
        self.update_statistics()
    
    def filter_by_category(self, category_name: str):
        """Filter tasks by category"""
        if category_name == "全部":
            self.current_tasks = self.task_service.get_tasks()
        else:
            # Find category ID
            category_id = None
            for category in self.categories:
                if category.name == category_name:
                    category_id = category.id
                    break
            
            if category_id:
                self.current_tasks = self.task_service.get_tasks(category_id=category_id)
            else:
                self.current_tasks = []
        
        self.task_table.update_tasks(self.current_tasks, self.categories)
        self.update_statistics()
    
    def toggle_completed_tasks(self, option: str):
        """Toggle display of completed tasks"""
        if option == "隐藏已完成":
            self.current_tasks = [task for task in self.current_tasks 
                                if task.status.value != "completed"]
        else:
            self.load_data()
            return
        
        self.task_table.update_tasks(self.current_tasks, self.categories)
        self.update_statistics()
    
    def add_new_task(self):
        """Add new task manually"""
        # This will trigger the new task row in the table
        self.task_table.scrollToBottom()
    
    def on_task_updated(self, task_id: str, updated_data: dict):
        """Handle task update from table"""
        try:
            success = self.task_service.update_task(task_id, **updated_data)
            if success:
                self.status_label.setText("任务已更新")
                # Refresh data to reflect changes
                self.load_data()
            else:
                QMessageBox.warning(self, "错误", "更新任务失败")
        except Exception as e:
            self.logger.error(f"Error updating task {task_id}: {e}")
            QMessageBox.warning(self, "错误", f"更新任务时出错: {str(e)}")
    
    def on_task_deleted(self, task_id: str):
        """Handle task deletion from table"""
        try:
            success = self.task_service.delete_task(task_id)
            if success:
                self.status_label.setText("任务已删除")
                self.load_data()
            else:
                QMessageBox.warning(self, "错误", "删除任务失败")
        except Exception as e:
            self.logger.error(f"Error deleting task {task_id}: {e}")
            QMessageBox.warning(self, "错误", f"删除任务时出错: {str(e)}")
    
    def on_new_task_requested(self, task_data: dict):
        """Handle new task creation from table"""
        try:
            task = self.task_service.create_task(**task_data)
            if task:
                self.status_label.setText("新任务已创建")
                self.load_data()
            else:
                QMessageBox.warning(self, "错误", "创建任务失败")
        except Exception as e:
            self.logger.error(f"Error creating new task: {e}")
            QMessageBox.warning(self, "错误", f"创建任务时出错: {str(e)}")
