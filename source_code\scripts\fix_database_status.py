#!/usr/bin/env python3
"""
数据库状态修复脚本
修复数据库中的旧状态值，将小写的状态值转换为正确的枚举值
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'src'))

from src.core.database import DatabaseManager, get_session
from src.models.task_models import Task, TaskStatus
from src.core.config import Config
from sqlalchemy import text

def fix_task_status(db_manager):
    """修复任务状态值"""
    print("开始修复任务状态值...")

    try:
        with db_manager.get_session() as session:
            # 查询所有任务
            tasks = session.query(Task).all()
            
            fixed_count = 0
            for task in tasks:
                old_status = task.status
                
                # 如果状态是字符串，需要转换为枚举
                if isinstance(task.status, str):
                    status_map = {
                        'pending': TaskStatus.PENDING,
                        'in_progress': TaskStatus.IN_PROGRESS,
                        'completed': TaskStatus.COMPLETED,
                        'cancelled': TaskStatus.CANCELLED,
                        'paused': TaskStatus.PAUSED
                    }
                    
                    if task.status in status_map:
                        task.status = status_map[task.status]
                        fixed_count += 1
                        print(f"修复任务 {task.id}: {old_status} -> {task.status}")
            
            # 提交更改
            session.commit()
            print(f"修复完成！共修复了 {fixed_count} 个任务的状态值。")
            
    except Exception as e:
        print(f"修复过程中出现错误: {e}")
        return False
    
    return True

def clean_invalid_status(db_manager):
    """清理无效的状态值"""
    print("开始清理无效的状态值...")

    try:
        with db_manager.get_session() as session:
            # 直接使用SQL更新无效的状态值
            result = session.execute(text("""
                UPDATE tasks 
                SET status = 'COMPLETED' 
                WHERE status = 'completed'
            """))
            
            result2 = session.execute(text("""
                UPDATE tasks 
                SET status = 'PENDING' 
                WHERE status = 'pending'
            """))
            
            result3 = session.execute(text("""
                UPDATE tasks 
                SET status = 'IN_PROGRESS' 
                WHERE status = 'in_progress'
            """))
            
            result4 = session.execute(text("""
                UPDATE tasks 
                SET status = 'CANCELLED' 
                WHERE status = 'cancelled'
            """))
            
            session.commit()
            
            total_fixed = result.rowcount + result2.rowcount + result3.rowcount + result4.rowcount
            print(f"SQL清理完成！共修复了 {total_fixed} 个状态值。")
            
    except Exception as e:
        print(f"SQL清理过程中出现错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("=== 数据库状态修复工具 ===")

    # 初始化配置和数据库
    try:
        config = Config()
        db_manager = DatabaseManager(config=config)
        db_manager.initialize()
        print("数据库初始化成功！")
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        sys.exit(1)

    # 先尝试SQL清理
    if clean_invalid_status(db_manager):
        print("SQL清理成功！")
    else:
        print("SQL清理失败，尝试ORM方式...")
        if fix_task_status(db_manager):
            print("ORM修复成功！")
        else:
            print("修复失败！")
            sys.exit(1)

    print("数据库状态修复完成！")
