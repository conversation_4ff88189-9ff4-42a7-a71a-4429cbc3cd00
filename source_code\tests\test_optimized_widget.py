#!/usr/bin/env python3
"""
测试优化后的桌面小部件
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
import time

def test_startup_performance():
    """测试启动性能"""
    print("开始测试桌面小部件启动性能...")
    
    app = QApplication(sys.argv)
    
    # 记录启动时间
    start_time = time.time()
    
    # 导入并创建小部件
    from src.modules.task_manager.desktop_widget import DesktopTaskWidget
    
    import_time = time.time()
    print(f"模块导入时间: {import_time - start_time:.3f}秒")
    
    # 创建小部件
    widget = DesktopTaskWidget()
    
    creation_time = time.time()
    print(f"小部件创建时间: {creation_time - import_time:.3f}秒")
    
    # 显示小部件
    widget.show()
    
    show_time = time.time()
    print(f"小部件显示时间: {show_time - creation_time:.3f}秒")
    print(f"总启动时间: {show_time - start_time:.3f}秒")
    
    # 等待延迟初始化完成
    def check_initialization():
        if hasattr(widget, 'task_service') and widget.task_service is not None:
            init_complete_time = time.time()
            print(f"延迟初始化完成时间: {init_complete_time - show_time:.3f}秒")
            print(f"完整初始化总时间: {init_complete_time - start_time:.3f}秒")
            
            # 测试任务加载
            if hasattr(widget, 'tasks_by_category'):
                total_tasks = sum(len(tasks) for tasks in widget.tasks_by_category.values())
                print(f"已加载任务数量: {total_tasks}")
            
            # 5秒后关闭
            QTimer.singleShot(5000, app.quit)
        else:
            # 继续检查
            QTimer.singleShot(100, check_initialization)
    
    # 开始检查初始化状态
    QTimer.singleShot(100, check_initialization)
    
    # 运行应用
    app.exec()

if __name__ == "__main__":
    test_startup_performance()
