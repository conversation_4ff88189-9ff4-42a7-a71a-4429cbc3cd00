"""
Common modules for Personal Manager System

This package contains shared components and base classes used across
different modules in the system.
"""

from .base_desktop_widget import BaseDesktopWidget, create_widget_application
from .widget_controller import UnifiedWidgetController
from .communication_manager import WidgetCommunicationManager
from .process_manager import WidgetProcessManager
from .universal_daemon import UniversalDaemon

__all__ = [
    'BaseDesktopWidget',
    'create_widget_application',
    'UnifiedWidgetController',
    'WidgetCommunicationManager',
    'WidgetProcessManager',
    'UniversalDaemon'
]