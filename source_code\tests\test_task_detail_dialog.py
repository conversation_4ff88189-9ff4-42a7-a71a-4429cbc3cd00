#!/usr/bin/env python3
"""
测试任务详情对话框功能
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from PyQt6.QtWidgets import QApplication, QVBoxLayout, QWidget, QPushButton
from PyQt6.QtCore import QTimer

def test_task_detail_dialog():
    """测试任务详情对话框"""
    print("开始测试任务详情对话框...")
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = QWidget()
    test_window.setWindowTitle("任务详情对话框测试")
    test_window.setGeometry(100, 100, 300, 200)
    
    layout = QVBoxLayout(test_window)
    
    # 模拟任务数据
    test_task_data = {
        'id': 'test-task-123',
        'title': '测试任务 - 点击查看详情',
        'description': '这是一个测试任务的描述',
        'status': '待处理',
        'priority': '普通',
        'completed': False
    }
    
    # 创建任务卡片
    from src.modules.task_manager.desktop_widget import TaskCardWidget
    
    task_card = TaskCardWidget(test_task_data)
    layout.addWidget(task_card)
    
    # 添加说明
    from PyQt6.QtWidgets import QLabel
    info_label = QLabel("点击上面的任务卡片测试详情对话框功能")
    layout.addWidget(info_label)
    
    # 添加关闭按钮
    close_btn = QPushButton("关闭测试")
    close_btn.clicked.connect(app.quit)
    layout.addWidget(close_btn)
    
    test_window.show()
    
    print("测试窗口已显示，请点击任务卡片测试详情对话框功能")
    print("功能测试要点：")
    print("1. 点击任务卡片应该弹出详情对话框")
    print("2. 对话框应该显示任务信息")
    print("3. 可以切换完成/未完成状态")
    print("4. 可以删除任务")
    print("5. 保存后应该更新任务状态")
    
    # 运行应用
    app.exec()

if __name__ == "__main__":
    test_task_detail_dialog()
