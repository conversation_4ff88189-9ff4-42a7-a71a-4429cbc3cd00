#!/usr/bin/env python3
"""
桌面任务小部件启动脚本

独立启动桌面任务小部件，无需启动主应用
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

# 设置环境变量
os.environ.setdefault('QT_AUTO_SCREEN_SCALE_FACTOR', '1')
os.environ.setdefault('QT_ENABLE_HIGHDPI_SCALING', '1')

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

from src.modules.task_manager.desktop_widget import DesktopTaskWidget
from src.core.logger import setup_logging, get_logger
from src.core.config import ConfigManager


def setup_environment():
    """设置运行环境"""
    try:
        # 初始化配置
        config = ConfigManager()
        
        # 设置日志
        setup_logging(
            level=config.get('logging.level', 'INFO'),
            log_file=config.get('logging.file'),
            max_size=config.get('logging.max_size', 10485760),
            backup_count=config.get('logging.backup_count', 5)
        )
        
        logger = get_logger(__name__)
        logger.info("桌面小部件环境初始化完成")
        
        return config, logger
        
    except Exception as e:
        print(f"环境初始化失败: {e}")
        return None, None


def main():
    """主函数"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        app.setQuitOnLastWindowClosed(False)  # 不在最后窗口关闭时退出
        
        # 设置应用信息
        app.setApplicationName("桌面任务小部件")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("Personal Manager")
        
        # 设置应用图标
        icon_path = project_root / "resources" / "icons" / "app.ico"
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
        
        # 初始化环境
        config, logger = setup_environment()
        if not config or not logger:
            QMessageBox.critical(None, "错误", "环境初始化失败，无法启动桌面小部件")
            return 1
        
        # 检查是否已有实例在运行
        app.setApplicationDisplayName("桌面任务小部件")
        
        # 创建并显示桌面小部件
        try:
            print("创建桌面小部件...")
            widget = DesktopTaskWidget()
            print("桌面小部件创建完成")

            widget.show()
            print(f"桌面小部件显示命令已执行，位置: ({widget.x()}, {widget.y()})")
            print(f"窗口可见性: {widget.isVisible()}")

            logger.info("桌面任务小部件启动成功")

            # 启动事件循环
            exit_code = app.exec()
            logger.info(f"桌面小部件退出，退出码: {exit_code}")

            return exit_code
            
        except Exception as e:
            logger.error(f"创建桌面小部件失败: {e}")
            QMessageBox.critical(None, "错误", f"创建桌面小部件失败:\n{str(e)}")
            return 1
            
    except Exception as e:
        print(f"应用启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
