2025-07-07 12:24:30,750 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 12:24:30,751 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 12:24:30,751 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 12:24:30,760 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 12:25:00,761 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 12:25:02,766 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 542088
2025-07-07 12:26:39,914 - src.core.logger - [32m<PERSON><PERSON>O[0m - Logging initialized - Level: INFO
2025-07-07 12:26:39,914 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 12:26:39,928 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 12:26:39,929 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 12:27:09,929 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 12:27:11,935 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 461728
2025-07-07 12:29:10,232 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 12:29:10,232 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 12:29:10,246 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 12:29:10,246 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 12:29:40,247 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 12:29:42,252 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 165864
2025-07-07 12:31:46,336 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 12:31:46,336 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 12:31:46,349 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 12:31:46,349 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 12:32:16,350 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 12:32:18,356 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 404136
2025-07-07 12:44:37,864 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 12:44:37,864 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 12:44:37,879 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 12:44:37,880 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 12:45:07,880 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 12:45:09,886 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 85888
2025-07-07 13:20:02,730 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 13:20:02,730 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 13:20:02,743 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 13:20:02,744 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 13:20:32,744 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 13:20:34,750 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 469496
2025-07-07 13:31:05,266 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 13:31:05,267 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 13:31:05,280 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 13:31:05,281 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 13:31:35,282 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 13:31:37,289 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 208712
2025-07-07 13:41:03,915 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 13:41:03,915 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 13:41:03,929 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 13:41:03,930 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 13:41:33,931 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 13:41:35,937 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 159964
2025-07-07 13:44:41,604 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 13:44:41,604 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 13:44:41,617 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 13:44:41,617 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 13:45:11,618 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 13:45:13,624 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 429912
2025-07-07 13:46:46,260 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 13:46:46,260 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 13:46:46,274 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 13:46:46,274 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 13:47:16,275 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 13:47:18,281 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 213892
2025-07-07 13:48:18,284 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 13:48:18,284 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 13:48:49,659 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 13:48:49,659 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 13:48:49,673 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 13:48:49,674 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 13:55:03,197 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 13:55:03,197 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 13:55:03,210 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 13:55:03,211 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 13:55:33,211 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 13:55:35,217 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 133528
2025-07-07 13:56:47,033 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 13:56:47,033 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 13:56:47,048 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 13:56:47,048 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 13:57:17,049 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 13:57:19,054 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 31848
2025-07-07 13:57:59,056 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 13:57:59,056 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:18:06,448 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 14:18:06,448 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 14:18:06,473 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 14:18:06,473 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 14:18:36,474 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:18:38,480 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 543672
2025-07-07 14:19:18,481 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:19:18,481 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:19:23,482 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:19:25,487 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 574248
2025-07-07 14:19:25,487 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第1次）
2025-07-07 14:35:20,128 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 14:35:20,128 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 14:35:20,141 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 14:35:20,142 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 14:35:50,142 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:35:52,148 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 89788
2025-07-07 14:42:15,713 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 14:42:15,713 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 14:42:15,726 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 14:42:15,727 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 14:42:45,728 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:42:47,733 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 195816
2025-07-07 14:43:07,734 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:43:07,734 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:43:11,356 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 14:43:11,357 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 14:43:11,372 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 14:43:11,373 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 14:43:41,374 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:43:43,379 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 316492
2025-07-07 14:43:53,379 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:43:53,379 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:43:58,380 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:44:00,385 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 445772
2025-07-07 14:44:00,385 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第1次）
2025-07-07 14:44:10,386 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:44:10,386 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:44:15,386 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:44:17,392 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 471236
2025-07-07 14:44:17,392 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第2次）
2025-07-07 14:45:27,395 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:45:27,395 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:45:32,396 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:45:34,403 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 538244
2025-07-07 14:45:34,403 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第3次）
2025-07-07 14:47:34,406 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:47:34,406 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:47:39,407 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:47:41,412 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 363440
2025-07-07 14:47:41,412 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第4次）
2025-07-07 14:47:51,412 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:47:51,412 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:47:56,413 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:47:58,418 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 343584
2025-07-07 14:47:58,418 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第5次）
2025-07-07 14:48:08,419 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:48:08,419 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:48:13,419 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:48:15,425 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 106704
2025-07-07 14:48:15,425 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第6次）
2025-07-07 14:48:35,426 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:48:35,426 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:48:40,426 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:48:42,432 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 343928
2025-07-07 14:48:42,432 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第7次）
2025-07-07 14:48:52,432 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:48:52,432 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:48:57,433 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:48:59,439 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 134460
2025-07-07 14:48:59,439 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第8次）
2025-07-07 14:49:39,441 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:49:39,441 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:49:44,442 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:49:46,448 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 402368
2025-07-07 14:49:46,448 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第9次）
2025-07-07 14:50:16,449 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:50:16,449 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:50:21,450 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:50:23,454 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 480264
2025-07-07 14:50:23,454 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第10次）
2025-07-07 14:50:33,455 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:50:33,455 - __main__ - [33mWARNING[0m - 重启次数过多，暂停重启
2025-07-07 14:50:43,455 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:50:43,455 - __main__ - [33mWARNING[0m - 重启次数过多，暂停重启
2025-07-07 14:50:53,456 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:50:53,456 - __main__ - [33mWARNING[0m - 重启次数过多，暂停重启
2025-07-07 14:51:03,457 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:51:03,457 - __main__ - [33mWARNING[0m - 重启次数过多，暂停重启
2025-07-07 14:51:07,757 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 14:51:07,757 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 14:51:07,771 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 14:51:07,772 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 14:51:37,772 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:51:39,779 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 521016
2025-07-07 14:54:29,785 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:54:29,785 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:54:32,861 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 14:54:32,861 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 14:54:32,876 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 14:54:32,877 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 14:55:09,327 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 14:55:09,327 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 14:55:09,356 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 14:55:09,356 - __main__ - [32mINFO[0m - 等待30秒后启动桌面小部件...
2025-07-07 14:55:39,357 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:55:41,363 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 245300
2025-07-07 14:55:51,364 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:55:51,364 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:55:56,364 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:55:58,370 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 249456
2025-07-07 14:55:58,370 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第1次）
2025-07-07 14:56:08,370 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:56:08,370 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:56:13,371 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:56:15,377 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 514716
2025-07-07 14:56:15,377 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第2次）
2025-07-07 14:56:25,377 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:56:25,377 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:56:30,378 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:56:32,384 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 551664
2025-07-07 14:56:32,384 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第3次）
2025-07-07 14:56:42,385 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:56:42,385 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:56:47,386 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:56:49,390 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 53512
2025-07-07 14:56:49,390 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第4次）
2025-07-07 14:56:59,391 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:56:59,391 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:57:04,391 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:57:06,397 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 159584
2025-07-07 14:57:06,397 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第5次）
2025-07-07 14:57:16,398 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:57:16,398 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 14:57:21,398 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 14:57:23,404 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 508552
2025-07-07 14:57:23,404 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第6次）
2025-07-07 14:58:03,406 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 14:58:03,406 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 15:00:38,228 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:00:38,228 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:00:38,242 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:00:38,243 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:00:38,243 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:00:40,248 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 334960
2025-07-07 15:01:07,468 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:01:07,469 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:01:07,483 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:01:07,484 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:01:07,484 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:01:09,488 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 437240
2025-07-07 15:01:39,490 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 15:01:39,490 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 15:01:44,490 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:01:46,496 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 131900
2025-07-07 15:01:46,496 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第1次）
2025-07-07 15:06:06,505 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 15:06:06,505 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 15:06:11,506 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:06:13,512 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 574276
2025-07-07 15:06:13,512 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第2次）
2025-07-07 15:18:03,539 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 15:18:03,539 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 15:18:08,539 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:18:10,545 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 124204
2025-07-07 15:18:10,545 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第3次）
2025-07-07 15:22:00,564 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:22:00,565 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:22:00,579 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:22:00,580 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:22:00,580 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:22:02,584 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 337232
2025-07-07 15:25:09,427 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:25:09,427 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:25:09,441 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:25:09,442 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:25:09,442 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:25:11,447 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 15:25:11,447 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 15:25:23,454 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:25:23,454 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:25:23,468 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:25:23,468 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:25:23,468 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:25:25,474 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 15:25:25,474 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 15:26:30,823 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:26:30,823 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:26:30,837 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:26:30,837 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:26:30,837 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:26:32,842 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 15:26:32,842 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 15:27:23,263 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:27:23,263 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:27:23,278 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:27:23,278 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:27:23,278 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:27:25,283 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 15:27:25,283 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 15:31:32,857 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:31:32,857 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:31:32,872 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:31:32,872 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:31:32,872 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:31:34,878 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 15:31:34,878 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 15:38:20,947 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:38:20,947 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:38:20,962 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:38:20,962 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:38:20,963 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:38:22,968 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 15:38:22,968 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 15:38:39,647 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:38:39,647 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:38:39,662 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:38:39,663 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:38:39,663 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:38:41,669 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 15:38:41,669 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 15:41:59,282 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:41:59,282 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:41:59,295 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:41:59,295 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:41:59,296 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:42:01,300 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 15:42:01,300 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 15:42:28,849 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:42:28,849 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:42:28,863 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:42:28,864 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:42:28,864 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:42:30,869 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 15:42:30,869 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 15:43:51,981 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:43:51,981 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:43:51,994 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:43:51,995 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:43:51,995 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:43:54,000 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 15:43:54,000 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 15:55:33,496 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 15:55:33,497 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 15:55:33,512 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 15:55:33,512 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 15:55:33,512 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 15:55:35,517 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 15:55:35,517 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 16:00:53,428 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:00:53,428 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 16:00:53,466 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 16:00:53,467 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 16:00:53,467 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:00:55,472 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:00:55,472 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 16:01:15,727 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:01:15,727 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 16:01:15,740 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 16:01:15,741 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 16:01:15,741 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:01:17,745 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:01:17,745 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 16:14:49,638 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:14:49,639 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 16:14:49,651 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 16:14:49,651 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 16:14:49,651 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:14:51,656 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:14:51,656 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 16:34:10,470 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:34:10,470 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 16:34:10,483 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 16:34:10,483 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 16:34:10,483 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:34:12,488 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:34:12,488 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 16:44:47,471 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:44:47,471 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 16:44:47,485 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 16:44:47,486 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 16:44:47,486 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:44:49,491 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:44:49,491 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 16:44:56,283 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:44:56,284 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 16:44:56,298 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 16:44:56,298 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 16:44:56,299 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:44:58,304 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:44:58,304 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 16:45:14,207 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:45:14,208 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 16:45:14,233 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 16:45:14,234 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 16:45:14,234 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:45:16,239 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:45:16,239 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 16:52:58,025 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:52:58,025 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 16:52:58,036 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 16:52:58,037 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 16:52:58,037 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:53:00,042 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:53:00,042 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 16:56:52,916 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:56:52,916 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 16:56:52,941 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 16:56:52,942 - __main__ - [32mINFO[0m - 自动启动，但系统已运行较长时间，立即启动桌面小部件...
2025-07-07 16:56:52,943 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:56:54,948 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:56:54,948 - __main__ - [31mERROR[0m - 初始启动失败
2025-07-07 16:57:48,771 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:57:48,772 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 16:57:48,786 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 16:57:48,787 - __main__ - [32mINFO[0m - 自动启动，但系统已运行较长时间，立即启动桌面小部件...
2025-07-07 16:57:48,787 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:57:50,792 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:57:50,792 - __main__ - [31mERROR[0m - 初始启动失败，但继续运行以监听命令
2025-07-07 16:57:50,793 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-07 16:57:50,794 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-07 16:57:50,794 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-07 16:57:50,794 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-07 16:57:50,794 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:57:52,798 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:57:52,798 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 16:57:52,798 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 16:57:57,799 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:57:59,804 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:57:59,804 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 16:58:09,805 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-07 16:58:09,805 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-07 16:58:09,805 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-07 16:58:09,805 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-07 16:58:09,805 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:58:11,810 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:58:11,810 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 16:58:11,810 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 16:58:16,810 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:58:18,815 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:58:18,815 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 16:58:28,816 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 16:58:28,816 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 16:58:33,816 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:58:35,821 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:58:35,821 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 16:58:45,822 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 16:58:45,822 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 16:58:50,823 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:58:52,828 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:58:52,828 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 16:59:02,829 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 16:59:02,829 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 16:59:07,829 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:59:09,836 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:59:09,836 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 16:59:19,836 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 16:59:19,836 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 16:59:24,837 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:59:26,843 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:59:26,843 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 16:59:36,843 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 16:59:36,843 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 16:59:41,844 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:59:43,431 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:59:43,431 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 16:59:43,445 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 16:59:43,445 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 16:59:43,446 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:59:45,451 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:59:45,451 - __main__ - [31mERROR[0m - 初始启动失败，但继续运行以监听命令
2025-07-07 16:59:45,451 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 16:59:45,451 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 16:59:50,451 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 16:59:52,456 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 16:59:52,456 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:00:02,457 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:00:02,457 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:00:07,457 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:00:09,462 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:00:09,462 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:00:19,463 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:00:19,463 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:00:24,463 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:00:26,469 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:00:26,469 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:00:36,469 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:00:36,469 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:00:41,470 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:00:43,474 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:00:43,474 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:00:53,475 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:00:53,475 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:00:58,475 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:01:00,480 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:01:00,480 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:01:10,481 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:01:10,481 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:01:15,481 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:01:17,487 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:01:17,487 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:01:27,487 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:01:27,487 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:01:32,488 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:01:34,492 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:01:34,492 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:01:44,493 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:01:44,493 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:01:49,494 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:01:51,501 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:01:51,501 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:02:01,501 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-07 17:02:01,502 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-07 17:02:01,502 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-07 17:02:01,502 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-07 17:02:01,502 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:02:03,506 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:02:03,506 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:02:03,506 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:02:08,507 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:02:10,510 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:02:10,510 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:02:20,511 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:02:20,511 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:02:25,512 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:02:27,516 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:02:27,516 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:02:37,516 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:02:37,516 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:02:42,517 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:02:44,521 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:02:44,521 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:02:54,522 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:02:54,522 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:02:59,523 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:03:01,527 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:03:01,527 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:03:11,527 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:03:11,527 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:03:16,528 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:03:18,533 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:03:18,533 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:03:28,533 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:03:28,533 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:03:33,534 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:03:35,538 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:03:35,538 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:03:45,539 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:03:45,539 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:03:50,539 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:03:52,543 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:03:52,543 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:04:02,544 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:04:02,544 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:04:07,545 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:04:09,550 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:04:09,550 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:04:19,551 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:04:19,551 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:04:24,551 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:04:26,555 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 235960
2025-07-07 17:04:26,555 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第1次）
2025-07-07 17:06:36,561 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:06:36,561 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:06:38,173 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 17:06:38,173 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 17:06:38,187 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 17:06:38,187 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 17:06:38,187 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:06:40,192 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 317700
2025-07-07 17:14:30,212 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:14:30,212 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:14:35,213 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:14:37,219 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 68948
2025-07-07 17:14:37,219 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第1次）
2025-07-07 17:18:37,231 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:18:37,231 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:18:42,231 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:18:44,237 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 270532
2025-07-07 17:18:44,237 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第2次）
2025-07-07 17:19:44,239 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:19:44,239 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:19:49,240 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:19:51,245 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 468356
2025-07-07 17:19:51,245 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第3次）
2025-07-07 17:20:01,246 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-07 17:20:01,246 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-07 17:20:01,246 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-07 17:20:01,267 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-07 17:20:01,267 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-07 17:20:01,267 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:20:03,272 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 192136
2025-07-07 17:22:30,842 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 17:22:30,842 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 17:22:30,857 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 17:22:30,858 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 17:22:30,858 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:22:32,864 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 37508
2025-07-07 17:22:54,006 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 17:22:54,006 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 17:24:32,738 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 17:24:32,738 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 17:24:32,752 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 17:24:32,753 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 17:24:32,753 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:24:34,758 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 549256
2025-07-07 17:24:54,759 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:24:54,759 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:24:59,759 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:25:01,763 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 86660
2025-07-07 17:25:01,763 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第1次）
2025-07-07 17:25:21,764 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:25:21,764 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:25:26,765 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:25:28,769 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 9712
2025-07-07 17:25:28,769 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第2次）
2025-07-07 17:30:17,295 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 17:30:17,295 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 17:30:35,574 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 17:30:35,574 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 17:30:35,588 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 17:30:35,589 - __main__ - [32mINFO[0m - 自动启动，但系统已运行较长时间，立即启动桌面小部件...
2025-07-07 17:30:35,589 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:30:37,594 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:30:37,594 - __main__ - [31mERROR[0m - 初始启动失败，但继续运行以监听命令
2025-07-07 17:30:37,594 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:30:37,594 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:30:42,595 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:30:44,600 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:30:44,600 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:30:54,600 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:30:54,600 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:30:59,601 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:31:01,606 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:31:01,606 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:31:11,606 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:31:11,606 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:31:16,607 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:31:18,612 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 17:31:18,612 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 17:31:28,613 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 17:31:28,613 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 17:31:33,614 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 17:31:35,619 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 506500
2025-07-07 17:31:35,619 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第1次）
2025-07-07 19:33:05,948 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 19:33:05,949 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 19:33:10,950 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 19:33:12,955 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 444024
2025-07-07 19:33:12,955 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第1次）
2025-07-07 20:00:53,032 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-07 20:00:53,033 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-07 20:00:53,033 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-07 20:00:53,033 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-07 20:00:53,033 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:00:55,038 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 20:00:55,038 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:00:55,039 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:01:00,039 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:01:02,045 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 20:01:02,045 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:01:04,781 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:01:04,781 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:01:04,793 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:01:04,794 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:01:04,794 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:01:06,799 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 20:01:06,799 - __main__ - [31mERROR[0m - 初始启动失败，但继续运行以监听命令
2025-07-07 20:01:06,799 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:01:06,799 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:01:20,331 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:01:20,331 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:01:20,346 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:01:20,347 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:01:20,347 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:01:22,352 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 20:01:22,352 - __main__ - [31mERROR[0m - 初始启动失败，但继续运行以监听命令
2025-07-07 20:01:22,352 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:01:22,352 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:01:27,353 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:01:29,361 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 20:01:29,361 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:01:39,362 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:01:39,362 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:01:44,362 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:01:46,367 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 20:01:46,367 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:01:56,368 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:01:56,368 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:02:01,826 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:02:01,826 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:02:01,840 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:02:01,841 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:02:01,841 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:02:03,846 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 20:02:03,846 - __main__ - [31mERROR[0m - 初始启动失败，但继续运行以监听命令
2025-07-07 20:02:03,846 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:02:03,846 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:02:08,847 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:02:10,852 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 20:02:10,852 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:02:20,852 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:02:20,852 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:02:25,853 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:02:27,858 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 20:02:27,858 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:02:37,859 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:02:37,859 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:02:42,859 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:02:44,864 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 20:02:44,864 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:02:54,865 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:02:54,865 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:02:59,866 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:03:01,873 - __main__ - [31mERROR[0m - 桌面小部件启动失败
2025-07-07 20:03:01,873 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:03:11,873 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:03:11,873 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:14:00,725 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:14:00,725 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:14:00,740 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:14:00,741 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:14:00,741 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:14:02,746 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:14:02,746 - __main__ - [31mERROR[0m - 初始启动失败，但继续运行以监听命令
2025-07-07 20:14:02,747 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:14:02,747 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:14:07,748 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:14:09,752 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:14:09,752 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:14:19,753 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:14:19,753 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:14:24,753 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:14:26,759 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:14:26,759 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:14:36,760 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:14:36,760 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:14:41,760 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:14:43,765 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:14:43,765 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:14:53,766 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:14:53,766 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:14:58,766 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:15:00,771 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:15:00,771 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:15:10,772 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:15:10,772 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:15:15,772 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:15:17,777 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:15:17,778 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:15:27,778 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:15:27,778 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:15:32,779 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:15:34,783 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:15:34,784 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:15:44,784 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:15:44,784 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:15:49,785 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:15:51,789 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:15:51,790 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:16:01,790 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:16:01,790 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:16:06,791 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:16:08,796 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:16:08,797 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:16:18,797 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:16:18,797 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:16:23,797 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:16:25,801 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:16:25,802 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:16:35,802 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:16:35,802 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:16:40,803 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:16:42,807 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:16:42,808 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:16:52,808 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:16:52,808 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:16:55,178 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:16:55,178 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:16:57,809 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:16:59,813 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:16:59,813 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:17:09,814 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:17:09,814 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:17:19,636 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:17:19,637 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:17:19,651 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:17:19,652 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:17:19,652 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:17:21,657 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:17:21,658 - __main__ - [31mERROR[0m - 初始启动失败，但继续运行以监听命令
2025-07-07 20:17:21,658 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:17:21,658 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:17:26,659 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:17:28,664 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:17:28,666 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:17:38,666 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:17:38,666 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:17:43,667 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:17:45,673 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:17:45,674 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:17:55,675 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:17:55,675 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:18:00,676 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:18:02,682 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:18:02,683 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:18:12,684 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:18:12,684 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:18:17,685 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:18:19,690 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:18:19,691 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:18:29,692 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:18:29,692 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:18:34,694 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:18:36,699 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:18:36,700 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:18:46,701 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:18:46,701 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:18:51,702 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:18:53,708 - __main__ - [31mERROR[0m - 桌面小部件启动失败，退出码: 1
2025-07-07 20:18:53,709 - __main__ - [31mERROR[0m - 桌面小部件重启失败
2025-07-07 20:19:03,710 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-07 20:19:03,710 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-07 20:19:08,711 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:19:10,716 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 178564
2025-07-07 20:19:10,717 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第1次）
2025-07-07 20:19:21,218 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:19:21,218 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:19:21,248 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:19:21,248 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:19:21,249 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:19:23,254 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 472168
2025-07-07 20:27:07,626 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:27:07,626 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:27:07,640 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:27:07,641 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:27:07,641 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:27:09,646 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 418896
2025-07-07 20:32:32,297 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:32:32,297 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:32:32,311 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:32:32,311 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:32:32,311 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:32:34,317 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 124816
2025-07-07 20:33:28,622 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:33:28,622 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:33:28,636 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:33:28,637 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:33:28,637 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:33:30,642 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 370152
2025-07-07 20:39:04,358 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:39:04,359 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:39:04,373 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:39:04,373 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:39:04,373 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:39:06,379 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 40144
2025-07-07 20:39:47,200 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:39:47,200 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:39:47,214 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:39:47,214 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:39:47,214 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:39:49,219 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 104572
2025-07-07 20:44:29,336 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:44:29,336 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:44:29,350 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:44:29,350 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:44:29,351 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:44:31,355 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 108288
2025-07-07 20:46:24,733 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:46:24,733 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:46:24,747 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:46:24,747 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:46:24,748 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:46:26,752 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 269204
2025-07-07 20:46:57,393 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:46:57,393 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:46:57,408 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:46:57,408 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:46:57,409 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:46:59,414 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 558976
2025-07-07 20:49:04,373 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:49:04,374 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:49:04,389 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:49:04,389 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:49:04,389 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:49:06,395 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 194472
2025-07-07 20:51:15,127 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:51:15,127 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:51:15,141 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:51:15,142 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:51:15,142 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:51:17,146 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 432528
2025-07-07 20:52:36,359 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:52:36,359 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:52:36,374 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:52:36,375 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:52:36,375 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:52:38,380 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 432080
2025-07-07 20:54:19,888 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:54:19,889 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:54:19,902 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:54:19,903 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:54:19,903 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:54:21,909 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 559944
2025-07-07 20:59:53,742 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 20:59:53,742 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 20:59:53,756 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 20:59:53,757 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 20:59:53,757 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 20:59:55,762 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 392764
2025-07-07 21:03:45,417 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:03:45,417 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:03:45,433 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:03:45,434 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:03:45,434 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:03:47,439 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 531452
2025-07-07 21:03:47,440 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-07 21:03:47,440 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-07 21:03:47,440 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-07 21:03:47,456 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-07 21:03:47,457 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-07 21:03:47,457 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:03:49,462 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 207940
2025-07-07 21:04:23,841 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:04:23,841 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:04:23,857 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:04:23,857 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:04:23,857 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:04:25,862 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 471620
2025-07-07 21:04:25,863 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-07 21:04:25,863 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-07 21:04:25,863 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-07 21:04:25,879 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-07 21:04:25,879 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-07 21:04:25,880 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:04:27,884 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 461356
2025-07-07 21:09:52,260 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:09:52,260 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:09:52,274 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:09:52,275 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:09:52,275 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:09:54,280 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 210024
2025-07-07 21:09:54,281 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-07 21:09:54,281 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-07 21:09:54,281 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-07 21:09:54,298 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-07 21:09:54,298 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-07 21:09:54,298 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:09:56,303 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 280184
2025-07-07 21:18:49,664 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:18:49,664 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:18:49,678 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:18:49,678 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:18:49,678 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:18:51,683 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 188144
2025-07-07 21:19:24,509 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:19:24,509 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:19:24,524 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:19:24,524 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:19:24,524 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:19:26,529 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 61760
2025-07-07 21:21:38,473 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:21:38,473 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:21:38,487 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:21:38,488 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:21:38,488 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:21:40,493 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 435120
2025-07-07 21:22:21,275 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:22:21,275 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:22:21,289 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:22:21,290 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:22:21,290 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:22:23,295 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 215528
2025-07-07 21:26:27,146 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:26:27,146 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:26:27,159 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:26:27,160 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:26:27,160 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:26:29,165 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 565728
2025-07-07 21:28:59,936 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:28:59,936 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:28:59,950 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:28:59,950 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:28:59,950 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:29:01,955 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 46264
2025-07-07 21:29:43,623 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:29:43,623 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:29:43,623 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:29:43,623 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:29:43,623 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:29:45,628 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 375152
2025-07-07 21:38:31,817 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:38:31,817 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:38:31,818 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:38:31,818 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:38:31,818 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:38:33,823 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 342808
2025-07-07 21:43:12,189 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:43:12,189 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:43:12,189 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:43:12,189 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:43:12,189 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:43:14,194 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 282816
2025-07-07 21:53:33,574 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:53:33,574 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:53:33,574 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:53:33,575 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:53:33,575 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:53:35,579 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 420696
2025-07-07 21:54:44,037 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:54:44,037 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:54:44,037 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:54:44,038 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:54:44,038 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:54:46,043 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 401016
2025-07-07 21:58:04,572 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 21:58:04,572 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 21:58:04,572 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 21:58:04,572 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 21:58:04,572 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 21:58:06,577 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 502336
2025-07-07 22:03:32,176 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:03:32,176 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:03:32,177 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:03:32,177 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:03:32,177 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:03:34,181 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 497788
2025-07-07 22:06:20,269 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:06:20,269 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:06:20,269 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:06:20,270 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:06:20,270 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:06:22,274 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 427572
2025-07-07 22:10:27,272 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:10:27,272 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:10:27,272 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:10:27,273 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:10:27,273 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:10:29,277 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 541168
2025-07-07 22:18:21,703 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:18:21,704 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:18:21,704 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:18:21,705 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:18:21,705 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:18:23,709 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 568432
2025-07-07 22:21:00,503 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:21:00,503 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:21:00,503 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:21:00,504 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:21:00,504 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:21:02,509 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 319000
2025-07-07 22:22:58,453 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:22:58,453 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:22:58,453 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:22:58,454 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:22:58,454 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:23:00,458 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 355340
2025-07-07 22:33:47,600 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:33:47,600 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:33:47,600 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:33:47,601 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:33:47,601 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:33:49,605 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 185260
2025-07-07 22:36:25,190 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:36:25,190 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:36:25,191 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:36:25,191 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:36:25,191 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:36:27,196 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 241072
2025-07-07 22:41:17,513 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:41:17,513 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:41:17,513 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:41:17,514 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:41:17,514 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:41:19,519 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 132612
2025-07-07 22:43:36,729 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:43:36,729 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:43:36,729 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:43:36,730 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:43:36,730 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:43:38,735 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 124456
2025-07-07 22:46:02,348 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:46:02,348 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:46:02,348 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:46:02,349 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:46:02,349 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:46:04,354 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 491720
2025-07-07 22:52:42,877 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:52:42,878 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:52:42,878 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:52:42,878 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:52:42,878 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:52:44,883 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 245960
2025-07-07 22:56:06,339 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:56:06,339 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:56:06,339 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:56:06,340 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:56:06,340 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:56:08,344 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 311184
2025-07-07 22:58:50,404 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 22:58:50,404 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 22:58:50,404 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 22:58:50,404 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 22:58:50,404 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 22:58:52,409 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 531756
2025-07-07 23:08:21,063 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 23:08:21,064 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 23:08:21,064 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 23:08:21,064 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-07 23:08:21,064 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 23:08:23,069 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 536248
2025-07-07 23:16:04,571 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 23:16:04,571 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-07 23:16:04,576 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-07 23:16:04,576 - __main__ - [32mINFO[0m - 检测到开机启动，等待30秒后启动桌面小部件...
2025-07-07 23:16:34,577 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-07 23:16:36,581 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 22912
2025-07-08 09:11:45,498 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-08 09:11:45,499 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-08 09:11:45,500 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-08 09:11:45,501 - __main__ - [32mINFO[0m - 检测到开机启动，等待30秒后启动桌面小部件...
2025-07-08 09:12:15,501 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:12:17,505 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 18560
2025-07-08 09:22:13,167 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-08 09:22:13,167 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-08 09:22:13,172 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-08 09:22:13,172 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-08 09:22:13,172 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:22:15,176 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 4368
2025-07-08 09:29:17,994 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-08 09:29:17,994 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-08 09:29:17,998 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-08 09:29:17,998 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-08 09:29:17,998 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:29:20,003 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 7796
2025-07-08 09:30:58,467 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-08 09:30:58,467 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-08 09:30:58,471 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-08 09:30:58,471 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-08 09:30:58,471 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:31:00,475 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 9528
2025-07-08 09:31:46,145 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-08 09:31:46,145 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-08 09:31:46,149 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-08 09:31:46,150 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-08 09:31:46,150 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:31:48,154 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 12648
2025-07-08 09:32:37,413 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-08 09:32:37,413 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-08 09:32:37,417 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-08 09:32:37,417 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-08 09:32:37,417 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:32:39,422 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 18120
2025-07-08 09:34:19,427 - __main__ - [33mWARNING[0m - 检测到桌面小部件异常
2025-07-08 09:34:19,427 - __main__ - [32mINFO[0m - 重启桌面小部件...
2025-07-08 09:34:24,428 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:34:26,431 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 19012
2025-07-08 09:34:26,431 - __main__ - [32mINFO[0m - 桌面小部件重启成功（第1次）
2025-07-08 09:42:46,451 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 09:42:46,451 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 09:42:46,451 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 09:42:46,461 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 09:42:46,462 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 09:42:46,462 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:42:48,465 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 21248
2025-07-08 09:44:31,920 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-08 09:44:31,920 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-08 09:44:31,924 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-08 09:44:31,925 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-08 09:44:31,925 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:44:33,929 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 28660
2025-07-08 09:47:44,442 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-08 09:47:44,442 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-08 09:47:44,446 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-08 09:47:44,447 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-08 09:47:44,447 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:47:46,451 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 14048
2025-07-08 09:49:38,108 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-08 09:49:38,108 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-08 09:49:38,114 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-08 09:49:38,114 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-08 09:49:38,114 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:49:40,118 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 17264
2025-07-08 09:52:06,068 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-08 09:52:06,068 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-08 09:52:06,068 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-08 09:52:06,069 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-08 09:52:06,069 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:52:08,073 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 27936
2025-07-08 09:54:48,082 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 09:54:48,082 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 09:54:48,082 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 09:54:48,096 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 09:54:48,096 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 09:54:48,096 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:54:50,099 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 23632
2025-07-08 09:55:30,102 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 09:55:30,102 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 09:55:30,102 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 09:55:30,113 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 09:55:30,113 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 09:55:30,113 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 09:55:32,117 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 25628
2025-07-08 20:50:23,787 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 20:50:23,787 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 20:50:23,787 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 20:50:23,801 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 20:50:23,801 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 20:50:23,801 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 20:50:25,806 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 33560
2025-07-08 20:50:35,807 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 20:50:35,807 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 20:50:35,807 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 20:50:35,817 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 20:50:35,817 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 20:50:35,817 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 20:50:37,821 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 2060
2025-07-08 20:57:57,843 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 20:57:57,843 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 20:57:57,843 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 20:57:57,857 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 20:57:57,857 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 20:57:57,857 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 20:57:59,861 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 28756
2025-07-08 20:58:09,863 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 20:58:09,863 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 20:58:09,863 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 20:58:09,872 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 20:58:09,872 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 20:58:09,872 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 20:58:11,876 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 37300
2025-07-08 20:58:41,878 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 20:58:41,878 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 20:58:41,878 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 20:58:41,890 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 20:58:41,890 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 20:58:41,890 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 20:58:43,894 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 33528
2025-07-08 20:59:43,898 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 20:59:43,898 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 20:59:43,898 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 20:59:43,907 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 20:59:43,907 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 20:59:43,907 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 20:59:45,912 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 37412
2025-07-08 21:06:05,929 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 21:06:05,929 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 21:06:05,929 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 21:06:05,939 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 21:06:05,939 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 21:06:05,939 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 21:06:07,944 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 25120
2025-07-08 21:06:17,945 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 21:06:17,945 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 21:06:17,945 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 21:06:17,957 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 21:06:17,957 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 21:06:17,957 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 21:06:19,961 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 33360
2025-07-08 21:06:39,963 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 21:06:39,963 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 21:06:39,963 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 21:06:39,972 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 21:06:39,973 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 21:06:39,973 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 21:06:41,977 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 25900
2025-07-08 21:09:41,984 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 21:09:41,985 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 21:09:41,985 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 21:09:41,992 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 21:09:41,993 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 21:09:41,993 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 21:10:24,513 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-08 21:10:24,513 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-08 21:10:24,513 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-08 21:10:24,513 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-08 21:10:24,513 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 21:10:26,518 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 36164
2025-07-08 21:10:36,521 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 21:10:36,521 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 21:10:36,521 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 21:10:36,532 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 21:10:36,532 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 21:10:36,532 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 21:10:38,537 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 35164
2025-07-08 21:18:08,555 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 21:18:08,556 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 21:18:08,556 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 21:18:08,566 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 21:18:08,566 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 21:18:08,566 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 21:18:10,570 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 35980
2025-07-08 21:24:10,586 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 21:24:10,586 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 21:24:10,586 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 21:24:10,596 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 21:24:10,596 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 21:24:10,596 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 21:24:12,600 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 39144
2025-07-08 21:41:32,645 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 21:41:32,645 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 21:41:32,645 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 21:41:32,655 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 21:41:32,655 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 21:41:32,655 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 21:41:34,659 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 32380
2025-07-08 23:23:44,927 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 23:23:44,928 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 23:23:44,928 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 23:23:44,937 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 23:23:44,937 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 23:23:44,937 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 23:23:46,942 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 37304
2025-07-08 23:28:16,955 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-08 23:28:16,955 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-08 23:28:16,955 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-08 23:28:16,965 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-08 23:28:16,966 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-08 23:28:16,966 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-08 23:28:18,970 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 34660
2025-07-09 11:27:40,641 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 11:27:40,641 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 11:27:40,641 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 11:27:40,651 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 11:27:40,652 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 11:27:40,652 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 11:27:42,656 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 20908
2025-07-09 13:14:32,922 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 13:14:32,923 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 13:14:32,923 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 13:14:32,931 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 13:14:32,931 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 13:14:32,931 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 13:14:34,935 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 39972
2025-07-09 13:17:14,942 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 13:17:14,942 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 13:17:14,942 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 13:17:14,953 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 13:17:14,953 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 13:17:14,953 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 13:17:16,956 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 18528
2025-07-09 13:42:57,022 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 13:42:57,022 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 13:42:57,022 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 13:42:57,036 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 13:42:57,036 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 13:42:57,037 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 13:42:59,040 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 42152
2025-07-09 14:42:08,958 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-09 14:42:08,958 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-09 14:42:08,959 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-09 14:42:08,959 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-09 14:42:08,959 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 14:42:10,963 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 39152
2025-07-09 14:49:23,953 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-09 14:49:23,954 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-09 14:49:23,954 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-09 14:49:23,954 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-09 14:49:23,954 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 14:49:25,958 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 17700
2025-07-09 14:49:25,959 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 14:49:25,959 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 14:49:25,959 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 14:49:25,974 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 14:49:25,974 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 14:49:25,974 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 14:49:27,978 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 42408
2025-07-09 15:15:28,048 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 15:15:28,048 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 15:15:28,048 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 15:15:28,061 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 15:15:28,061 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 15:15:28,061 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 15:15:30,065 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 5880
2025-07-09 15:23:06,771 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-09 15:23:06,771 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-09 15:23:06,778 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-09 15:23:06,778 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-09 15:23:06,778 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 15:23:08,782 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 38556
2025-07-09 15:23:10,861 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-09 15:23:10,861 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-09 15:23:10,861 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-09 15:23:10,861 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-09 15:23:10,861 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 15:23:12,865 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 3132
2025-07-09 15:24:24,210 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-09 15:24:24,211 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-09 15:24:24,211 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-09 15:24:24,211 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-09 15:24:24,211 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 15:24:26,216 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 33068
2025-07-09 15:31:56,238 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 15:31:56,238 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 15:31:56,238 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 15:31:56,253 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 15:31:56,253 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 15:31:56,253 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 15:31:58,256 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 38040
2025-07-09 15:33:18,261 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 15:33:18,261 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 15:33:18,261 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 15:33:18,271 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 15:33:18,271 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 15:33:18,271 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 15:33:20,276 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 36468
2025-07-09 15:35:40,283 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 15:35:40,283 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 15:35:40,283 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 15:35:40,293 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 15:35:40,293 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 15:35:40,293 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 15:35:42,298 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 42252
2025-07-09 15:46:02,327 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 15:46:02,327 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 15:46:02,327 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 15:46:02,338 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 15:46:02,338 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 15:46:02,338 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 15:46:04,342 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 41412
2025-07-09 16:05:24,424 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 16:05:24,424 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 16:05:24,424 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 16:05:24,434 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 16:05:24,434 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 16:05:24,434 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 16:05:26,438 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 40948
2025-07-09 16:09:46,450 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 16:09:46,450 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 16:09:46,450 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 16:09:46,461 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 16:09:46,461 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 16:09:46,461 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 16:09:48,466 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 37352
2025-07-09 16:15:32,099 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-09 16:15:32,099 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-09 16:15:32,099 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-09 16:15:32,100 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-09 16:15:32,100 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 16:15:34,104 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 43844
2025-07-09 16:20:03,524 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-09 16:20:03,524 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-09 16:20:03,524 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-09 16:20:03,524 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-09 16:20:03,524 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 16:20:05,529 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 21768
2025-07-09 16:24:35,540 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 16:24:35,540 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 16:24:35,540 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 16:24:35,553 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 16:24:35,554 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 16:24:35,554 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 16:24:37,558 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 6444
2025-07-09 16:47:47,833 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-09 16:47:47,834 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-09 16:47:47,842 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-09 16:47:47,842 - __main__ - [32mINFO[0m - 检测到开机启动，等待30秒后启动桌面小部件...
2025-07-09 16:48:17,842 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 16:48:19,847 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 25092
2025-07-09 16:55:19,867 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 16:55:19,867 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 16:55:19,867 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 16:55:19,877 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 16:55:19,877 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 16:55:19,877 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 16:58:04,903 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-09 16:58:04,903 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-09 16:58:04,903 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-09 16:58:04,904 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-09 16:58:04,904 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 16:58:06,908 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 18756
2025-07-09 17:01:26,917 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:01:26,917 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:01:26,917 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:01:26,929 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:01:26,929 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:01:26,929 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:01:28,933 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 16872
2025-07-09 17:03:58,939 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:03:58,939 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:03:58,939 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:03:58,948 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:03:58,948 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:03:58,948 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:04:00,952 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 21724
2025-07-09 17:11:51,016 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:11:51,016 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:11:51,016 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:11:51,024 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:11:51,024 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:11:51,024 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:11:53,028 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 31176
2025-07-09 17:14:03,033 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:14:03,033 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:14:03,033 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:14:03,042 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:14:03,042 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:14:03,042 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:14:05,046 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 29192
2025-07-09 17:16:45,052 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:16:45,052 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:16:45,052 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:16:45,062 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:16:45,062 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:16:45,062 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:16:47,066 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 10992
2025-07-09 17:20:47,076 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:20:47,076 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:20:47,076 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:20:47,086 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:20:47,086 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:20:47,086 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:20:49,089 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 31716
2025-07-09 17:23:19,096 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:23:19,096 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:23:19,096 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:23:19,104 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:23:19,104 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:23:19,104 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:23:21,108 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 18740
2025-07-09 17:27:01,152 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:27:01,152 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:27:01,152 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:27:01,161 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:27:01,161 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:27:01,161 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:27:03,165 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 28316
2025-07-09 17:28:43,169 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:28:43,169 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:28:43,169 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:28:43,178 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:28:43,178 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:28:43,178 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:28:45,181 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 19720
2025-07-09 17:30:25,187 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:30:25,187 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:30:25,187 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:30:25,196 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:30:25,196 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:30:25,196 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:30:27,200 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 30344
2025-07-09 17:33:17,208 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:33:17,208 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:33:17,208 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:33:17,218 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:33:17,218 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:33:17,218 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:33:19,221 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 22364
2025-07-09 17:34:09,224 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:34:09,224 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:34:09,224 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:34:09,233 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:34:09,233 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:34:09,233 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:34:11,236 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 4512
2025-07-09 17:38:01,246 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:38:01,247 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:38:01,247 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:38:01,254 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:38:01,255 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:38:01,255 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:38:03,258 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 26840
2025-07-09 17:41:33,268 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:41:33,268 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:41:33,268 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:41:33,276 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:41:33,276 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:41:33,276 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:41:35,280 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 31372
2025-07-09 17:42:15,282 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:42:15,282 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:42:15,282 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:42:15,292 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:42:15,292 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:42:15,292 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:42:17,295 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 28844
2025-07-09 17:44:37,301 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:44:37,301 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:44:37,301 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:44:37,310 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:44:37,310 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:44:37,310 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:44:39,314 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 9220
2025-07-09 17:47:39,322 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:47:39,322 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:47:39,322 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:47:39,330 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:47:39,330 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:47:39,330 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:47:41,334 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 6940
2025-07-09 17:49:31,339 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:49:31,339 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:49:31,339 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:49:31,349 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:49:31,349 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:49:31,349 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:49:33,352 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 30296
2025-07-09 17:51:43,359 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:51:43,359 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:51:43,359 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:51:43,368 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:51:43,368 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:51:43,368 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:51:45,371 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 29500
2025-07-09 17:52:25,373 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:52:25,373 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:52:25,373 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:52:25,384 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:52:25,384 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:52:25,384 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:52:27,388 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 29812
2025-07-09 17:54:07,392 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:54:07,392 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:54:07,392 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:54:07,402 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:54:07,402 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:54:07,402 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:54:09,406 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 16664
2025-07-09 17:59:39,421 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 17:59:39,421 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 17:59:39,421 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 17:59:39,431 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 17:59:39,431 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 17:59:39,431 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 17:59:41,435 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 13812
2025-07-09 18:02:51,443 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:02:51,443 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:02:51,443 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:02:51,453 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:02:51,453 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:02:51,453 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:02:53,456 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 13216
2025-07-09 18:05:53,465 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:05:53,465 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:05:53,465 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:05:53,474 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:05:53,474 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:05:53,474 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:05:55,478 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 21600
2025-07-09 18:07:55,484 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:07:55,484 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:07:55,484 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:07:55,492 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:07:55,492 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:07:55,492 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:07:57,496 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 6864
2025-07-09 18:11:47,506 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:11:47,506 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:11:47,506 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:11:47,515 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:11:47,516 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:11:47,516 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:11:49,519 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 1928
2025-07-09 18:13:59,524 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:13:59,524 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:13:59,524 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:13:59,533 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:13:59,533 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:13:59,533 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:14:01,536 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 18444
2025-07-09 18:16:41,542 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:16:41,543 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:16:41,543 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:16:41,551 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:16:41,551 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:16:41,551 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:16:43,555 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 20900
2025-07-09 18:18:23,559 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:18:23,559 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:18:23,559 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:18:23,568 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:18:23,568 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:18:23,568 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:18:25,571 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 28916
2025-07-09 18:20:55,578 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:20:55,578 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:20:55,578 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:20:55,587 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:20:55,588 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:20:55,588 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:20:57,591 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 14084
2025-07-09 18:23:17,596 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:23:17,596 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:23:17,596 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:23:17,606 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:23:17,606 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:23:17,606 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:23:19,610 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 10508
2025-07-09 18:25:09,614 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:25:09,615 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:25:09,615 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:25:09,625 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:25:09,625 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:25:09,625 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:25:11,628 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 26024
2025-07-09 18:25:21,629 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:25:21,629 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:25:21,629 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:25:21,640 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:25:21,640 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:25:21,640 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:25:23,645 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 30152
2025-07-09 18:26:53,649 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 18:26:53,649 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 18:26:53,649 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 18:26:53,660 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 18:26:53,660 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 18:26:53,660 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 18:26:55,664 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 27212
2025-07-09 20:27:45,950 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-09 20:27:45,950 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-09 20:27:45,950 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-09 20:27:45,960 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-09 20:27:45,960 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-09 20:27:45,960 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-09 20:27:47,963 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 17896
2025-07-10 09:51:45,919 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-10 09:51:45,919 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-10 09:51:45,923 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-10 09:51:45,923 - __main__ - [32mINFO[0m - 检测到开机启动，等待30秒后启动桌面小部件...
2025-07-10 09:52:15,924 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-10 09:52:17,928 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 26424
2025-07-10 23:16:55,549 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-10 23:16:55,549 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-10 23:16:55,549 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-10 23:16:55,549 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-10 23:16:55,550 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-10 23:16:57,553 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 36612
2025-07-10 23:16:57,554 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-10 23:16:57,554 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-10 23:16:57,554 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-10 23:16:57,568 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-10 23:16:57,569 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-10 23:16:57,569 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-10 23:16:59,573 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 30788
2025-07-11 09:17:16,475 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-11 09:17:16,475 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-11 09:17:16,475 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-11 09:17:16,476 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-11 09:17:16,476 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-11 09:17:18,479 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 7624
2025-07-14 14:31:42,306 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-14 14:31:42,306 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\daemon.log
2025-07-14 14:31:42,306 - __main__ - [32mINFO[0m - 桌面小部件守护进程启动
2025-07-14 14:31:42,307 - __main__ - [32mINFO[0m - 手动启动，立即启动桌面小部件...
2025-07-14 14:31:42,307 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-14 14:31:44,311 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 14892
2025-07-15 21:03:20,191 - __main__ - [32mINFO[0m - 收到手动接管命令，停止自启动流程并重启
2025-07-15 21:03:20,192 - __main__ - [32mINFO[0m - 开始手动接管流程...
2025-07-15 21:03:20,192 - __main__ - [32mINFO[0m - 停止当前小组件进程
2025-07-15 21:03:20,206 - __main__ - [32mINFO[0m - 切换到手动模式
2025-07-15 21:03:20,206 - __main__ - [32mINFO[0m - 手动模式下重新启动小组件...
2025-07-15 21:03:20,206 - __main__ - [32mINFO[0m - 启动桌面小部件...
2025-07-15 21:03:22,210 - __main__ - [32mINFO[0m - 桌面小部件启动成功，PID: 5260
