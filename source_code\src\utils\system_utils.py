"""
系统信息工具类

提供系统信息获取和监控功能，包括：
- CPU信息和使用率
- 内存信息和使用率
- 磁盘信息和使用率
- 网络信息
- 进程信息
- 系统基本信息
"""

import psutil
import platform
import socket
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta


class SystemUtils:
    """系统信息工具类"""
    
    @staticmethod
    def get_system_info() -> Dict:
        """
        获取系统基本信息
        
        Returns:
            系统信息字典
        """
        try:
            uname = platform.uname()
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            
            return {
                'system': uname.system,
                'node_name': uname.node,
                'release': uname.release,
                'version': uname.version,
                'machine': uname.machine,
                'processor': uname.processor,
                'platform': platform.platform(),
                'architecture': platform.architecture(),
                'python_version': platform.python_version(),
                'boot_time': boot_time,
                'uptime': datetime.now() - boot_time
            }
        except Exception:
            return {}
    
    @staticmethod
    def get_cpu_info() -> Dict:
        """
        获取CPU信息
        
        Returns:
            CPU信息字典
        """
        try:
            cpu_freq = psutil.cpu_freq()
            cpu_count_logical = psutil.cpu_count(logical=True)
            cpu_count_physical = psutil.cpu_count(logical=False)
            
            return {
                'physical_cores': cpu_count_physical,
                'logical_cores': cpu_count_logical,
                'max_frequency': cpu_freq.max if cpu_freq else None,
                'min_frequency': cpu_freq.min if cpu_freq else None,
                'current_frequency': cpu_freq.current if cpu_freq else None,
                'usage_percent': psutil.cpu_percent(interval=1),
                'usage_per_core': psutil.cpu_percent(interval=1, percpu=True)
            }
        except Exception:
            return {}
    
    @staticmethod
    def get_memory_info() -> Dict:
        """
        获取内存信息
        
        Returns:
            内存信息字典
        """
        try:
            virtual_memory = psutil.virtual_memory()
            swap_memory = psutil.swap_memory()
            
            return {
                'total': virtual_memory.total,
                'available': virtual_memory.available,
                'used': virtual_memory.used,
                'free': virtual_memory.free,
                'percent': virtual_memory.percent,
                'swap_total': swap_memory.total,
                'swap_used': swap_memory.used,
                'swap_free': swap_memory.free,
                'swap_percent': swap_memory.percent
            }
        except Exception:
            return {}
    
    @staticmethod
    def get_disk_info() -> List[Dict]:
        """
        获取磁盘信息
        
        Returns:
            磁盘信息列表
        """
        disks = []
        try:
            partitions = psutil.disk_partitions()
            for partition in partitions:
                try:
                    disk_usage = psutil.disk_usage(partition.mountpoint)
                    disks.append({
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'file_system': partition.fstype,
                        'total': disk_usage.total,
                        'used': disk_usage.used,
                        'free': disk_usage.free,
                        'percent': (disk_usage.used / disk_usage.total) * 100
                    })
                except PermissionError:
                    continue
        except Exception:
            pass
        return disks
    
    @staticmethod
    def get_network_info() -> Dict:
        """
        获取网络信息
        
        Returns:
            网络信息字典
        """
        try:
            hostname = socket.gethostname()
            ip_address = socket.gethostbyname(hostname)
            
            # 网络接口信息
            net_if_addrs = psutil.net_if_addrs()
            interfaces = {}
            for interface_name, interface_addresses in net_if_addrs.items():
                addresses = []
                for address in interface_addresses:
                    addresses.append({
                        'family': str(address.family),
                        'address': address.address,
                        'netmask': address.netmask,
                        'broadcast': address.broadcast
                    })
                interfaces[interface_name] = addresses
            
            # 网络统计信息
            net_io = psutil.net_io_counters()
            
            return {
                'hostname': hostname,
                'ip_address': ip_address,
                'interfaces': interfaces,
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv,
                'errin': net_io.errin,
                'errout': net_io.errout,
                'dropin': net_io.dropin,
                'dropout': net_io.dropout
            }
        except Exception:
            return {}
    
    @staticmethod
    def get_process_list() -> List[Dict]:
        """
        获取进程列表
        
        Returns:
            进程信息列表
        """
        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception:
            pass
        return processes
    
    @staticmethod
    def get_process_info(pid: int) -> Optional[Dict]:
        """
        获取指定进程信息
        
        Args:
            pid: 进程ID
            
        Returns:
            进程信息字典，如果进程不存在返回None
        """
        try:
            proc = psutil.Process(pid)
            return {
                'pid': proc.pid,
                'name': proc.name(),
                'username': proc.username(),
                'status': proc.status(),
                'create_time': datetime.fromtimestamp(proc.create_time()),
                'cpu_percent': proc.cpu_percent(),
                'memory_percent': proc.memory_percent(),
                'memory_info': proc.memory_info()._asdict(),
                'num_threads': proc.num_threads(),
                'cmdline': proc.cmdline(),
                'exe': proc.exe(),
                'cwd': proc.cwd()
            }
        except (psutil.NoSuchProcess, psutil.AccessDenied, Exception):
            return None
    
    @staticmethod
    def kill_process(pid: int) -> bool:
        """
        终止指定进程
        
        Args:
            pid: 进程ID
            
        Returns:
            是否成功终止进程
        """
        try:
            proc = psutil.Process(pid)
            proc.terminate()
            return True
        except (psutil.NoSuchProcess, psutil.AccessDenied, Exception):
            return False
    
    @staticmethod
    def get_battery_info() -> Optional[Dict]:
        """
        获取电池信息（如果有）
        
        Returns:
            电池信息字典，如果没有电池返回None
        """
        try:
            battery = psutil.sensors_battery()
            if battery:
                return {
                    'percent': battery.percent,
                    'secsleft': battery.secsleft,
                    'power_plugged': battery.power_plugged
                }
        except Exception:
            pass
        return None
    
    @staticmethod
    def get_temperature_info() -> Dict:
        """
        获取温度信息
        
        Returns:
            温度信息字典
        """
        try:
            temps = psutil.sensors_temperatures()
            temperature_info = {}
            for name, entries in temps.items():
                temperature_info[name] = []
                for entry in entries:
                    temperature_info[name].append({
                        'label': entry.label or 'Unknown',
                        'current': entry.current,
                        'high': entry.high,
                        'critical': entry.critical
                    })
            return temperature_info
        except Exception:
            return {}
    
    @staticmethod
    def format_bytes(bytes_value: int) -> str:
        """
        格式化字节数为人类可读格式
        
        Args:
            bytes_value: 字节数
            
        Returns:
            格式化后的字符串
        """
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} PB"
    
    @staticmethod
    def is_admin() -> bool:
        """
        检查当前用户是否有管理员权限
        
        Returns:
            是否有管理员权限
        """
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except Exception:
            return False
    
    @staticmethod
    def get_startup_programs() -> List[Dict]:
        """
        获取开机启动程序列表
        
        Returns:
            启动程序信息列表
        """
        startup_programs = []
        try:
            import winreg
            
            # 检查注册表中的启动项
            startup_keys = [
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_LOCAL_MACHINE, r"Software\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\RunOnce"),
                (winreg.HKEY_LOCAL_MACHINE, r"Software\Microsoft\Windows\CurrentVersion\RunOnce")
            ]
            
            for hkey, subkey in startup_keys:
                try:
                    with winreg.OpenKey(hkey, subkey) as key:
                        i = 0
                        while True:
                            try:
                                name, value, _ = winreg.EnumValue(key, i)
                                startup_programs.append({
                                    'name': name,
                                    'command': value,
                                    'location': f"{hkey}\\{subkey}"
                                })
                                i += 1
                            except WindowsError:
                                break
                except Exception:
                    continue
        except Exception:
            pass
        
        return startup_programs
