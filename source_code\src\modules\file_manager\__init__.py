"""
File Manager Module for Personal Manager System

This module provides file management functionality including:
- File browsing and navigation
- File operations (copy, move, delete, rename)
- File bookmarks and favorites
- File search and filtering
- File tagging system
"""

from .file_service import FileService

# Import GUI components only when needed
try:
    from .file_manager_widget import FileManagerWidget
    __all__ = ['FileManagerWidget', 'FileService']
except ImportError:
    # PyQt6 not available, only export service
    __all__ = ['FileService']