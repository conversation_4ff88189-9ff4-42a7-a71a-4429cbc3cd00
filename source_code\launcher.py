#!/usr/bin/env python3
"""
Application Launcher - 造神计划
应用程序启动器，确保单一进程运行和统一管理
"""

import os
import sys
import time
import psutil
import subprocess
from pathlib import Path
from typing import Optional


class ApplicationLauncher:
    """应用程序启动器 - 造神计划"""
    
    def __init__(self):
        self.app_name = "造神计划"
        self.project_root = Path(__file__).parent
        self.main_script = self.project_root / "main.py"
        self.lock_file = Path.home() / f".{self.app_name.lower()}_launcher_lock"
        
        print(f"造神计划启动器初始化")
    
    def find_existing_process(self) -> Optional[psutil.Process]:
        """查找已存在的应用进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info.get('cmdline', [])
                    if not cmdline:
                        continue
                    
                    cmdline_str = ' '.join(cmdline)
                    
                    # 检查是否是造神计划主进程
                    if (('main.py' in cmdline_str or '造神计划.exe' in cmdline_str) and
                        ('造神计划' in cmdline_str or 'personal_manager' in cmdline_str)):
                        return proc
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        
        except Exception as e:
            print(f"查找进程时出错: {e}")
        
        return None
    
    def is_launcher_running(self) -> bool:
        """检查启动器是否已在运行"""
        try:
            if self.lock_file.exists():
                with open(self.lock_file, 'r') as f:
                    existing_pid = int(f.read().strip())
                
                if psutil.pid_exists(existing_pid):
                    try:
                        existing_process = psutil.Process(existing_pid)
                        if 'launcher.py' in ' '.join(existing_process.cmdline()):
                            return True
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                
                # 如果进程不存在，删除锁文件
                self.lock_file.unlink()
            
            return False
        
        except Exception as e:
            print(f"检查启动器状态时出错: {e}")
            return False
    
    def create_launcher_lock(self):
        """创建启动器锁文件"""
        try:
            with open(self.lock_file, 'w') as f:
                f.write(str(os.getpid()))
        except Exception as e:
            print(f"创建启动器锁文件失败: {e}")
    
    def cleanup_launcher_lock(self):
        """清理启动器锁文件"""
        try:
            if self.lock_file.exists():
                self.lock_file.unlink()
        except Exception as e:
            print(f"清理启动器锁文件失败: {e}")
    
    def bring_window_to_front(self, process: psutil.Process):
        """将窗口置于前台"""
        try:
            if os.name == 'nt':  # Windows
                import ctypes
                from ctypes import wintypes
                
                # 获取窗口句柄
                def enum_windows_callback(hwnd, pid):
                    if ctypes.windll.user32.GetWindowThreadProcessId(hwnd, None) == pid:
                        # 显示窗口
                        ctypes.windll.user32.ShowWindow(hwnd, 9)  # SW_RESTORE
                        ctypes.windll.user32.SetForegroundWindow(hwnd)
                        return False
                    return True
                
                # 枚举窗口
                WNDENUMPROC = ctypes.WINFUNCTYPE(wintypes.BOOL, wintypes.HWND, wintypes.LPARAM)
                ctypes.windll.user32.EnumWindows(WNDENUMPROC(enum_windows_callback), process.pid)
                
                print(f"已将窗口置于前台")
            
        except Exception as e:
            print(f"置于前台失败: {e}")
    
    def launch_application(self) -> bool:
        """启动应用程序"""
        try:
            if not self.main_script.exists():
                print(f"错误: 主程序文件不存在: {self.main_script}")
                return False
            
            print("正在启动造神计划...")
            
            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSFSENCODING'] = '0'
            if os.name == 'nt':
                env['PYTHONUTF8'] = '1'
            
            # 启动主程序
            process = subprocess.Popen(
                [sys.executable, str(self.main_script)],
                cwd=str(self.project_root),
                env=env,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            print(f"造神计划已启动，进程ID: {process.pid}")
            
            # 等待一下确保进程启动
            time.sleep(2)
            
            return True
        
        except Exception as e:
            print(f"启动应用程序失败: {e}")
            return False
    
    def run(self):
        """运行启动器"""
        try:
            print("=" * 50)
            print("造神计划启动器")
            print("=" * 50)
            
            # 检查启动器是否已在运行
            if self.is_launcher_running():
                print("启动器已在运行中")
                return
            
            # 创建启动器锁文件
            self.create_launcher_lock()
            
            try:
                # 查找已存在的进程
                existing_process = self.find_existing_process()
                
                if existing_process:
                    print(f"发现造神计划已在运行 (PID: {existing_process.pid})")
                    print("正在将窗口置于前台...")
                    
                    # 尝试将窗口置于前台
                    self.bring_window_to_front(existing_process)
                    
                    print("操作完成")
                else:
                    print("未发现运行中的造神计划实例")
                    
                    # 启动新实例
                    if self.launch_application():
                        print("造神计划启动成功")
                    else:
                        print("造神计划启动失败")
                        return
            
            finally:
                # 清理启动器锁文件
                self.cleanup_launcher_lock()
        
        except KeyboardInterrupt:
            print("\n启动器被用户中断")
        except Exception as e:
            print(f"启动器运行时出错: {e}")
        finally:
            self.cleanup_launcher_lock()


def main():
    """主函数"""
    launcher = ApplicationLauncher()
    launcher.run()


if __name__ == "__main__":
    main()