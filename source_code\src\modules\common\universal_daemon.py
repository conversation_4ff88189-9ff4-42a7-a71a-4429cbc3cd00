"""
Universal Daemon for Desktop Widgets

A unified daemon that can manage different types of desktop widgets,
replacing the need for separate daemon scripts for each widget type.
"""

import os
import sys
import time
import signal
import argparse
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from core.logger import setup_logging, get_logger
from core.config import ConfigManager
from .communication_manager import WidgetCommunicationManager
from .process_manager import WidgetProcessManager


class UniversalDaemon:
    """通用守护进程 - 造神计划"""
    
    def __init__(self, widget_type: str):
        self.widget_type = widget_type
        self.project_root = self._get_project_root()
        
        # 设置日志
        self.setup_logging()
        self.logger = get_logger()
        
        # 配置管理
        self.config_manager = ConfigManager()
        
        # 通信和进程管理
        self.communication_manager = WidgetCommunicationManager(widget_type)
        self.process_manager = WidgetProcessManager(widget_type)
        
        # 状态
        self.widget_process: Optional[subprocess.Popen] = None
        self.is_running = True
        self.manual_mode = False
        
        # 设置信号处理
        self.setup_signal_handlers()
        
        self.logger.info(f"Universal daemon initialized for {widget_type}")
    
    def _get_project_root(self) -> Path:
        """获取项目根目录"""
        current_file = Path(__file__)
        return current_file.parent.parent.parent.parent
    
    def setup_logging(self):
        """设置日志"""
        log_file = self.project_root / f"{self.widget_type}_daemon.log"
        setup_logging(
            level="INFO",
            log_file=str(log_file),
            console_output=False
        )
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        if os.name != 'nt':  # Unix系统
            signal.signal(signal.SIGTERM, self.signal_handler)
            signal.signal(signal.SIGINT, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.shutdown()
    
    def get_widget_script_path(self) -> Path:
        """获取小组件脚本路径"""
        script_map = {
            'desktop_task': 'modules/task_manager/desktop_widget.py',
            'study_floating': 'start_study_floating_widget.py',
            'system_monitor': 'modules/system_monitor/system_monitor_widget.py'
        }
        
        script_relative_path = script_map.get(self.widget_type)
        if not script_relative_path:
            raise ValueError(f"Unknown widget type: {self.widget_type}")
        
        if script_relative_path.startswith('modules/'):
            # 模块内的脚本
            script_path = self.project_root / 'src' / script_relative_path
        else:
            # 项目根目录的脚本
            script_path = self.project_root / script_relative_path
        
        return script_path
    
    def start_widget(self) -> bool:
        """启动小组件"""
        try:
            if self.widget_process and self.widget_process.poll() is None:
                self.logger.warning(f"{self.widget_type} widget already running")
                return True
            
            widget_script = self.get_widget_script_path()
            if not widget_script.exists():
                self.logger.error(f"Widget script not found: {widget_script}")
                return False
            
            self.logger.info(f"Starting {self.widget_type} widget...")
            
            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSFSENCODING'] = '0'
            if os.name == 'nt':
                env['PYTHONUTF8'] = '1'
            
            # 启动小组件进程
            self.widget_process = subprocess.Popen(
                [sys.executable, str(widget_script)],
                cwd=str(self.project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            self.logger.info(f"{self.widget_type} widget started, PID: {self.widget_process.pid}")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to start {self.widget_type} widget: {e}")
            return False
    
    def stop_widget(self) -> bool:
        """停止小组件"""
        try:
            if not self.widget_process or self.widget_process.poll() is not None:
                self.logger.info(f"{self.widget_type} widget not running")
                return True
            
            self.logger.info(f"Stopping {self.widget_type} widget...")
            
            # 尝试优雅关闭
            self.widget_process.terminate()
            
            # 等待进程结束
            try:
                self.widget_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.logger.warning("Widget process did not terminate, killing...")
                self.widget_process.kill()
            
            self.widget_process = None
            self.logger.info(f"{self.widget_type} widget stopped")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to stop {self.widget_type} widget: {e}")
            return False
    
    def check_commands(self):
        """检查命令"""
        try:
            command_file = self.communication_manager.daemon_command_file
            
            if command_file.exists():
                with open(command_file, 'r', encoding='utf-8') as f:
                    command = f.read().strip()
                
                # 删除命令文件
                command_file.unlink()
                
                if command:
                    self.handle_command(command)
        
        except Exception as e:
            self.logger.debug(f"Error checking commands: {e}")
    
    def handle_command(self, command: str):
        """处理命令"""
        self.logger.info(f"Received command: {command}")
        
        if command == "start":
            self.start_widget()
        elif command == "stop":
            self.stop_widget()
        elif command == "restart":
            self.stop_widget()
            time.sleep(1)
            self.start_widget()
        elif command == "manual_takeover":
            self.handle_manual_takeover()
        elif command == "show":
            self.send_show_command()
        elif command == "hide":
            self.send_hide_command()
        elif command == "shutdown":
            self.shutdown()
        else:
            self.logger.warning(f"Unknown command: {command}")
    
    def handle_manual_takeover(self):
        """处理手动接管"""
        self.manual_mode = True
        self.logger.info("Manual takeover mode activated")
        
        # 如果小组件未运行，启动它
        if not self.widget_process or self.widget_process.poll() is not None:
            self.start_widget()
        
        # 发送显示命令
        self.send_show_command()
    
    def send_show_command(self):
        """发送显示命令给小组件"""
        self.communication_manager.send_show_command("show")
    
    def send_hide_command(self):
        """发送隐藏命令给小组件"""
        self.communication_manager.send_show_command("hide")
    
    def monitor_widget(self):
        """监控小组件状态"""
        if self.widget_process and self.widget_process.poll() is not None:
            # 小组件进程已结束
            exit_code = self.widget_process.returncode
            self.logger.warning(f"{self.widget_type} widget process ended with code: {exit_code}")
            
            # 如果是手动模式且非正常退出，尝试重启
            if self.manual_mode and exit_code != 0:
                self.logger.info("Attempting to restart widget in manual mode...")
                time.sleep(2)
                self.start_widget()
    
    def write_pid_file(self):
        """写入PID文件"""
        try:
            pid_file = self.project_root / f"{self.widget_type}_daemon.pid"
            with open(pid_file, 'w') as f:
                f.write(str(os.getpid()))
        except Exception as e:
            self.logger.warning(f"Failed to write PID file: {e}")
    
    def cleanup_pid_file(self):
        """清理PID文件"""
        try:
            pid_file = self.project_root / f"{self.widget_type}_daemon.pid"
            if pid_file.exists():
                pid_file.unlink()
        except Exception as e:
            self.logger.warning(f"Failed to cleanup PID file: {e}")
    
    def run(self):
        """运行守护进程主循环"""
        self.logger.info(f"Starting {self.widget_type} daemon main loop...")
        
        # 写入PID文件
        self.write_pid_file()
        
        try:
            # 如果是手动模式，立即启动小组件
            if self.manual_mode:
                self.start_widget()
            
            # 主循环
            while self.is_running:
                # 检查命令
                self.check_commands()
                
                # 监控小组件状态
                self.monitor_widget()
                
                # 短暂休眠
                time.sleep(1)
        
        except KeyboardInterrupt:
            self.logger.info("Daemon interrupted by user")
        except Exception as e:
            self.logger.error(f"Daemon error: {e}")
        finally:
            self.shutdown()
    
    def shutdown(self):
        """关闭守护进程"""
        self.logger.info(f"Shutting down {self.widget_type} daemon...")
        
        self.is_running = False
        
        # 停止小组件
        self.stop_widget()
        
        # 清理资源
        self.cleanup_pid_file()
        self.communication_manager.cleanup_files()
        
        self.logger.info("Daemon shutdown complete")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Universal Desktop Widget Daemon - 造神计划")
    parser.add_argument('widget_type', help='Widget type (desktop_task, study_floating, etc.)')
    parser.add_argument('--manual', action='store_true', help='Manual takeover mode')
    
    args = parser.parse_args()
    
    # 创建并运行守护进程
    daemon = UniversalDaemon(args.widget_type)
    daemon.manual_mode = args.manual
    
    try:
        daemon.run()
    except Exception as e:
        print(f"Daemon failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()