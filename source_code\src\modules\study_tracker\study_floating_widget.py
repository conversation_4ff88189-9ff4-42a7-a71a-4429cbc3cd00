"""
学习时间悬浮桌面小组件

提供一个可悬浮在桌面上的小窗口，显示：
- 当前学习状态
- 今日累计学习时间
- 快捷操作按钮
"""

from datetime import datetime, date
from typing import Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QApplication, QSystemTrayIcon, QMenu, QComboBox
)
from PyQt6.QtCore import (
    Qt, QTimer, pyqtSignal, QPoint, QPropertyAnimation, 
    QEasingCurve, QRect
)
from PyQt6.QtGui import (
    QFont, QPalette, QColor, QPainter, QPen, QBrush,
    QLinearGradient, QIcon, QPixmap, QAction, QMouseEvent
)

from core.logger import LoggerMixin
from .study_service import StudyService


class StudyFloatingWidget(QWidget, LoggerMixin):
    """学习时间悬浮桌面小组件"""
    
    # 信号
    show_main_window = pyqtSignal()
    start_study = pyqtSignal()
    pause_study = pyqtSignal()
    resume_study = pyqtSignal()
    stop_study = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)

        # 确保数据库已初始化
        self.init_database()

        self.study_service = StudyService()

        # 拖动相关属性
        self.is_dragging = False
        self.drag_position = QPoint()

        # 显示模式相关属性
        self.current_display_mode = "全显示"  # 默认为全显示
        self.is_collapsed = False  # 是否折叠状态

        # 时间显示状态保持
        self.last_time_display = "00:00:00"  # 保存最后的时间显示
        self.session_ever_started = False   # 是否曾经开始过会话
        
        # 窗口属性
        self.is_dragging = False
        self.drag_position = QPoint()
        self.is_collapsed = False
        
        # 动画
        self.fade_animation = None
        self.resize_animation = None
        
        # 初始化UI
        self.init_ui()
        self.setup_system_tray()
        
        # 定时更新 - 更频繁的更新以显示秒数
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # 每秒更新以显示秒数

        # 设置命令检查定时器
        self.command_timer = QTimer()
        self.command_timer.timeout.connect(self.check_show_command)
        self.command_timer.start(1000)  # 每秒检查一次显示命令

        # 初始位置（右上角）
        self.move_to_corner()
        
        self.logger.info("Study floating widget initialized")

    def init_database(self):
        """初始化数据库连接"""
        try:
            from core.database import init_db_manager, get_db_manager
            from core.config import Config

            # 检查是否已经初始化
            try:
                get_db_manager()
                self.logger.info("Database already initialized")
                return
            except RuntimeError:
                # 数据库未初始化，进行初始化
                pass

            config = Config()
            db_manager = init_db_manager(config)
            self.logger.info("Database initialized for floating widget")
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")

    def check_show_command(self):
        """检查是否有显示命令"""
        try:
            from pathlib import Path
            command_file = Path(__file__).parent.parent.parent.parent.parent / "source_code" / "study_floating_show_command.txt"

            if command_file.exists():
                with open(command_file, 'r', encoding='utf-8') as f:
                    command = f.read().strip()

                # 删除命令文件
                command_file.unlink()

                if command == "show":
                    self.show_widget()

        except Exception as e:
            # 静默处理错误，避免日志噪音
            pass

    def show_widget(self):
        """显示小组件"""
        self.show()
        self.raise_()
        self.activateWindow()
        self.logger.info("Study floating widget shown via command")

    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件 - 开始拖动"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_dragging = True
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 拖动窗口"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.is_dragging:
            self.move(event.globalPosition().toPoint() - self.drag_position)
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件 - 结束拖动"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_dragging = False
        super().mouseReleaseEvent(event)
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # 设置窗口尺寸为1.5倍（原来200x150，现在300x225）
        self.default_width = 300
        self.default_height = 225
        self.setMinimumSize(self.default_width, self.default_height)
        self.resize(self.default_width, self.default_height)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)
        
        # 标题栏
        title_layout = QHBoxLayout()
        
        self.title_label = QLabel("学习追踪")
        title_font = QFont()
        title_font.setPointSize(10)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        self.title_label.setStyleSheet("color: white;")
        
        # 控制按钮
        self.collapse_btn = QPushButton("−")
        self.collapse_btn.setFixedSize(20, 20)
        self.collapse_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.3);
                border: none;
                border-radius: 10px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.5);
            }
        """)
        self.collapse_btn.clicked.connect(self.toggle_collapse)
        
        self.close_btn = QPushButton("×")
        self.close_btn.setFixedSize(20, 20)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 0, 0, 0.7);
                border: none;
                border-radius: 10px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 0, 0, 0.9);
            }
        """)
        self.close_btn.clicked.connect(self.close_widget)
        
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()

        # 显示模式下拉菜单
        self.display_mode_combo = QComboBox()
        self.display_mode_combo.addItems(['全显示', '半显示'])
        self.display_mode_combo.setFixedSize(100, 25)
        self.display_mode_combo.setFont(QFont('Microsoft YaHei', 9))
        self.display_mode_combo.setToolTip("选择显示模式：\n全显示 - 始终置顶显示\n半显示 - 可被其他应用覆盖")
        self.display_mode_combo.currentTextChanged.connect(self.on_display_mode_changed)
        self.display_mode_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.5);
                border-radius: 3px;
                color: white;
                font-size: 9px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """)
        title_layout.addWidget(self.display_mode_combo)

        title_layout.addWidget(self.collapse_btn)
        title_layout.addWidget(self.close_btn)
        
        main_layout.addLayout(title_layout)
        
        # 内容区域
        self.content_widget = QWidget()
        content_layout = QVBoxLayout(self.content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(3)
        
        # 状态显示
        self.status_label = QLabel("准备开始")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_font = QFont()
        status_font.setPointSize(9)
        self.status_label.setFont(status_font)
        self.status_label.setStyleSheet("color: white;")
        content_layout.addWidget(self.status_label)
        
        # 时间显示 - 初始显示00:00:00
        self.time_label = QLabel("00:00:00")
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        time_font = QFont()
        time_font.setPointSize(14)
        time_font.setBold(True)
        self.time_label.setFont(time_font)
        self.time_label.setStyleSheet("color: #FFD700;")  # 金色
        content_layout.addWidget(self.time_label)
        
        # 今日统计
        self.today_label = QLabel("今日: 0分钟")
        self.today_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        today_font = QFont()
        today_font.setPointSize(8)
        self.today_label.setFont(today_font)
        self.today_label.setStyleSheet("color: #E0E0E0;")
        content_layout.addWidget(self.today_label)
        
        # 操作按钮
        button_layout = QVBoxLayout()

        # 第一行：开始/停止按钮
        first_row = QHBoxLayout()

        self.start_btn = QPushButton("开始")
        self.start_btn.setFixedHeight(25)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(46, 204, 113, 0.8);
                border: none;
                border-radius: 12px;
                color: white;
                font-weight: bold;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: rgba(46, 204, 113, 1.0);
            }
            QPushButton:pressed {
                background-color: rgba(39, 174, 96, 1.0);
            }
        """)
        self.start_btn.clicked.connect(self.start_session)
        self.logger.info("Start button connected to start_session")

        self.pause_btn = QPushButton("暂停")
        self.pause_btn.setFixedHeight(25)
        self.pause_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(243, 156, 18, 0.8);
                border: none;
                border-radius: 12px;
                color: white;
                font-weight: bold;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: rgba(243, 156, 18, 1.0);
            }
            QPushButton:pressed {
                background-color: rgba(211, 84, 0, 1.0);
            }
        """)
        self.pause_btn.clicked.connect(self.pause_session)
        self.pause_btn.setEnabled(False)
        self.logger.info("Pause button connected to pause_session")

        self.stop_btn = QPushButton("停止")
        self.stop_btn.setFixedHeight(25)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(231, 76, 60, 0.8);
                border: none;
                border-radius: 12px;
                color: white;
                font-weight: bold;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: rgba(231, 76, 60, 1.0);
            }
            QPushButton:pressed {
                background-color: rgba(192, 57, 43, 1.0);
            }
        """)
        self.stop_btn.clicked.connect(self.stop_session)
        self.stop_btn.setEnabled(False)
        self.logger.info("Stop button connected to stop_session")

        first_row.addWidget(self.start_btn)
        first_row.addWidget(self.pause_btn)
        first_row.addWidget(self.stop_btn)

        # 第二行：主界面按钮
        second_row = QHBoxLayout()

        self.main_btn = QPushButton("主界面")
        self.main_btn.setFixedHeight(25)
        self.main_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(52, 152, 219, 0.8);
                border: none;
                border-radius: 12px;
                color: white;
                font-weight: bold;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: rgba(52, 152, 219, 1.0);
            }
            QPushButton:pressed {
                background-color: rgba(41, 128, 185, 1.0);
            }
        """)
        self.main_btn.clicked.connect(self.open_main_app)

        second_row.addWidget(self.main_btn)

        button_layout.addLayout(first_row)
        button_layout.addLayout(second_row)
        content_layout.addLayout(button_layout)
        
        main_layout.addWidget(self.content_widget)
    
    def setup_system_tray(self):
        """设置系统托盘"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)
            
            # 创建托盘图标
            pixmap = QPixmap(16, 16)
            pixmap.fill(QColor(52, 152, 219))
            self.tray_icon.setIcon(QIcon(pixmap))
            
            # 创建托盘菜单
            tray_menu = QMenu()
            
            show_action = QAction("显示悬浮窗", self)
            show_action.triggered.connect(self.show)
            tray_menu.addAction(show_action)
            
            main_action = QAction("打开主界面", self)
            main_action.triggered.connect(self.show_main_window.emit)
            tray_menu.addAction(main_action)
            
            tray_menu.addSeparator()
            
            quit_action = QAction("退出", self)
            quit_action.triggered.connect(QApplication.instance().quit)
            tray_menu.addAction(quit_action)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.show()
            
            # 双击托盘图标显示悬浮窗
            self.tray_icon.activated.connect(self.on_tray_activated)
    
    def paintEvent(self, event):
        """绘制背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 创建渐变背景
        gradient = QLinearGradient(0, 0, 0, self.height())
        gradient.setColorAt(0, QColor(52, 73, 94, 200))  # 深蓝色，半透明
        gradient.setColorAt(1, QColor(44, 62, 80, 220))  # 更深的蓝色
        
        # 绘制圆角矩形背景
        painter.setBrush(QBrush(gradient))
        painter.setPen(QPen(QColor(255, 255, 255, 100), 1))
        painter.drawRoundedRect(self.rect(), 10, 10)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_dragging = True
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.is_dragging:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        self.is_dragging = False
    
    def mouseDoubleClickEvent(self, event):
        """双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.show_main_window.emit()
    
    # ==================== 公共方法 ====================
    
    def move_to_corner(self):
        """移动到屏幕右上角"""
        screen = QApplication.primaryScreen().availableGeometry()
        self.move(screen.width() - self.width() - 20, 20)
    
    def toggle_collapse(self):
        """切换折叠状态"""
        if self.is_collapsed:
            self.expand()
        else:
            self.collapse()
    
    def collapse(self):
        """折叠窗口"""
        if self.is_collapsed:
            return
        
        self.is_collapsed = True
        self.content_widget.hide()
        self.setFixedSize(self.default_width, 40)
        self.collapse_btn.setText("+")
        
        self.logger.info("Floating widget collapsed")
    
    def expand(self):
        """展开窗口"""
        if not self.is_collapsed:
            return
        
        self.is_collapsed = False
        self.content_widget.show()
        self.setFixedSize(self.default_width, self.default_height)
        self.collapse_btn.setText("−")
        
        self.logger.info("Floating widget expanded")
    
    def update_display(self):
        """更新显示内容"""
        try:
            # 使用缓存状态而不是ORM对象
            try:
                has_session = self.study_service.current_session_cache['id'] is not None

                if has_session:
                    session_status = self.study_service.current_session_cache['status']

                    # 根据会话状态更新按钮状态
                    if session_status == "active":
                        self.status_label.setText("学习中...")
                        self.start_btn.setEnabled(False)
                        self.pause_btn.setEnabled(True)
                        self.pause_btn.setText("暂停")
                        self.stop_btn.setEnabled(True)
                    elif session_status == "paused":
                        self.status_label.setText("已暂停")
                        self.start_btn.setEnabled(False)
                        self.pause_btn.setEnabled(True)
                        self.pause_btn.setText("继续")
                        self.stop_btn.setEnabled(True)

                    # 显示当前学习时间（精确到秒）
                    try:
                        current_duration_seconds = self.study_service.get_current_duration_seconds()
                        hours = current_duration_seconds // 3600
                        minutes = (current_duration_seconds % 3600) // 60
                        seconds = current_duration_seconds % 60

                        # 始终显示 HH:MM:SS 格式
                        time_display = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                        self.time_label.setText(time_display)
                        self.last_time_display = time_display  # 保存最后的时间显示
                        self.session_ever_started = True      # 标记曾经开始过会话
                    except:
                        self.time_label.setText(self.last_time_display)
                else:
                    # 没有活动会话 - 保持上次的时间显示
                    self.status_label.setText("准备开始")
                    # 保持上次的时间显示，不重置
                    if not self.session_ever_started:
                        self.time_label.setText("00:00:00")  # 只有从未开始过才显示00:00:00
                    self.start_btn.setEnabled(True)
                    self.pause_btn.setEnabled(False)
                    self.pause_btn.setText("暂停")
                    self.stop_btn.setEnabled(False)
            except:
                # 如果获取会话信息失败，使用默认状态
                self.status_label.setText("准备开始")
                # 保持上次的时间显示，不重置
                if not self.session_ever_started:
                    self.time_label.setText("00:00:00")  # 只有从未开始过才显示00:00:00
                self.start_btn.setEnabled(True)
                self.pause_btn.setEnabled(False)
                self.pause_btn.setText("暂停")
                self.stop_btn.setEnabled(False)

            # 更新今日统计（简化处理）
            try:
                today_stats = self.study_service.get_daily_statistics()
                today_minutes = today_stats.get('total_minutes', 0)
                today_sessions = today_stats.get('total_sessions', 0)

                if today_minutes >= 60:
                    hours = today_minutes // 60
                    minutes = today_minutes % 60
                    self.today_label.setText(f"今日: {hours}h{minutes}m ({today_sessions}次)")
                else:
                    self.today_label.setText(f"今日: {today_minutes}分钟 ({today_sessions}次)")
            except:
                self.today_label.setText("今日: 0分钟 (0次)")

        except Exception as e:
            self.logger.error(f"Error updating floating widget display: {e}")
            # 设置默认状态
            self.status_label.setText("准备开始")
            self.time_label.setText("00:00")
            self.today_label.setText("今日: 0分钟 (0次)")
    
    # ==================== 事件处理 ====================
    
    def start_session(self):
        """开始学习会话"""
        print("🔥 START BUTTON CLICKED!")  # 调试输出
        self.logger.info("START BUTTON CLICKED - Starting study session from floating widget")

        try:
            self.status_label.setText("正在开始...")

            # 简化处理：直接创建学习会话，不依赖复杂的科目管理
            subject_id = self.ensure_default_subject()
            if not subject_id:
                self.status_label.setText("科目准备失败")
                self.logger.error("Failed to ensure default subject")
                print("❌ Failed to ensure default subject")
                return

            print(f"✅ Got subject ID: {subject_id}")

            # 开始学习会话
            session = self.study_service.start_session(
                subject_id=subject_id,
                title="悬浮小组件学习",
                planned_duration=25  # 默认25分钟
            )

            if session:
                print("✅ Session created successfully")
                self.start_btn.setEnabled(False)
                self.pause_btn.setEnabled(True)
                self.stop_btn.setEnabled(True)
                self.status_label.setText("学习中")
                self.start_study.emit()
                self.logger.info("Started study session from floating widget successfully")
            else:
                print("❌ Failed to create session")
                self.status_label.setText("开始失败")
                self.logger.error("Failed to start study session")

        except Exception as e:
            print(f"❌ Exception in start_session: {e}")
            self.logger.error(f"Error starting session: {e}")
            self.status_label.setText("开始失败")
            import traceback
            traceback.print_exc()

    def ensure_default_subject(self):
        """确保有默认科目可用"""
        try:
            # 尝试获取现有科目（现在返回字典格式）
            subjects = self.study_service.get_subjects()
            if subjects and len(subjects) > 0:
                # 查找默认学习科目
                for subject in subjects:
                    if subject['name'] == "默认学习":
                        self.logger.info(f"Found existing default subject with ID: {subject['id']}")
                        return subject['id']

                # 如果没有找到默认学习科目，使用第一个科目
                first_subject = subjects[0]
                self.logger.info(f"Using first subject with ID: {first_subject['id']}")
                return first_subject['id']

            # 如果没有科目，创建默认科目
            self.logger.info("No subjects found, creating default subject")
            default_subject = self.study_service.create_subject(
                name="默认学习",
                description="通过悬浮小组件创建的默认学习科目",
                color="#3498db"
            )

            if default_subject and hasattr(default_subject, 'id'):
                self.logger.info(f"Created new default subject with ID: {default_subject.id}")
                return default_subject.id
            else:
                self.logger.error("Failed to create default subject")
                return None

        except Exception as e:
            self.logger.error(f"Error ensuring default subject: {e}")
            return None

    def pause_session(self):
        """暂停学习会话"""
        try:
            # 使用缓存状态检查而不是ORM对象
            if (self.study_service.current_session_cache['id'] is not None and
                self.study_service.current_session_cache['status'] == 'active'):

                success = self.study_service.pause_session()
                if success:
                    self.pause_btn.setText("继续")
                    self.pause_btn.clicked.disconnect()
                    self.pause_btn.clicked.connect(self.resume_session)
                    self.status_label.setText("已暂停")
                    self.pause_study.emit()
                    self.logger.info("Paused study session from floating widget")
                else:
                    self.status_label.setText("暂停失败")
            else:
                self.status_label.setText("无活动会话")

        except Exception as e:
            self.logger.error(f"Error pausing session: {e}")
            self.status_label.setText("暂停失败")

    def resume_session(self):
        """恢复学习会话"""
        try:
            # 使用缓存状态检查而不是ORM对象
            if (self.study_service.current_session_cache['id'] is not None and
                self.study_service.current_session_cache['status'] == 'paused'):

                success = self.study_service.resume_session()
                if success:
                    self.pause_btn.setText("暂停")
                    self.pause_btn.clicked.disconnect()
                    self.pause_btn.clicked.connect(self.pause_session)
                    self.status_label.setText("学习中")
                    self.resume_study.emit()
                    self.logger.info("Resumed study session from floating widget")
                else:
                    self.status_label.setText("恢复失败")
            else:
                self.status_label.setText("无暂停会话")

        except Exception as e:
            self.logger.error(f"Error resuming session: {e}")
            self.status_label.setText("恢复失败")

    def stop_session(self):
        """停止学习会话"""
        try:
            success = self.study_service.complete_session()
            if success:
                self.start_btn.setEnabled(True)
                self.pause_btn.setEnabled(False)
                self.pause_btn.setText("暂停")
                self.pause_btn.clicked.disconnect()
                self.pause_btn.clicked.connect(self.pause_session)
                self.stop_btn.setEnabled(False)
                self.status_label.setText("已完成")

                # 只有在停止时才清零时间显示
                self.time_label.setText("00:00:00")
                self.last_time_display = "00:00:00"
                self.session_ever_started = False

                self.stop_study.emit()
                self.logger.info("Completed study session from floating widget")
            else:
                self.status_label.setText("停止失败")

        except Exception as e:
            self.logger.error(f"Error stopping session: {e}")
            self.status_label.setText("停止失败")

    def on_display_mode_changed(self, mode: str):
        """显示模式改变"""
        self.current_display_mode = mode
        self.set_display_mode(mode)

    def set_display_mode(self, mode: str):
        """设置显示模式"""
        if mode == "全显示":
            # 全显示模式：始终置顶
            flags = (Qt.WindowType.FramelessWindowHint |
                    Qt.WindowType.WindowStaysOnTopHint |
                    Qt.WindowType.Tool)
        else:  # 半显示
            # 半显示模式：可被其他应用覆盖
            flags = (Qt.WindowType.FramelessWindowHint |
                    Qt.WindowType.Tool)

        # 保存当前位置和大小
        current_geometry = self.geometry()
        was_visible = self.isVisible()

        # 设置新的窗口标志
        self.setWindowFlags(flags)

        # 恢复位置和大小
        self.setGeometry(current_geometry)

        # 如果之前是可见的，重新显示
        if was_visible:
            self.show()

    def toggle_collapse(self):
        """切换折叠状态"""
        if self.is_collapsed:
            # 展开
            self.content_widget.show()
            self.collapse_btn.setText("−")
            self.resize(self.default_width, self.default_height)
            self.is_collapsed = False
        else:
            # 折叠
            self.content_widget.hide()
            self.collapse_btn.setText("+")
            self.resize(self.default_width, 40)  # 只显示标题栏
            self.is_collapsed = True

    def open_main_app(self):
        """打开主应用"""
        try:
            # 检查主应用是否已经在运行
            if self.is_main_app_running():
                # 尝试激活已有的应用窗口
                self.activate_main_app_window()
                return

            # 获取主应用路径
            from pathlib import Path
            import sys
            current_dir = Path(__file__).parent.parent.parent.parent
            main_py = current_dir / "main.py"

            if main_py.exists():
                import subprocess
                subprocess.Popen([sys.executable, str(main_py)],
                               cwd=str(current_dir))
                self.logger.info("主应用程序已启动")
            else:
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "找不到主应用程序")

        except Exception as e:
            self.logger.error(f"打开主应用失败: {e}")
            print(f"打开主应用失败: {e}")

    def is_main_app_running(self):
        """检查主应用是否正在运行"""
        try:
            import psutil
            import sys
            import os

            current_python = sys.executable
            current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            main_py_path = os.path.join(current_dir, "main.py")

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and len(cmdline) >= 2:
                            if (cmdline[0] == current_python and
                                cmdline[1] == main_py_path):
                                return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            return False

        except Exception:
            return False

    def activate_main_app_window(self):
        """激活主应用窗口"""
        try:
            import sys
            if sys.platform == "win32":
                try:
                    import win32gui
                    import win32con

                    def enum_windows_callback(hwnd, windows):
                        if win32gui.IsWindowVisible(hwnd):
                            window_title = win32gui.GetWindowText(hwnd)
                            if "Personal Manager" in window_title:
                                windows.append(hwnd)
                        return True

                    windows = []
                    win32gui.EnumWindows(enum_windows_callback, windows)

                    if windows:
                        # 激活找到的第一个窗口
                        hwnd = windows[0]
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        win32gui.SetForegroundWindow(hwnd)
                        self.logger.info("主应用窗口已激活")
                        return True
                except ImportError:
                    # 如果没有win32gui，显示提示
                    from PyQt6.QtWidgets import QMessageBox
                    QMessageBox.information(self, "提示", "主应用已在运行，请查看任务栏")
                    return False

            # 如果是其他平台或者没有找到窗口，显示提示
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "主应用已在运行，请查看任务栏")
            return False

        except Exception as e:
            self.logger.error(f"激活主应用窗口失败: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "主应用已在运行，请查看任务栏")
            return False
    
    def on_tray_activated(self, reason):
        """系统托盘激活事件"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            if self.isVisible():
                self.hide()
            else:
                self.show()
                self.raise_()
                self.activateWindow()
    
    def close_widget(self):
        """关闭按钮点击处理"""
        self.logger.info("StudyFloatingWidget close button clicked by user")

        # 调用真正的关闭
        self.close()

    def closeEvent(self, event):
        """关闭事件"""
        self.logger.info("StudyFloatingWidget closeEvent triggered")

        # 通知主应用端小组件已关闭
        self.notify_main_app_closed()

        # 真正关闭窗口
        self.logger.info("StudyFloatingWidget window closing")
        event.accept()

    def notify_main_app_closed(self):
        """通知主应用端小组件已关闭"""
        try:
            # 创建状态文件，通知主应用端小组件已关闭
            # 确保与 study_floating_widget_controller.py 中的路径一致
            from pathlib import Path
            status_file = Path(__file__).parent.parent.parent.parent.parent / "study_floating_status.txt"

            # 确保父目录存在
            status_file.parent.mkdir(parents=True, exist_ok=True)

            with open(status_file, 'w', encoding='utf-8') as f:
                from datetime import datetime
                content = f"closed:{datetime.now().isoformat()}"
                f.write(content)
                f.flush()  # 强制刷新到磁盘

            self.logger.info(f"已通知主应用端小组件关闭，状态文件: {status_file}")

        except Exception as e:
            self.logger.warning(f"通知主应用端失败: {e}")

    def minimize_to_tray(self):
        """最小化到系统托盘"""
        self.hide()

        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            self.tray_icon.showMessage(
                "学习追踪",
                "应用程序已最小化到系统托盘",
                QSystemTrayIcon.MessageIcon.Information,
                2000
            )
    
    def show_with_animation(self):
        """带动画显示"""
        self.show()
        
        # 淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.fade_animation.start()
    
    def hide_with_animation(self):
        """带动画隐藏"""
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.InCubic)
        self.fade_animation.finished.connect(self.hide)
        self.fade_animation.start()
    
    def set_study_service(self, study_service: StudyService):
        """设置学习服务实例"""
        self.study_service = study_service
