"""
测试悬浮小组件按钮功能
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件
from modules.study_tracker.study_floating_widget import StudyFloatingWidget


class ButtonTestWindow(QMainWindow):
    """按钮测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("悬浮小组件按钮功能测试")
        self.setGeometry(100, 100, 500, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("准备测试悬浮小组件按钮功能")
        layout.addWidget(self.status_label)
        
        # 信息标签
        info_label = QLabel("""
按钮功能测试：
1. 点击"创建悬浮小组件"
2. 测试每个按钮的响应：
   - 绿色"开始"按钮
   - 橙色"暂停"按钮
   - 红色"停止"按钮
   - 蓝色"主界面"按钮
3. 观察控制台输出和状态变化
        """)
        layout.addWidget(info_label)
        
        # 控制按钮
        create_btn = QPushButton("创建悬浮小组件")
        create_btn.clicked.connect(self.create_floating_widget)
        layout.addWidget(create_btn)
        
        test_start_btn = QPushButton("测试开始按钮")
        test_start_btn.clicked.connect(self.test_start_button)
        layout.addWidget(test_start_btn)
        
        test_pause_btn = QPushButton("测试暂停按钮")
        test_pause_btn.clicked.connect(self.test_pause_button)
        layout.addWidget(test_pause_btn)
        
        test_stop_btn = QPushButton("测试停止按钮")
        test_stop_btn.clicked.connect(self.test_stop_button)
        layout.addWidget(test_stop_btn)
        
        # 悬浮小组件实例
        self.floating_widget = None
    
    def create_floating_widget(self):
        """创建悬浮小组件"""
        try:
            if self.floating_widget is None:
                self.status_label.setText("正在创建悬浮小组件...")
                self.floating_widget = StudyFloatingWidget()
                
                # 连接信号以监控按钮点击
                self.floating_widget.start_study.connect(self.on_start_signal)
                self.floating_widget.pause_study.connect(self.on_pause_signal)
                self.floating_widget.resume_study.connect(self.on_resume_signal)
                self.floating_widget.stop_study.connect(self.on_stop_signal)
                self.floating_widget.show_main_window.connect(self.on_show_main_signal)
            
            self.floating_widget.show()
            self.floating_widget.raise_()
            self.floating_widget.activateWindow()
            self.status_label.setText("悬浮小组件已创建并显示")
            print("悬浮小组件已创建")
            
        except Exception as e:
            self.status_label.setText(f"创建失败: {str(e)}")
            print(f"Error creating floating widget: {e}")
            import traceback
            traceback.print_exc()
    
    def test_start_button(self):
        """测试开始按钮"""
        if self.floating_widget:
            print("手动触发开始按钮...")
            self.floating_widget.start_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def test_pause_button(self):
        """测试暂停按钮"""
        if self.floating_widget:
            print("手动触发暂停按钮...")
            self.floating_widget.pause_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def test_stop_button(self):
        """测试停止按钮"""
        if self.floating_widget:
            print("手动触发停止按钮...")
            self.floating_widget.stop_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def on_start_signal(self):
        """开始学习信号"""
        self.status_label.setText("收到开始学习信号")
        print("✅ 开始学习信号触发")
    
    def on_pause_signal(self):
        """暂停学习信号"""
        self.status_label.setText("收到暂停学习信号")
        print("⏸️ 暂停学习信号触发")
    
    def on_resume_signal(self):
        """恢复学习信号"""
        self.status_label.setText("收到恢复学习信号")
        print("▶️ 恢复学习信号触发")
    
    def on_stop_signal(self):
        """停止学习信号"""
        self.status_label.setText("收到停止学习信号")
        print("⏹️ 停止学习信号触发")
    
    def on_show_main_signal(self):
        """显示主窗口信号"""
        self.status_label.setText("收到显示主窗口信号")
        print("🏠 显示主窗口信号触发")
        self.show()
        self.raise_()
        self.activateWindow()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.floating_widget:
            self.floating_widget.close()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建测试窗口
        window = ButtonTestWindow()
        window.show()
        
        print("按钮功能测试程序已启动")
        print("请按照界面提示进行测试")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
