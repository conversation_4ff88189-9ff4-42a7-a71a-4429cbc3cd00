"""
学习追踪悬浮小组件启动脚本

独立启动学习追踪悬浮小组件，支持：
- 独立进程运行
- 命令检查和响应
- 状态通知
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer

# 延迟导入标记
_heavy_imports_loaded = False
_study_service = None
_logger = None


def load_heavy_imports():
    """延迟加载重型模块"""
    global _heavy_imports_loaded, _study_service, _logger
    
    if _heavy_imports_loaded:
        return
    
    try:
        # 延迟导入重型模块
        from modules.study_tracker.study_service import StudyService
        
        # 初始化服务
        _study_service = StudyService()
        
        # 初始化数据库
        init_database()
        
        # 初始化日志
        init_logger()
        
        _heavy_imports_loaded = True
        
    except Exception as e:
        print(f"延迟导入失败: {e}")


def init_database():
    """初始化数据库连接"""
    try:
        import core.database as db
        import core.config as config
        
        config_manager = config.Config()
        db_manager = db.init_db_manager(config_manager)
        print("数据库初始化成功")
    except Exception as e:
        print(f"数据库初始化失败: {e}")


def init_logger():
    """初始化日志"""
    global _logger
    try:
        from core.logger import setup_logging
        from core.config import Config
        
        config = Config()
        setup_logging(config)
        
        import logging
        _logger = logging.getLogger(__name__)
        _logger.info("学习追踪悬浮小组件日志初始化成功")
    except Exception as e:
        print(f"日志初始化失败: {e}")
        import logging
        logging.basicConfig(level=logging.INFO)
        _logger = logging.getLogger(__name__)


def get_study_service():
    """获取学习服务实例"""
    global _study_service
    if _study_service is None:
        load_heavy_imports()
    return _study_service


def get_logger():
    """获取日志实例"""
    global _logger
    if _logger is None:
        load_heavy_imports()
    return _logger


class StudyFloatingWidgetApp:
    """学习追踪悬浮小组件应用"""
    
    def __init__(self):
        self.app = None
        self.widget = None
        self.command_timer = None
        
        # 路径配置
        self.project_root = Path(__file__).parent
        self.command_file = self.project_root / "study_floating_show_command.txt"
        self.status_file = self.project_root.parent / "study_floating_status.txt"
    
    def run(self):
        """运行应用"""
        try:
            # 创建应用
            self.app = QApplication(sys.argv)
            self.app.setQuitOnLastWindowClosed(False)
            self.app.setApplicationName("学习追踪悬浮小组件")
            self.app.setApplicationVersion("1.0.0")
            
            print("学习追踪悬浮小组件应用启动...")
            
            # 延迟初始化
            QTimer.singleShot(100, self.delayed_initialization)
            
            # 启动事件循环
            exit_code = self.app.exec()
            
            # 通知主应用端小组件已关闭
            self.notify_main_app_closed()
            
            return exit_code
            
        except Exception as e:
            print(f"应用启动失败: {e}")
            return 1
    
    def delayed_initialization(self):
        """延迟初始化重型组件"""
        try:
            # 加载重型模块
            load_heavy_imports()
            
            # 创建悬浮小组件
            from modules.study_tracker.study_floating_widget import StudyFloatingWidget
            self.widget = StudyFloatingWidget()

            # 连接主界面信号
            self.widget.show_main_window.connect(self.show_main_application)

            # 设置命令检查定时器
            self.command_timer = QTimer()
            self.command_timer.timeout.connect(self.check_show_command)
            self.command_timer.start(1000)  # 每秒检查一次显示命令
            
            # 显示小组件
            self.widget.show()
            self.widget.raise_()
            self.widget.activateWindow()
            
            logger = get_logger()
            if logger:
                logger.info("学习追踪悬浮小组件初始化完成")
            
            print("学习追踪悬浮小组件已显示")
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"延迟初始化失败: {e}")
            
            # 显示错误信息
            QMessageBox.critical(None, "错误", f"学习追踪悬浮小组件初始化失败:\n{str(e)}")
    
    def check_show_command(self):
        """检查是否有显示命令"""
        try:
            if self.command_file.exists():
                with open(self.command_file, 'r', encoding='utf-8') as f:
                    command = f.read().strip()
                
                # 删除命令文件
                self.command_file.unlink()
                
                if command == "show" and self.widget:
                    self.show_widget()
                    
        except Exception as e:
            # 静默处理错误，避免日志噪音
            pass
    
    def show_widget(self):
        """显示小组件"""
        if self.widget:
            self.widget.show()
            self.widget.raise_()
            self.widget.activateWindow()
            
            logger = get_logger()
            if logger:
                logger.info("学习追踪悬浮小组件已显示")
    
    def notify_main_app_closed(self):
        """通知主应用端小组件已关闭"""
        try:
            # 创建状态文件，通知主应用端小组件已关闭
            status_file = self.project_root.parent / "study_floating_status.txt"
            
            # 确保父目录存在
            status_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(status_file, 'w', encoding='utf-8') as f:
                from datetime import datetime
                content = f"closed:{datetime.now().isoformat()}"
                f.write(content)
                f.flush()  # 强制刷新到磁盘
            
            logger = get_logger()
            if logger:
                logger.info("已通知主应用端小组件关闭")
                
        except Exception as e:
            print(f"通知主应用端失败: {e}")

    def show_main_application(self):
        """显示主应用程序"""
        try:
            # 创建命令文件通知主应用程序显示
            command_file = self.project_root.parent / "show_main_app_command.txt"

            # 确保父目录存在
            command_file.parent.mkdir(parents=True, exist_ok=True)

            with open(command_file, 'w', encoding='utf-8') as f:
                from datetime import datetime
                content = f"show_main_app:{datetime.now().isoformat()}"
                f.write(content)
                f.flush()  # 强制刷新到磁盘

            logger = get_logger()
            if logger:
                logger.info("已请求显示主应用程序")

            print("已请求显示主应用程序")

        except Exception as e:
            print(f"请求显示主应用程序失败: {e}")


def main():
    """主函数"""
    try:
        app = StudyFloatingWidgetApp()
        return app.run()
    except Exception as e:
        print(f"学习追踪悬浮小组件启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
