@echo off
chcp 65001 >nul
title 桌面任务小部件守护进程

echo ========================================
echo 桌面任务小部件守护进程
echo ========================================
echo.

cd /d "%~dp0"

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请确保已安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo 检查依赖包...
python -c "import PyQt6, psutil" >nul 2>&1
if errorlevel 1 (
    echo 错误: 缺少必要的依赖包
    echo 正在尝试安装...
    pip install PyQt6 psutil
    if errorlevel 1 (
        echo 安装失败，请手动安装: pip install PyQt6 psutil
        pause
        exit /b 1
    )
)

echo 检查守护进程状态...
if exist "daemon.pid" (
    echo 守护进程可能已在运行，正在检查...
    python -c "
import psutil
try:
    with open('daemon.pid', 'r') as f:
        pid = int(f.read().strip())
    if psutil.pid_exists(pid):
        print('守护进程已在运行，PID:', pid)
        exit(1)
    else:
        print('PID文件存在但进程不存在，清理旧文件')
        import os
        os.remove('daemon.pid')
except:
    pass
"
    if errorlevel 1 (
        echo 守护进程已在运行
        pause
        exit /b 0
    )
)

echo 启动守护进程...
echo 守护进程将在后台运行，确保桌面小部件持续运行
echo 如需停止，请运行 stop_daemon.bat
echo.

start /min python desktop_widget_daemon.py

echo 等待守护进程启动...
timeout /t 3 /nobreak >nul

if exist "daemon.pid" (
    echo 守护进程启动成功！
    echo 桌面小部件将自动启动并持续运行
) else (
    echo 守护进程启动可能失败，请检查日志文件 daemon.log
)

echo.
echo 按任意键退出...
pause >nul
