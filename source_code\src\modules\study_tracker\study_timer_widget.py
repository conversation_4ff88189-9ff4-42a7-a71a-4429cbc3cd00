"""
学习计时器组件

提供学习时间计时功能，支持：
- 实时计时显示
- 开始、暂停、恢复、停止操作
- 番茄工作法支持
- 休息提醒功能
"""

from datetime import datetime, timedelta
from typing import Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QProgressBar, QGroupBox,
    QFrame, QSizePolicy
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QPalette, QColor, QPainter, QPen

from core.logger import LoggerMixin


class CircularProgressWidget(QWidget):
    """圆形进度条组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.progress = 0  # 0-100
        self.setMinimumSize(200, 200)
        self.setMaximumSize(200, 200)
    
    def set_progress(self, value: int):
        """设置进度值 (0-100)"""
        self.progress = max(0, min(100, value))
        self.update()
    
    def paintEvent(self, event):
        """绘制圆形进度条"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 设置画笔
        pen = QPen()
        pen.setWidth(8)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        
        # 绘制背景圆环
        pen.setColor(QColor(200, 200, 200))
        painter.setPen(pen)
        painter.drawArc(20, 20, 160, 160, 0, 360 * 16)
        
        # 绘制进度圆环
        if self.progress > 0:
            pen.setColor(QColor(52, 152, 219))  # 蓝色
            painter.setPen(pen)
            start_angle = 90 * 16  # 从顶部开始
            span_angle = -int(360 * 16 * self.progress / 100)  # 顺时针
            painter.drawArc(20, 20, 160, 160, start_angle, span_angle)


class StudyTimerWidget(QWidget, LoggerMixin):
    """学习计时器组件"""
    
    # 信号
    start_requested = pyqtSignal()
    pause_requested = pyqtSignal()
    resume_requested = pyqtSignal()
    stop_requested = pyqtSignal()
    break_requested = pyqtSignal(str)  # 休息类型
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 计时器状态
        self.is_running = False
        self.is_paused = False
        self.start_time: Optional[datetime] = None
        self.pause_time: Optional[datetime] = None
        self.total_paused_seconds = 0
        self.current_seconds = 0
        self.planned_seconds = 25 * 60  # 默认25分钟
        
        # 番茄工作法设置
        self.pomodoro_enabled = True
        self.work_duration = 25 * 60  # 25分钟工作
        self.short_break_duration = 5 * 60  # 5分钟短休息
        self.long_break_duration = 15 * 60  # 15分钟长休息
        self.pomodoro_count = 0
        
        # UI组件
        self.init_ui()
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_timer_display)
        self.update_timer.start(1000)  # 每秒更新
        
        self.logger.info("Study timer widget initialized")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # 计时器组
        timer_group = QGroupBox("学习计时器")
        timer_layout = QVBoxLayout(timer_group)
        
        # 圆形进度条和时间显示
        progress_layout = QVBoxLayout()
        progress_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.circular_progress = CircularProgressWidget()
        progress_layout.addWidget(self.circular_progress, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 时间显示
        self.time_label = QLabel("00:00:00")
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        self.time_label.setFont(font)
        progress_layout.addWidget(self.time_label)
        
        # 状态显示
        self.status_label = QLabel("准备开始")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_font = QFont()
        status_font.setPointSize(12)
        self.status_label.setFont(status_font)
        progress_layout.addWidget(self.status_label)
        
        timer_layout.addLayout(progress_layout)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始")
        self.start_btn.setMinimumHeight(40)
        self.start_btn.clicked.connect(self.on_start_clicked)
        
        self.pause_btn = QPushButton("暂停")
        self.pause_btn.setMinimumHeight(40)
        self.pause_btn.setEnabled(False)
        self.pause_btn.clicked.connect(self.on_pause_clicked)
        
        self.stop_btn = QPushButton("停止")
        self.stop_btn.setMinimumHeight(40)
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.on_stop_clicked)
        
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setMinimumHeight(40)
        self.reset_btn.clicked.connect(self.on_reset_clicked)
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.pause_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.reset_btn)
        
        timer_layout.addLayout(control_layout)
        layout.addWidget(timer_group)
        
        # 休息控制
        break_group = QGroupBox("休息控制")
        break_layout = QHBoxLayout(break_group)
        
        self.short_break_btn = QPushButton("短休息 (5分钟)")
        self.short_break_btn.setEnabled(False)
        self.short_break_btn.clicked.connect(lambda: self.request_break("short"))
        
        self.long_break_btn = QPushButton("长休息 (15分钟)")
        self.long_break_btn.setEnabled(False)
        self.long_break_btn.clicked.connect(lambda: self.request_break("long"))
        
        break_layout.addWidget(self.short_break_btn)
        break_layout.addWidget(self.long_break_btn)
        
        layout.addWidget(break_group)
        
        # 番茄工作法信息
        pomodoro_group = QGroupBox("番茄工作法")
        pomodoro_layout = QVBoxLayout(pomodoro_group)
        
        self.pomodoro_count_label = QLabel("完成番茄: 0 个")
        self.pomodoro_progress = QProgressBar()
        self.pomodoro_progress.setRange(0, 4)  # 4个番茄为一个周期
        self.pomodoro_progress.setValue(0)
        
        pomodoro_layout.addWidget(self.pomodoro_count_label)
        pomodoro_layout.addWidget(self.pomodoro_progress)
        
        layout.addWidget(pomodoro_group)
        
        layout.addStretch()
    
    # ==================== 公共方法 ====================
    
    def start_timer(self, planned_duration: int = None):
        """开始计时器"""
        if planned_duration:
            self.planned_seconds = planned_duration * 60
        
        self.is_running = True
        self.is_paused = False
        self.start_time = datetime.now()
        self.total_paused_seconds = 0
        
        self.update_button_states()
        self.status_label.setText("学习中...")
        self.logger.info("Timer started")
    
    def pause_timer(self):
        """暂停计时器"""
        if self.is_running and not self.is_paused:
            self.is_paused = True
            self.pause_time = datetime.now()
            self.update_button_states()
            self.status_label.setText("已暂停")
            self.logger.info("Timer paused")
    
    def resume_timer(self):
        """恢复计时器"""
        if self.is_running and self.is_paused:
            if self.pause_time:
                self.total_paused_seconds += (datetime.now() - self.pause_time).total_seconds()
            self.is_paused = False
            self.pause_time = None
            self.update_button_states()
            self.status_label.setText("学习中...")
            self.logger.info("Timer resumed")
    
    def stop_timer(self):
        """停止计时器"""
        self.is_running = False
        self.is_paused = False
        self.start_time = None
        self.pause_time = None
        self.total_paused_seconds = 0
        self.current_seconds = 0
        
        # 更新番茄计数
        if self.pomodoro_enabled and self.current_seconds >= self.work_duration * 0.8:  # 完成80%算一个番茄
            self.pomodoro_count += 1
            self.update_pomodoro_display()
        
        self.update_button_states()
        self.status_label.setText("已完成")
        self.circular_progress.set_progress(0)
        self.time_label.setText("00:00:00")
        self.logger.info("Timer stopped")
    
    def reset_timer(self):
        """重置计时器"""
        self.is_running = False
        self.is_paused = False
        self.start_time = None
        self.pause_time = None
        self.total_paused_seconds = 0
        self.current_seconds = 0
        
        self.update_button_states()
        self.status_label.setText("准备开始")
        self.circular_progress.set_progress(0)
        self.time_label.setText("00:00:00")
        self.logger.info("Timer reset")
    
    def update_display(self, current_duration_minutes: int):
        """更新显示（由外部调用）"""
        self.current_seconds = current_duration_minutes * 60
        self.update_timer_display()
    
    # ==================== 事件处理 ====================
    
    def on_start_clicked(self):
        """开始按钮点击"""
        if not self.is_running:
            self.start_requested.emit()
        elif self.is_paused:
            self.resume_requested.emit()
    
    def on_pause_clicked(self):
        """暂停按钮点击"""
        if self.is_running and not self.is_paused:
            self.pause_requested.emit()
    
    def on_stop_clicked(self):
        """停止按钮点击"""
        if self.is_running:
            self.stop_requested.emit()
    
    def on_reset_clicked(self):
        """重置按钮点击"""
        self.reset_timer()
    
    def request_break(self, break_type: str):
        """请求休息"""
        self.break_requested.emit(break_type)
    
    # ==================== 私有方法 ====================
    
    def update_timer_display(self):
        """更新计时器显示"""
        if self.is_running and not self.is_paused:
            if self.start_time:
                elapsed = datetime.now() - self.start_time
                self.current_seconds = int(elapsed.total_seconds() - self.total_paused_seconds)
        
        # 格式化时间显示
        hours = self.current_seconds // 3600
        minutes = (self.current_seconds % 3600) // 60
        seconds = self.current_seconds % 60
        
        time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        self.time_label.setText(time_str)
        
        # 更新进度条
        if self.planned_seconds > 0:
            progress = min(100, int(self.current_seconds * 100 / self.planned_seconds))
            self.circular_progress.set_progress(progress)
        
        # 检查是否达到计划时间
        if self.is_running and self.current_seconds >= self.planned_seconds:
            self.status_label.setText("已达到计划时间！")
    
    def update_button_states(self):
        """更新按钮状态"""
        if not self.is_running:
            # 未开始状态
            self.start_btn.setText("开始")
            self.start_btn.setEnabled(True)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            self.short_break_btn.setEnabled(False)
            self.long_break_btn.setEnabled(False)
        elif self.is_paused:
            # 暂停状态
            self.start_btn.setText("恢复")
            self.start_btn.setEnabled(True)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.short_break_btn.setEnabled(True)
            self.long_break_btn.setEnabled(True)
        else:
            # 运行状态
            self.start_btn.setText("开始")
            self.start_btn.setEnabled(False)
            self.pause_btn.setEnabled(True)
            self.stop_btn.setEnabled(True)
            self.short_break_btn.setEnabled(True)
            self.long_break_btn.setEnabled(True)
    
    def update_pomodoro_display(self):
        """更新番茄工作法显示"""
        self.pomodoro_count_label.setText(f"完成番茄: {self.pomodoro_count} 个")
        cycle_progress = self.pomodoro_count % 4
        self.pomodoro_progress.setValue(cycle_progress)
        
        # 每4个番茄建议长休息
        if self.pomodoro_count > 0 and self.pomodoro_count % 4 == 0:
            self.status_label.setText("建议进行长休息！")
    
    def set_planned_duration(self, minutes: int):
        """设置计划时长"""
        self.planned_seconds = minutes * 60
    
    def get_current_duration_minutes(self) -> int:
        """获取当前时长（分钟）"""
        return self.current_seconds // 60
