#!/usr/bin/env python3
"""
简单测试桌面小部件
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

print("开始简单测试...")

try:
    print("1. 导入PyQt6...")
    from PyQt6.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
    from PyQt6.QtCore import Qt
    print("   PyQt6导入成功")
    
    print("2. 创建应用...")
    app = QApplication(sys.argv)
    print("   应用创建成功")
    
    print("3. 创建简单窗口...")
    widget = QWidget()
    widget.setWindowTitle("测试窗口")
    widget.setGeometry(100, 100, 300, 200)
    
    layout = QVBoxLayout()
    label = QLabel("桌面小部件测试")
    label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    layout.addWidget(label)
    widget.setLayout(layout)
    
    widget.show()
    print("   窗口显示成功")
    
    print("4. 测试数据库导入...")
    from core.database import init_database
    print("   数据库模块导入成功")
    
    print("5. 初始化数据库...")
    init_database()
    print("   数据库初始化成功")
    
    print("6. 测试任务服务导入...")
    from modules.task_manager.task_service import TaskService
    print("   任务服务导入成功")
    
    print("7. 创建任务服务...")
    task_service = TaskService()
    print("   任务服务创建成功")
    
    print("8. 测试获取任务...")
    tasks = task_service.get_tasks(limit=5)
    print(f"   获取到 {len(tasks)} 个任务")
    
    print("测试完成！按 Ctrl+C 退出...")
    
    # 运行应用
    sys.exit(app.exec())
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
