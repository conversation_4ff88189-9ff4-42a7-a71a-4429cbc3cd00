#!/usr/bin/env python3
"""
测试桌面小部件控制器性能
"""

import sys
import os
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

def test_controller_performance():
    """测试控制器启动和停止性能"""
    print("开始测试桌面小部件控制器性能...")
    
    app = QApplication(sys.argv)
    
    # 导入控制器
    from src.modules.task_manager.desktop_widget_controller import DesktopWidgetController
    
    controller = DesktopWidgetController()
    
    # 测试启动性能
    print("\n=== 测试启动性能 ===")
    start_time = time.time()
    
    success = controller.start_widget()
    
    end_time = time.time()
    startup_time = end_time - start_time
    
    print(f"启动结果: {'成功' if success else '失败'}")
    print(f"启动耗时: {startup_time:.3f}秒")
    
    if success:
        # 等待一下确保完全启动
        time.sleep(1)
        
        # 测试状态检查性能
        print("\n=== 测试状态检查性能 ===")
        check_times = []
        for i in range(5):
            start_check = time.time()
            is_running = controller.is_running()
            end_check = time.time()
            check_time = end_check - start_check
            check_times.append(check_time)
            print(f"第{i+1}次状态检查: {check_time:.3f}秒, 结果: {is_running}")
        
        avg_check_time = sum(check_times) / len(check_times)
        print(f"平均状态检查时间: {avg_check_time:.3f}秒")
        
        # 测试停止性能
        print("\n=== 测试停止性能 ===")
        start_stop = time.time()
        
        success = controller.stop_widget()
        
        end_stop = time.time()
        stop_time = end_stop - start_stop
        
        print(f"停止结果: {'成功' if success else '失败'}")
        print(f"停止耗时: {stop_time:.3f}秒")
        
        # 性能总结
        print("\n=== 性能总结 ===")
        print(f"启动时间: {startup_time:.3f}秒")
        print(f"停止时间: {stop_time:.3f}秒")
        print(f"状态检查平均时间: {avg_check_time:.3f}秒")
        
        total_time = startup_time + stop_time
        print(f"总操作时间: {total_time:.3f}秒")
        
        # 性能评估
        if startup_time < 2.0:
            print("✅ 启动性能: 优秀")
        elif startup_time < 4.0:
            print("⚠️ 启动性能: 良好")
        else:
            print("❌ 启动性能: 需要优化")
            
        if stop_time < 1.0:
            print("✅ 停止性能: 优秀")
        elif stop_time < 2.0:
            print("⚠️ 停止性能: 良好")
        else:
            print("❌ 停止性能: 需要优化")
            
        if avg_check_time < 0.1:
            print("✅ 状态检查性能: 优秀")
        elif avg_check_time < 0.3:
            print("⚠️ 状态检查性能: 良好")
        else:
            print("❌ 状态检查性能: 需要优化")
    
    # 退出应用
    QTimer.singleShot(1000, app.quit)
    app.exec()

if __name__ == "__main__":
    test_controller_performance()
