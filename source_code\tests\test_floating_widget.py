"""
测试悬浮小组件功能
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.config import Config
from core.logger import setup_logging
from core.database import DatabaseManager

# 导入悬浮小组件
from modules.study_tracker.study_floating_widget import StudyFloatingWidget


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 初始化配置和日志
    config = Config()
    setup_logging(config)
    
    # 初始化数据库
    db_manager = DatabaseManager(config)
    db_manager.init_database()
    
    # 创建悬浮小组件
    floating_widget = StudyFloatingWidget()
    floating_widget.show()
    
    # 连接信号（简单的测试处理）
    def on_show_main_window():
        print("显示主窗口信号触发")
    
    def on_start_study():
        print("开始学习信号触发")
    
    def on_stop_study():
        print("停止学习信号触发")
    
    floating_widget.show_main_window.connect(on_show_main_window)
    floating_widget.start_study.connect(on_start_study)
    floating_widget.stop_study.connect(on_stop_study)
    
    print("悬浮小组件已启动，可以测试其功能")
    print("- 拖拽移动窗口")
    print("- 点击开始/停止按钮")
    print("- 点击主界面按钮")
    print("- 点击折叠/展开按钮")
    print("- 右键系统托盘图标")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
