"""
学习统计图表组件

提供学习数据的可视化展示，包括：
- 日/周/月学习统计
- 学习趋势图表
- 科目分布分析
- 学习效率分析
"""

import sys
from datetime import datetime, date, timedelta
from typing import Dict, List, Any

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QComboBox, QDateEdit, QTabWidget,
    QGroupBox, QScrollArea, QFrame, QSizePolicy
)
from PyQt6.QtCore import Qt, QDate, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor

# 尝试导入matplotlib，如果失败则使用简单的文本显示
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.dates as mdates
    from matplotlib import rcParams
    
    # 设置中文字体
    rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    rcParams['axes.unicode_minus'] = False
    
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("Warning: matplotlib not available, using simple text display for charts")

from core.logger import LoggerMixin
from .study_service import StudyService


class SimpleStatsWidget(QWidget):
    """简单的统计显示组件（当matplotlib不可用时）"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        
        self.stats_label = QLabel("统计数据将在这里显示")
        self.stats_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.stats_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 20px;
                font-size: 14px;
            }
        """)
        
        layout.addWidget(self.stats_label)
    
    def update_stats(self, stats_data: Dict[str, Any]):
        """更新统计显示"""
        if not stats_data:
            self.stats_label.setText("暂无统计数据")
            return
        
        text_lines = []
        text_lines.append("=== 学习统计 ===")
        text_lines.append("")
        
        if 'total_minutes' in stats_data:
            hours = stats_data['total_minutes'] // 60
            minutes = stats_data['total_minutes'] % 60
            text_lines.append(f"总学习时间: {hours}小时{minutes}分钟")
        
        if 'total_sessions' in stats_data:
            text_lines.append(f"学习次数: {stats_data['total_sessions']}次")
        
        if 'average_session_minutes' in stats_data:
            text_lines.append(f"平均每次: {stats_data['average_session_minutes']:.1f}分钟")
        
        if 'subject_stats' in stats_data:
            text_lines.append("")
            text_lines.append("=== 科目分布 ===")
            for subject, data in stats_data['subject_stats'].items():
                text_lines.append(f"{subject}: {data['minutes']}分钟 ({data['sessions']}次)")
        
        self.stats_label.setText("\n".join(text_lines))


if MATPLOTLIB_AVAILABLE:
    class ChartWidget(FigureCanvas):
        """图表组件基类"""
        
        def __init__(self, parent=None, width=5, height=4, dpi=100):
            self.figure = Figure(figsize=(width, height), dpi=dpi)
            super().__init__(self.figure)
            self.setParent(parent)
            
            # 设置图表样式
            self.figure.patch.set_facecolor('white')
            
        def clear_chart(self):
            """清空图表"""
            self.figure.clear()
            self.draw()
    
    
    class DailyTrendChart(ChartWidget):
        """日学习趋势图表"""
        
        def update_chart(self, daily_data: List[Dict]):
            """更新日趋势图表"""
            self.figure.clear()
            
            if not daily_data:
                ax = self.figure.add_subplot(111)
                ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
                self.draw()
                return
            
            ax = self.figure.add_subplot(111)
            
            dates = [datetime.strptime(item['date'], '%Y-%m-%d') for item in daily_data]
            minutes = [item['minutes'] for item in daily_data]
            
            ax.plot(dates, minutes, marker='o', linewidth=2, markersize=6)
            ax.set_title('每日学习时长趋势', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期')
            ax.set_ylabel('学习时长 (分钟)')
            
            # 格式化x轴日期
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=1))
            
            # 旋转日期标签
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            ax.grid(True, alpha=0.3)
            self.figure.tight_layout()
            self.draw()
    
    
    class SubjectPieChart(ChartWidget):
        """科目分布饼图"""
        
        def update_chart(self, subject_data: Dict[str, Dict]):
            """更新科目分布图表"""
            self.figure.clear()
            
            if not subject_data:
                ax = self.figure.add_subplot(111)
                ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
                self.draw()
                return
            
            ax = self.figure.add_subplot(111)
            
            subjects = list(subject_data.keys())
            minutes = [data['minutes'] for data in subject_data.values()]
            colors = [data.get('color', '#3498db') for data in subject_data.values()]
            
            # 只显示有学习时间的科目
            filtered_data = [(s, m, c) for s, m, c in zip(subjects, minutes, colors) if m > 0]
            
            if not filtered_data:
                ax.text(0.5, 0.5, '暂无学习数据', ha='center', va='center', transform=ax.transAxes)
                self.draw()
                return
            
            subjects, minutes, colors = zip(*filtered_data)
            
            wedges, texts, autotexts = ax.pie(
                minutes, labels=subjects, colors=colors, autopct='%1.1f%%',
                startangle=90, textprops={'fontsize': 10}
            )
            
            ax.set_title('科目学习时间分布', fontsize=14, fontweight='bold')
            
            # 设置百分比文字颜色
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
            self.figure.tight_layout()
            self.draw()


def qdate_to_python_date(qdate):
    """将QDate转换为Python date对象"""
    if hasattr(qdate, 'toPython'):
        return qdate.toPython()
    else:
        return date(qdate.year(), qdate.month(), qdate.day())


class StudyStatsWidget(QWidget, LoggerMixin):
    """学习统计分析组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.study_service = StudyService()
        
        self.init_ui()
        self.load_current_stats()

        # 初始化活动时长显示
        self._init_activity_display()

        # 定时更新统计数据
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_stats)
        self.update_timer.start(60000)  # 每分钟更新一次
        
        self.logger.info("Study stats widget initialized")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 统计标签页
        self.tab_widget = QTabWidget()
        
        # 今日统计
        self.today_tab = self.create_today_tab()
        self.tab_widget.addTab(self.today_tab, "今日统计")
        
        # 周统计
        self.week_tab = self.create_week_tab()
        self.tab_widget.addTab(self.week_tab, "本周统计")
        
        # 月统计
        self.month_tab = self.create_month_tab()
        self.tab_widget.addTab(self.month_tab, "本月统计")
        
        # 趋势分析
        self.trend_tab = self.create_trend_tab()
        self.tab_widget.addTab(self.trend_tab, "趋势分析")
        
        layout.addWidget(self.tab_widget)
    
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QGroupBox("统计控制")
        layout = QHBoxLayout(panel)
        
        # 日期选择
        layout.addWidget(QLabel("统计日期:"))
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.dateChanged.connect(self.on_date_changed)
        layout.addWidget(self.date_edit)
        
        # 科目筛选
        layout.addWidget(QLabel("科目筛选:"))
        self.subject_combo = QComboBox()
        self.subject_combo.addItem("全部科目")
        self.load_subjects()
        layout.addWidget(self.subject_combo)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新统计")
        self.refresh_btn.clicked.connect(self.refresh_stats)
        layout.addWidget(self.refresh_btn)
        
        layout.addStretch()
        return panel
    
    def create_today_tab(self) -> QWidget:
        """创建今日统计标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 今日概览
        overview_group = QGroupBox("今日概览")
        overview_layout = QGridLayout(overview_group)
        
        self.today_time_label = QLabel("学习时间: 0分钟")
        self.today_sessions_label = QLabel("学习次数: 0次")
        self.today_avg_label = QLabel("平均时长: 0分钟")
        self.today_efficiency_label = QLabel("学习效率: 0%")
        
        overview_layout.addWidget(self.today_time_label, 0, 0)
        overview_layout.addWidget(self.today_sessions_label, 0, 1)
        overview_layout.addWidget(self.today_avg_label, 1, 0)
        overview_layout.addWidget(self.today_efficiency_label, 1, 1)
        
        layout.addWidget(overview_group)
        
        # 科目分布
        if MATPLOTLIB_AVAILABLE:
            self.today_pie_chart = SubjectPieChart()
            layout.addWidget(self.today_pie_chart)
        else:
            self.today_simple_stats = SimpleStatsWidget()
            layout.addWidget(self.today_simple_stats)

        # 24小时学习时间分布
        hourly_group = QGroupBox("24小时学习时间分布")
        hourly_layout = QVBoxLayout(hourly_group)
        hourly_layout.setSpacing(2)  # 进一步减少组件间距
        hourly_layout.setContentsMargins(10, 10, 10, 10)  # 减少容器边距

        # 活动时长显示区域
        activity_widget = QWidget()
        activity_widget.setMaximumHeight(80)  # 限制活动时长区域的最大高度
        activity_layout = QVBoxLayout(activity_widget)
        activity_layout.setContentsMargins(20, 5, 20, 2)  # 进一步减少边距

        # 标题
        activity_title = QLabel("活动时长")
        activity_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #666; margin-bottom: 5px;")
        activity_layout.addWidget(activity_title)

        # 时长统计区域
        stats_layout = QHBoxLayout()

        # 学习活动时长（蓝色）
        study_widget = QWidget()
        study_layout = QHBoxLayout(study_widget)
        study_layout.setContentsMargins(0, 0, 0, 0)

        study_color = QLabel()
        study_color.setFixedSize(20, 20)
        study_color.setStyleSheet("background-color: #007AFF; border-radius: 4px;")

        study_info = QWidget()
        study_info_layout = QVBoxLayout(study_info)
        study_info_layout.setContentsMargins(10, 0, 0, 0)
        study_info_layout.setSpacing(2)

        self.study_label = QLabel("学习活动时长")
        self.study_label.setStyleSheet("font-size: 12px; color: #666;")
        self.study_time = QLabel("0分钟")
        self.study_time.setStyleSheet("font-size: 18px; font-weight: bold; color: #333;")

        study_info_layout.addWidget(self.study_label)
        study_info_layout.addWidget(self.study_time)

        study_layout.addWidget(study_color)
        study_layout.addWidget(study_info)
        study_layout.addStretch()

        # 休息时长（浅蓝色）
        rest_widget = QWidget()
        rest_layout = QHBoxLayout(rest_widget)
        rest_layout.setContentsMargins(0, 0, 0, 0)

        rest_color = QLabel()
        rest_color.setFixedSize(20, 20)
        rest_color.setStyleSheet("background-color: #B3D9FF; border-radius: 4px;")

        rest_info = QWidget()
        rest_info_layout = QVBoxLayout(rest_info)
        rest_info_layout.setContentsMargins(10, 0, 0, 0)
        rest_info_layout.setSpacing(2)

        self.rest_label = QLabel("休息时长")
        self.rest_label.setStyleSheet("font-size: 12px; color: #666;")
        self.rest_time = QLabel("0分钟")
        self.rest_time.setStyleSheet("font-size: 18px; font-weight: bold; color: #333;")

        rest_info_layout.addWidget(self.rest_label)
        rest_info_layout.addWidget(self.rest_time)

        rest_layout.addWidget(rest_color)
        rest_layout.addWidget(rest_info)
        rest_layout.addStretch()

        stats_layout.addWidget(study_widget)
        stats_layout.addWidget(rest_widget)
        stats_layout.addStretch()

        activity_layout.addWidget(activity_title)
        activity_layout.addLayout(stats_layout)

        hourly_layout.addWidget(activity_widget)

        self.today_hourly_chart = HourlyBarChart()
        # 给图表更高的拉伸权重，让它占据更多垂直空间
        hourly_layout.addWidget(self.today_hourly_chart, 1)

        layout.addWidget(hourly_group)

        return tab
    
    def create_week_tab(self) -> QWidget:
        """创建周统计标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 本周概览
        overview_group = QGroupBox("本周概览")
        overview_layout = QGridLayout(overview_group)
        
        self.week_time_label = QLabel("学习时间: 0分钟")
        self.week_sessions_label = QLabel("学习次数: 0次")
        self.week_avg_daily_label = QLabel("日均时长: 0分钟")
        self.week_best_day_label = QLabel("最佳日期: 无")
        
        overview_layout.addWidget(self.week_time_label, 0, 0)
        overview_layout.addWidget(self.week_sessions_label, 0, 1)
        overview_layout.addWidget(self.week_avg_daily_label, 1, 0)
        overview_layout.addWidget(self.week_best_day_label, 1, 1)
        
        layout.addWidget(overview_group)
        
        # 每日趋势
        if MATPLOTLIB_AVAILABLE:
            self.week_trend_chart = DailyTrendChart()
            layout.addWidget(self.week_trend_chart)
        else:
            self.week_simple_stats = SimpleStatsWidget()
            layout.addWidget(self.week_simple_stats)
        
        return tab
    
    def create_month_tab(self) -> QWidget:
        """创建月统计标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 本月概览
        overview_group = QGroupBox("本月概览")
        overview_layout = QGridLayout(overview_group)
        
        self.month_time_label = QLabel("学习时间: 0分钟")
        self.month_sessions_label = QLabel("学习次数: 0次")
        self.month_avg_daily_label = QLabel("日均时长: 0分钟")
        self.month_progress_label = QLabel("月度进度: 0%")
        
        overview_layout.addWidget(self.month_time_label, 0, 0)
        overview_layout.addWidget(self.month_sessions_label, 0, 1)
        overview_layout.addWidget(self.month_avg_daily_label, 1, 0)
        overview_layout.addWidget(self.month_progress_label, 1, 1)
        
        layout.addWidget(overview_group)
        
        # 月度统计图表
        if MATPLOTLIB_AVAILABLE:
            self.month_chart = ChartWidget()
            layout.addWidget(self.month_chart)
        else:
            self.month_simple_stats = SimpleStatsWidget()
            layout.addWidget(self.month_simple_stats)
        
        return tab
    
    def create_trend_tab(self) -> QWidget:
        """创建趋势分析标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 趋势分析
        trend_group = QGroupBox("学习趋势分析")
        trend_layout = QVBoxLayout(trend_group)
        
        self.trend_summary_label = QLabel("趋势分析将在这里显示")
        self.trend_summary_label.setWordWrap(True)
        trend_layout.addWidget(self.trend_summary_label)
        
        layout.addWidget(trend_group)
        
        # 长期趋势图表
        if MATPLOTLIB_AVAILABLE:
            self.long_trend_chart = DailyTrendChart()
            layout.addWidget(self.long_trend_chart)
        else:
            self.trend_simple_stats = SimpleStatsWidget()
            layout.addWidget(self.trend_simple_stats)
        
        return tab
    
    # ==================== 数据加载和更新 ====================
    
    def load_subjects(self):
        """加载学习科目"""
        subjects = self.study_service.get_subjects()
        self.subject_combo.clear()
        self.subject_combo.addItem("全部科目")
        for subject in subjects:
            # 现在subject是字典格式
            self.subject_combo.addItem(subject['name'])
    
    def load_current_stats(self):
        """加载当前统计数据"""
        self.refresh_stats()
    
    def refresh_stats(self):
        """刷新统计数据"""
        try:
            # 获取选择的日期
            selected_date = qdate_to_python_date(self.date_edit.date())
            
            # 更新今日统计
            self.update_today_stats(selected_date)
            
            # 更新周统计
            self.update_week_stats(selected_date)
            
            # 更新月统计
            self.update_month_stats(selected_date)
            
            # 更新趋势分析
            self.update_trend_analysis(selected_date)
            
            self.logger.info("Stats refreshed successfully")
            
        except Exception as e:
            self.logger.error(f"Error refreshing stats: {e}")
    
    def update_today_stats(self, target_date: date):
        """更新今日统计"""
        stats = self.study_service.get_daily_statistics(target_date)
        
        # 更新概览标签
        total_minutes = stats.get('total_minutes', 0)
        total_sessions = stats.get('total_sessions', 0)
        avg_minutes = stats.get('average_session_minutes', 0)
        
        self.today_time_label.setText(f"学习时间: {total_minutes}分钟")
        self.today_sessions_label.setText(f"学习次数: {total_sessions}次")
        self.today_avg_label.setText(f"平均时长: {avg_minutes:.1f}分钟")
        
        # 计算效率（简单示例：基于专注度评分）
        efficiency = 0
        if stats.get('sessions'):
            focus_ratings = [s.focus_rating for s in stats['sessions'] if s.focus_rating]
            if focus_ratings:
                efficiency = sum(focus_ratings) / len(focus_ratings) * 20  # 转换为百分比
        
        self.today_efficiency_label.setText(f"学习效率: {efficiency:.0f}%")
        
        # 更新图表
        subject_stats = stats.get('subject_stats', {})
        if MATPLOTLIB_AVAILABLE:
            self.today_pie_chart.update_chart(subject_stats)
        else:
            self.today_simple_stats.update_stats(stats)

        # 更新24小时条形图
        hourly_data = self.study_service.get_hourly_statistics(target_date)
        self.today_hourly_chart.update_chart(hourly_data)
    
    def update_week_stats(self, target_date: date):
        """更新周统计"""
        stats = self.study_service.get_weekly_statistics(target_date)
        
        total_minutes = stats.get('total_minutes', 0)
        total_sessions = stats.get('total_sessions', 0)
        avg_daily = stats.get('average_daily_minutes', 0)
        
        self.week_time_label.setText(f"学习时间: {total_minutes}分钟")
        self.week_sessions_label.setText(f"学习次数: {total_sessions}次")
        self.week_avg_daily_label.setText(f"日均时长: {avg_daily:.1f}分钟")
        
        # 找出最佳学习日
        daily_stats = stats.get('daily_stats', {})
        if daily_stats:
            best_day = max(daily_stats.items(), key=lambda x: x[1]['minutes'])
            best_date = datetime.strptime(best_day[0], '%Y-%m-%d').strftime('%m-%d')
            self.week_best_day_label.setText(f"最佳日期: {best_date} ({best_day[1]['minutes']}分钟)")
        
        # 更新趋势图表
        if MATPLOTLIB_AVAILABLE:
            daily_data = [
                {'date': date_str, 'minutes': data['minutes']}
                for date_str, data in daily_stats.items()
            ]
            self.week_trend_chart.update_chart(daily_data)
        else:
            self.week_simple_stats.update_stats(stats)
    
    def update_month_stats(self, target_date: date):
        """更新月统计"""
        stats = self.study_service.get_monthly_statistics(target_date.year, target_date.month)
        
        total_minutes = stats.get('total_minutes', 0)
        total_sessions = stats.get('total_sessions', 0)
        avg_daily = stats.get('average_daily_minutes', 0)
        
        self.month_time_label.setText(f"学习时间: {total_minutes}分钟")
        self.month_sessions_label.setText(f"学习次数: {total_sessions}次")
        self.month_avg_daily_label.setText(f"日均时长: {avg_daily:.1f}分钟")
        
        # 计算月度进度（假设目标是每天60分钟）
        target_daily = 60
        days_in_month = (stats.get('end_date', target_date) - stats.get('start_date', target_date)).days + 1
        target_total = target_daily * days_in_month
        progress = min(100, total_minutes / target_total * 100) if target_total > 0 else 0
        
        self.month_progress_label.setText(f"月度进度: {progress:.1f}%")
        
        # 更新图表
        if not MATPLOTLIB_AVAILABLE:
            self.month_simple_stats.update_stats(stats)
    
    def update_trend_analysis(self, target_date: date):
        """更新趋势分析"""
        # 获取过去30天的数据进行趋势分析
        end_date = target_date
        start_date = end_date - timedelta(days=29)
        
        # 简单的趋势分析文本
        analysis_text = "基于过去30天的学习数据分析：\n\n"
        analysis_text += "• 学习习惯正在养成中\n"
        analysis_text += "• 建议保持每日学习的连续性\n"
        analysis_text += "• 可以尝试增加学习时长或频次"
        
        self.trend_summary_label.setText(analysis_text)
        
        # 更新长期趋势图表
        if not MATPLOTLIB_AVAILABLE:
            self.trend_simple_stats.update_stats({'trend': '趋势分析数据'})
    
    # ==================== 事件处理 ====================
    
    def on_date_changed(self, date):
        """日期改变事件"""
        self.refresh_stats()

    def _update_activity_display(self, study_minutes, rest_minutes, hour):
        """更新活动时长显示"""
        try:
            # 格式化学习时间
            if study_minutes == 0:
                study_text = "0分钟"
            elif study_minutes < 60:
                study_text = f"{study_minutes}分钟"
            else:
                hours = study_minutes // 60
                mins = study_minutes % 60
                if mins == 0:
                    study_text = f"{hours}小时"
                else:
                    study_text = f"{hours}小时{mins}分钟"

            # 格式化休息时间
            if rest_minutes == 0:
                rest_text = "0分钟"
            elif rest_minutes < 60:
                rest_text = f"{rest_minutes}分钟"
            else:
                hours = rest_minutes // 60
                mins = rest_minutes % 60
                if mins == 0:
                    rest_text = f"{hours}小时"
                else:
                    rest_text = f"{hours}小时{mins}分钟"

            # 更新显示
            self.study_time.setText(study_text)
            self.rest_time.setText(rest_text)

            # 更新标签显示当前选中的时间段
            from datetime import datetime
            current_hour = datetime.now().hour
            if hour == current_hour:
                time_label = f"现在 ({hour:02d}:00-{(hour+1)%24:02d}:00)"
            else:
                time_label = f"{hour:02d}:00-{(hour+1)%24:02d}:00"

            self.study_label.setText(f"学习活动时长 ({time_label})")
            self.rest_label.setText(f"休息时长 ({time_label})")

        except Exception as e:
            self.logger.error(f"更新活动时长显示失败: {e}")

    def _init_activity_display(self):
        """初始化活动时长显示"""
        try:
            # 显示今日总体统计
            from datetime import date
            today = date.today()
            hourly_data = self.study_service.get_hourly_statistics(today)

            total_study_minutes = sum(hourly_data.values())
            total_rest_minutes = 0  # 可以根据需要计算实际休息时间

            # 格式化显示
            if total_study_minutes == 0:
                study_text = "0分钟"
            elif total_study_minutes < 60:
                study_text = f"{total_study_minutes}分钟"
            else:
                hours = total_study_minutes // 60
                mins = total_study_minutes % 60
                if mins == 0:
                    study_text = f"{hours}小时"
                else:
                    study_text = f"{hours}小时{mins}分钟"

            self.study_time.setText(study_text)
            self.rest_time.setText("0分钟")

            # 重置标签
            self.study_label.setText("学习活动时长")
            self.rest_label.setText("休息时长")

        except Exception as e:
            self.logger.error(f"初始化活动时长显示失败: {e}")
            self.study_time.setText("0分钟")
            self.rest_time.setText("0分钟")


class HourlyBarChart(QWidget, LoggerMixin):
    """24小时学习时间条形图"""

    def __init__(self, parent=None):
        super().__init__(parent)

        if MATPLOTLIB_AVAILABLE:
            self.init_matplotlib_chart()
        else:
            self.init_simple_chart()

    def init_matplotlib_chart(self):
        """初始化matplotlib图表"""
        # 大幅增加图表高度，显著提升垂直空间利用率
        self.figure = Figure(figsize=(14, 7.5), dpi=100)
        self.canvas = FigureCanvas(self.figure)

        # 大幅增加最小高度，确保图表占据足够空间
        self.canvas.setMinimumSize(900, 600)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)  # 减少组件间距

        # 设置图表的拉伸策略，让它占据更多空间
        from PyQt6.QtWidgets import QSizePolicy
        self.canvas.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        layout.addWidget(self.canvas)

        # 设置图表样式
        self.figure.patch.set_facecolor('white')

        # 存储当前数据，用于交互
        self.current_hourly_data = {}

        # 连接鼠标点击事件
        self.canvas.mpl_connect('button_press_event', self.on_chart_click)

        # 初始化时绘制空图表
        self._draw_empty_chart()

    def init_simple_chart(self):
        """初始化简单文本图表"""
        layout = QVBoxLayout(self)

        self.chart_label = QLabel("24小时学习时间分布")
        self.chart_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.chart_label.setFont(QFont('Microsoft YaHei', 12, QFont.Weight.Bold))

        self.data_label = QLabel("暂无数据")
        self.data_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.data_label.setWordWrap(True)

        layout.addWidget(self.chart_label)
        layout.addWidget(self.data_label)

    def update_chart(self, hourly_data: Dict[int, int]):
        """
        更新图表数据

        Args:
            hourly_data: 小时数据，格式为 {hour: minutes}
                        hour: 0-23的小时数
                        minutes: 该小时内的学习分钟数
        """
        # 保存当前数据用于交互
        self.current_hourly_data = hourly_data.copy()

        if MATPLOTLIB_AVAILABLE:
            self._update_matplotlib_chart(hourly_data)
        else:
            self._update_simple_chart(hourly_data)

    def _draw_empty_chart(self):
        """绘制空的图表框架"""
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            self.figure.clear()
            ax = self.figure.add_subplot(111)

            # 准备空数据
            hours = list(range(24))

            # 获取当前小时
            from datetime import datetime
            current_hour = datetime.now().hour

            # 创建颜色数组 - 使用渐变蓝色配色
            colors = ['#e0f2fe'] * 24  # 全部使用极浅蓝色
            colors[current_hour] = '#1e3a8a'  # 当前小时使用深蓝色高亮

            # 创建条形图 - 显示很小的条形作为框架
            bars = ax.bar(hours, [2] * 24, color=colors, alpha=0.6, width=0.8)

            # 设置X轴
            ax.set_xlim(-0.5, 23.5)
            key_hours = [15, 17, 19, 21, 23, 1, 3, 5, 7, 9, 11, current_hour]
            key_hours = sorted(list(set(key_hours)))
            ax.set_xticks(key_hours)

            x_labels = []
            for h in key_hours:
                if h == current_hour:
                    x_labels.append('现在')
                else:
                    x_labels.append(str(h))
            ax.set_xticklabels(x_labels, fontsize=10, color='#6b7280')

            # 设置Y轴 - 减少范围让图表更紧凑
            ax.set_ylim(0, 60)
            ax.set_yticks([0, 30, 60])
            ax.set_yticklabels(['0分钟', '30分钟', '60分钟'], fontsize=9, color='#6b7280')

            # 设置样式
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['left'].set_color('#e5e7eb')
            ax.spines['bottom'].set_color('#e5e7eb')

            # 添加网格
            ax.grid(True, axis='y', alpha=0.3, linestyle='-', linewidth=0.5, color='#e5e7eb')
            ax.set_axisbelow(True)

            # 添加"今天"标注
            ax.text(11.5, -10, '今天', ha='center', va='top',
                   fontsize=11, color='#6b7280', fontweight='normal')

            # 移除提示文字 - 保持图表简洁

            # 调整布局 - 适应增加的高度，减少边距让图表更紧凑
            self.figure.subplots_adjust(left=0.06, right=0.98, top=0.95, bottom=0.15)

            # 刷新画布
            self.canvas.draw()

        except Exception as e:
            self.logger.error(f"绘制空图表失败: {e}")

    def _update_matplotlib_chart(self, hourly_data: Dict[int, int]):
        """更新matplotlib图表"""
        try:
            # 如果没有数据，显示空图表
            if not hourly_data or sum(hourly_data.values()) == 0:
                self._draw_empty_chart()
                return

            self.figure.clear()
            ax = self.figure.add_subplot(111)

            # 准备数据
            hours = list(range(24))  # 0-23小时
            minutes = [hourly_data.get(hour, 0) for hour in hours]

            # 获取当前小时
            from datetime import datetime
            current_hour = datetime.now().hour

            # 创建颜色数组 - 参考图表的渐变效果
            colors = []
            for i, hour in enumerate(hours):
                if hour == current_hour:
                    # 当前小时使用特殊颜色（深蓝色）
                    colors.append('#1e3a8a')
                elif minutes[i] > 0:
                    # 有数据的小时使用渐变蓝色
                    intensity = minutes[i] / max(minutes) if max(minutes) > 0 else 0
                    if intensity > 0.7:
                        colors.append('#1e40af')  # 深蓝
                    elif intensity > 0.4:
                        colors.append('#3b82f6')  # 中蓝
                    else:
                        colors.append('#60a5fa')  # 浅蓝
                else:
                    # 无数据的小时使用很浅的蓝色
                    colors.append('#e0f2fe')

            # 创建条形图
            bars = ax.bar(hours, minutes, color=colors, alpha=0.8, width=0.8)

            # 移除标题，让图表更简洁
            # ax.set_title('24小时学习时间分布', fontsize=14, fontweight='bold', pad=20)

            # 设置X轴 - 参考图表的时间点显示
            ax.set_xlim(-0.5, 23.5)
            # 显示关键时间点：每2小时一个刻度
            key_hours = [15, 17, 19, 21, 23, 1, 3, 5, 7, 9, 11, current_hour]
            # 去重并排序
            key_hours = sorted(list(set(key_hours)))
            ax.set_xticks(key_hours)

            # 设置X轴标签
            x_labels = []
            for h in key_hours:
                if h == current_hour:
                    x_labels.append('现在')
                else:
                    x_labels.append(str(h))
            ax.set_xticklabels(x_labels, fontsize=10, color='#6b7280')

            # 设置Y轴 - 参考图表的刻度显示
            max_minutes = max(minutes) if minutes else 60
            # 设置合适的Y轴范围
            y_max = max(60, max_minutes * 1.1)  # 至少显示到60分钟
            ax.set_ylim(0, y_max)

            # 设置Y轴刻度 - 显示0, 30, 60分钟等关键刻度
            y_ticks = []
            if y_max <= 60:
                y_ticks = [0, 30, 60]
            elif y_max <= 120:
                y_ticks = [0, 30, 60, 90, 120]
            else:
                step = int(y_max / 4)
                y_ticks = [i * step for i in range(5)]

            ax.set_yticks(y_ticks)
            ax.set_yticklabels([f'{int(y)}分钟' for y in y_ticks], fontsize=9, color='#6b7280')

            # 移除边框，让图表更简洁
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['left'].set_color('#e5e7eb')
            ax.spines['bottom'].set_color('#e5e7eb')

            # 添加水平网格线
            ax.grid(True, axis='y', alpha=0.3, linestyle='-', linewidth=0.5, color='#e5e7eb')
            ax.set_axisbelow(True)

            # 在底部添加"今天"标注
            ax.text(11.5, -y_max * 0.10, '今天', ha='center', va='top',
                   fontsize=11, color='#6b7280', fontweight='normal')

            # 调整布局 - 适应增加的高度，减少边距让图表更紧凑
            self.figure.subplots_adjust(left=0.06, right=0.98, top=0.95, bottom=0.15)

            # 刷新画布
            self.canvas.draw()

        except Exception as e:
            self.logger.error(f"更新24小时条形图失败: {e}")

    def _update_simple_chart(self, hourly_data: Dict[int, int]):
        """更新简单文本图表"""
        try:
            if not hourly_data:
                self.data_label.setText("今日暂无学习记录")
                return

            # 找出有学习记录的小时
            active_hours = [(hour, minutes) for hour, minutes in hourly_data.items() if minutes > 0]

            if not active_hours:
                self.data_label.setText("今日暂无学习记录")
                return

            # 按学习时间排序
            active_hours.sort(key=lambda x: x[1], reverse=True)

            # 生成文本显示
            text_lines = ["24小时学习时间分布：\n"]

            for hour, minutes in active_hours[:10]:  # 只显示前10个最活跃的小时
                bar_length = int(minutes / max(hourly_data.values()) * 20)  # 最大20个字符的条形
                bar = "█" * bar_length + "░" * (20 - bar_length)
                text_lines.append(f"{hour:02d}:00 {bar} {minutes}分钟")

            if len(active_hours) > 10:
                text_lines.append(f"... 还有{len(active_hours) - 10}个时段")

            self.data_label.setText("\n".join(text_lines))

        except Exception as e:
            self.logger.error(f"更新简单24小时图表失败: {e}")
            self.data_label.setText("数据更新失败")

    def on_chart_click(self, event):
        """处理图表点击事件 - 更新活动时长显示"""
        if not MATPLOTLIB_AVAILABLE or event.inaxes is None:
            return

        try:
            # 获取点击的X坐标（小时）
            clicked_hour = round(event.xdata)

            # 确保小时在有效范围内
            if clicked_hour < 0 or clicked_hour > 23:
                return

            # 获取该小时的学习时间
            study_minutes = self.current_hourly_data.get(clicked_hour, 0)

            # 计算休息时长（假设一小时内非学习时间为休息时间）
            rest_minutes = max(0, 60 - study_minutes) if study_minutes > 0 else 0

            # 通知父组件更新活动时长显示
            parent = self.parent()
            while parent and not hasattr(parent, '_update_activity_display'):
                parent = parent.parent()

            if parent and hasattr(parent, '_update_activity_display'):
                parent._update_activity_display(study_minutes, rest_minutes, clicked_hour)

        except Exception as e:
            self.logger.error(f"处理图表点击事件失败: {e}")


