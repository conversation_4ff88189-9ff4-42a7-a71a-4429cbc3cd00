# Personal Manager System - 项目结构说明

## 📁 项目文件夹结构

```
personal_manager_project/
├── 📋 项目结构说明.md              # 本文件 - 项目结构说明
├── 📋 开发计划.md                  # 详细的开发计划和任务分解
├── 📋 技术方案总结.md              # 完整的技术方案总结
│
├── 📂 source_code/                 # 源代码文件夹
│   ├── 📂 src/                     # 主要源代码
│   │   ├── 📂 core/                # 核心基础设施
│   │   │   ├── config.py          # ✅ 配置管理系统
│   │   │   ├── database.py        # ✅ 数据库管理
│   │   │   └── logger.py          # ✅ 日志系统
│   │   ├── 📂 models/              # 数据模型
│   │   │   └── base.py            # ✅ 基础模型类
│   │   ├── 📂 services/            # 业务逻辑服务
│   │   ├── 📂 modules/             # 功能模块
│   │   │   ├── 📂 file_manager/    # 文件管理器模块
│   │   │   ├── 📂 task_manager/    # 任务管理模块
│   │   │   ├── 📂 system_monitor/  # 系统监控模块
│   │   │   ├── 📂 app_launcher/    # 应用启动器模块
│   │   │   ├── 📂 backup_manager/  # 备份管理模块
│   │   │   ├── 📂 note_manager/    # 笔记管理模块
│   │   │   └── 📂 password_manager/ # 密码管理模块
│   │   ├── 📂 gui/                 # GUI界面
│   │   │   ├── main_window.py     # ✅ 主窗口框架
│   │   │   ├── 📂 widgets/         # 自定义控件
│   │   │   └── 📂 dialogs/         # 对话框
│   │   └── 📂 utils/               # 工具类
│   ├── 📂 resources/               # 资源文件
│   │   ├── 📂 icons/               # 图标文件
│   │   ├── 📂 styles/              # 样式文件
│   │   └── 📂 translations/        # 国际化文件
│   ├── 📂 tests/                   # 测试代码
│   ├── 📄 main.py                  # ✅ 程序入口
│   ├── 📄 requirements.txt         # ✅ 依赖管理
│   └── 📄 setup.py                 # ✅ 安装脚本
│
├── 📂 config_templates/            # 配置文件模板
│   ├── 📂 config/                  
│   │   └── default_config.yaml    # ✅ 默认配置文件
│   └── .env.example               # ✅ 环境变量示例
│
└── 📂 docs/                        # 文档文件夹
    ├── README.md                  # ✅ 项目说明文档
    └── 📂 technical_docs/          # 技术文档
```

## 🎯 当前开发状态

### ✅ 已完成 (阶段1 - 基础框架)
1. **项目结构搭建** - 完整的目录结构和文件组织
2. **配置管理系统** - 支持YAML配置、环境变量、用户自定义配置
3. **日志系统** - 彩色控制台输出、文件轮转、性能监控
4. **数据库管理** - SQLAlchemy ORM、连接池、备份恢复
5. **基础数据模型** - 时间戳、UUID、软删除等Mixin
6. **主窗口框架** - PyQt6主界面、菜单栏、工具栏、导航

### 🔄 进行中
- 配置管理系统的完善和测试

### 📋 待开发 (按优先级排序)
1. **日志系统实现** - 完善日志配置和测试
2. **数据库模型设计** - 各模块的具体数据模型
3. **基础工具类** - 常用工具函数和辅助类
4. **文件管理器模块** - 核心功能实现
5. **系统监控模块** - 实时监控功能
6. **任务管理模块** - 任务调度和管理
7. **其他高级模块** - 应用启动器、备份管理等

## 🚀 快速开始

### 1. 环境准备
```bash
cd personal_manager_project/source_code
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

### 2. 配置设置
```bash
# 复制配置文件
copy ..\config_templates\.env.example .env
copy ..\config_templates\config\default_config.yaml config\
```

### 3. 运行程序
```bash
python main.py
```

## 📝 开发注意事项

1. **代码规范** - 遵循PEP 8，使用black格式化
2. **测试驱动** - 为每个模块编写单元测试
3. **文档更新** - 及时更新技术文档和API文档
4. **版本控制** - 使用Git进行版本管理
5. **安全考虑** - 敏感信息使用环境变量

## 🔧 技术栈

- **GUI**: PyQt6
- **数据库**: SQLite + SQLAlchemy
- **任务调度**: APScheduler
- **系统监控**: psutil
- **文件监控**: watchdog
- **加密**: cryptography
- **打包**: PyInstaller

---

**更新时间**: 2025-07-05
**当前版本**: v1.0.0-alpha
**开发状态**: 阶段1 - 基础框架搭建中