# Personal Manager System - Default Configuration

# Application Settings
app:
  name: "Personal Manager"
  version: "1.0.0"
  debug: false
  auto_save_interval: 300  # seconds
  language: "zh_CN"
  theme: "default"

# Database Configuration
database:
  # SQLite database file path (relative to config directory)
  url: "sqlite:///personal_manager.db"
  echo: false  # Set to true for SQL debugging
  pool_size: 5
  max_overflow: 10
  backup_enabled: true
  backup_interval: 86400  # 24 hours in seconds

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/app.log"
  max_size: 10485760  # 10MB
  backup_count: 5
  console_output: true
  file_output: true

# GUI Configuration
gui:
  theme: "default"  # default, dark, light
  window_size: [1200, 800]
  window_position: [100, 100]
  remember_geometry: true
  show_splash_screen: true
  animation_enabled: true
  font_family: "Microsoft YaHei"
  font_size: 9

# File Manager Module
file_manager:
  default_path: "~"  # User home directory
  show_hidden: false
  auto_refresh: true
  refresh_interval: 5  # seconds
  thumbnail_size: 128
  max_recent_files: 10
  enable_preview: true
  supported_formats:
    - "txt"
    - "pdf"
    - "docx"
    - "xlsx"
    - "png"
    - "jpg"
    - "jpeg"
    - "gif"

# System Monitor Module
system_monitor:
  update_interval: 2  # seconds
  history_length: 100
  alerts_enabled: true
  cpu_alert_threshold: 80  # percentage
  memory_alert_threshold: 85  # percentage
  disk_alert_threshold: 90  # percentage
  temperature_alert_threshold: 70  # celsius
  show_notifications: true

# Task Manager Module
task_manager:
  auto_start_scheduler: true
  max_concurrent_tasks: 5
  task_timeout: 3600  # seconds
  enable_notifications: true
  notification_sound: true
  default_priority: "normal"  # low, normal, high, urgent
  backup_tasks: true

# Application Launcher Module
app_launcher:
  auto_scan_programs: true
  scan_paths:
    - "C:\\Program Files"
    - "C:\\Program Files (x86)"
    - "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs"
  max_recent_apps: 15
  enable_search: true
  search_delay: 300  # milliseconds

# Backup Manager Module
backup:
  default_location: "backups"
  compression: true
  compression_level: 6  # 1-9, higher = better compression
  encryption: false
  encryption_algorithm: "AES-256"
  schedule: "daily"  # daily, weekly, monthly, custom
  schedule_time: "02:00"  # 24-hour format
  retention_days: 30
  verify_backups: true

# Note Manager Module
note_manager:
  default_format: "markdown"  # markdown, html, plain
  auto_save: true
  auto_save_interval: 30  # seconds
  enable_sync: false
  sync_provider: "none"  # none, dropbox, onedrive, google_drive
  max_note_size: 10485760  # 10MB
  enable_tags: true
  enable_categories: true

# Password Manager Module
password_manager:
  master_password_required: true
  password_length: 12
  require_uppercase: true
  require_lowercase: true
  require_numbers: true
  require_special_chars: true
  session_timeout: 3600  # seconds
  auto_lock: true
  auto_lock_delay: 300  # seconds
  backup_vault: true
  export_format: "json"  # json, csv, xml

# Security Settings
security:
  enable_encryption: true
  encryption_algorithm: "AES-256-GCM"
  key_derivation: "PBKDF2"
  key_iterations: 100000
  session_timeout: 3600  # seconds
  max_login_attempts: 3
  lockout_duration: 300  # seconds

# Network Settings
network:
  enable_updates: true
  update_check_interval: 86400  # 24 hours
  update_server: "https://api.example.com/updates"
  proxy_enabled: false
  proxy_host: ""
  proxy_port: 8080
  proxy_username: ""
  proxy_password: ""

# Performance Settings
performance:
  max_memory_usage: 512  # MB
  enable_caching: true
  cache_size: 100  # MB
  background_processing: true
  max_background_threads: 4
  enable_gpu_acceleration: false

# Notification Settings
notifications:
  enabled: true
  sound_enabled: true
  sound_file: "resources/sounds/notification.wav"
  show_desktop_notifications: true
  notification_duration: 5000  # milliseconds
  priority_levels:
    - "low"
    - "normal"
    - "high"
    - "urgent"

# Plugin Settings
plugins:
  enabled: true
  auto_load: true
  plugin_directory: "plugins"
  allowed_plugins: []
  blocked_plugins: []

# Advanced Settings
advanced:
  debug_mode: false
  verbose_logging: false
  crash_reporting: true
  telemetry_enabled: false
  experimental_features: false
  developer_mode: false
