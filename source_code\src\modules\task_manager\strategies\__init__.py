"""
Task Manager Strategies Package

This package contains strategy pattern implementations for task manager
themes and views, allowing flexible customization of the task management interface.
"""

from .theme_strategy import ThemeStrategy, DefaultTheme, LakeBlueTheme, DarkTheme
from .view_strategy import ViewStrategy, WeeklyView, DailyView, ListView

__all__ = [
    'ThemeStrategy', 'DefaultTheme', 'LakeBlueTheme', 'DarkTheme',
    'ViewStrategy', 'WeeklyView', 'DailyView', 'ListView'
]