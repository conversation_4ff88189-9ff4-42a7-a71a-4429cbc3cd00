"""
测试全局快捷键功能
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import QTimer

# 导入全局快捷键管理器
from core.global_hotkey import get_global_hotkey_manager


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("全局快捷键测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("等待快捷键触发...")
        layout.addWidget(self.status_label)
        
        # 信息标签
        info_label = QLabel("""
测试说明：
1. 按 Ctrl+Shift+S 触发学习追踪快捷键
2. 按 Ctrl+Shift+T 触发测试快捷键
3. 观察状态变化
        """)
        layout.addWidget(info_label)
        
        # 测试按钮
        test_btn = QPushButton("测试本地快捷键")
        test_btn.clicked.connect(self.test_local_hotkey)
        layout.addWidget(test_btn)
        
        # 清理按钮
        cleanup_btn = QPushButton("清理所有快捷键")
        cleanup_btn.clicked.connect(self.cleanup_hotkeys)
        layout.addWidget(cleanup_btn)
        
        # 获取全局快捷键管理器
        self.hotkey_manager = get_global_hotkey_manager()
        
        # 设置快捷键
        self.setup_hotkeys()
    
    def setup_hotkeys(self):
        """设置测试快捷键"""
        if not self.hotkey_manager.is_supported():
            self.status_label.setText("当前平台不支持全局快捷键")
            return
        
        # 注册测试快捷键
        success1 = self.hotkey_manager.register_hotkey(
            "Ctrl+Shift+S",
            self.on_study_hotkey,
            "学习追踪快捷键测试"
        )
        
        success2 = self.hotkey_manager.register_hotkey(
            "Ctrl+Shift+T",
            self.on_test_hotkey,
            "通用测试快捷键"
        )
        
        if success1 and success2:
            self.status_label.setText("快捷键注册成功！请尝试按 Ctrl+Shift+S 或 Ctrl+Shift+T")
        else:
            self.status_label.setText("快捷键注册失败，可能已被其他程序占用")
        
        # 连接信号
        self.hotkey_manager.hotkey_triggered.connect(self.on_hotkey_triggered)
    
    def on_study_hotkey(self):
        """学习追踪快捷键回调"""
        self.status_label.setText("学习追踪快捷键被触发！(Ctrl+Shift+S)")
        print("学习追踪快捷键触发")
        
        # 3秒后恢复状态
        QTimer.singleShot(3000, lambda: self.status_label.setText("等待快捷键触发..."))
    
    def on_test_hotkey(self):
        """测试快捷键回调"""
        self.status_label.setText("测试快捷键被触发！(Ctrl+Shift+T)")
        print("测试快捷键触发")
        
        # 3秒后恢复状态
        QTimer.singleShot(3000, lambda: self.status_label.setText("等待快捷键触发..."))
    
    def on_hotkey_triggered(self, key_combination):
        """快捷键触发信号处理"""
        print(f"快捷键信号触发: {key_combination}")
    
    def test_local_hotkey(self):
        """测试本地快捷键功能"""
        self.status_label.setText("本地快捷键测试 - 这不是全局快捷键")
        QTimer.singleShot(2000, lambda: self.status_label.setText("等待快捷键触发..."))
    
    def cleanup_hotkeys(self):
        """清理所有快捷键"""
        self.hotkey_manager.unregister_all()
        self.status_label.setText("所有快捷键已清理")
        print("快捷键已清理")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.cleanup_hotkeys()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("全局快捷键测试程序已启动")
    print("支持的快捷键:")
    print("- Ctrl+Shift+S: 学习追踪快捷键")
    print("- Ctrl+Shift+T: 测试快捷键")
    print("请在任何应用程序中尝试这些快捷键组合")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
