"""
真实场景测试：通过控制器启动悬浮小组件并测试关闭状态同步
"""

import sys
from pathlib import Path
import time

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件控制器
from modules.study_tracker.study_floating_widget_controller import StudyFloatingWidgetController


def test_close_sync_realistic():
    """真实场景测试关闭状态同步"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        print("\n🧪 真实场景测试悬浮小组件关闭状态同步")
        print("=" * 50)
        
        # 1. 创建控制器
        print("1. 创建控制器...")
        controller = StudyFloatingWidgetController()
        
        # 记录状态变化
        status_changes = []
        
        def on_status_changed(is_running):
            status_text = "运行中" if is_running else "已停止"
            status_changes.append((time.time(), status_text))
            print(f"   🔄 控制器状态变化: {status_text}")
        
        controller.status_changed.connect(on_status_changed)
        
        # 等待初始状态检查
        time.sleep(3)
        initial_status = controller.is_running()
        print(f"   初始状态: {'运行中' if initial_status else '已停止'}")
        
        # 2. 通过控制器启动悬浮小组件
        print("\n2. 通过控制器启动悬浮小组件...")
        start_success = controller.start_widget()
        if start_success:
            print("   ✅ 启动命令发送成功")
        else:
            print("   ❌ 启动命令发送失败")
            return
        
        # 等待启动完成
        print("   等待悬浮小组件启动...")
        widget_started = False
        for i in range(15):
            time.sleep(1)
            current_status = controller.is_running()
            print(f"   第{i+1}秒: 控制器状态 = {'运行中' if current_status else '已停止'}")
            if current_status:
                print("   ✅ 悬浮小组件已启动")
                widget_started = True
                break
        
        if not widget_started:
            print("   ❌ 悬浮小组件启动超时")
            return
        
        # 3. 等待一段时间让悬浮小组件稳定运行
        print("\n3. 等待悬浮小组件稳定运行...")
        time.sleep(5)
        
        # 4. 手动关闭悬浮小组件（模拟用户点击关闭按钮）
        print("\n4. 模拟用户关闭悬浮小组件...")
        print("   注意：这需要手动在悬浮小组件窗口中点击关闭按钮")
        print("   或者我们可以通过停止控制器来测试...")
        
        # 选择1：通过控制器停止
        print("   通过控制器停止悬浮小组件...")
        stop_success = controller.stop_widget()
        if stop_success:
            print("   ✅ 停止命令发送成功")
        else:
            print("   ❌ 停止命令发送失败")
        
        # 5. 等待控制器检测到关闭状态
        print("\n5. 等待控制器检测到关闭状态...")
        sync_success = False
        for i in range(10):
            time.sleep(1)
            current_status = controller.is_running()
            print(f"   第{i+1}秒: 控制器状态 = {'运行中' if current_status else '已停止'}")
            if not current_status:
                print("   ✅ 控制器已检测到悬浮小组件关闭")
                sync_success = True
                break
        
        # 6. 测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果:")
        
        if sync_success:
            print("✅ 关闭状态同步测试通过")
            print("   - 悬浮小组件通过控制器正常启动")
            print("   - 悬浮小组件通过控制器正常停止")
            print("   - 控制器状态正确同步")
        else:
            print("❌ 关闭状态同步测试失败")
            print("   - 控制器未能检测到关闭状态")
        
        print(f"\n状态变化记录: {len(status_changes)} 次")
        for timestamp, status in status_changes:
            print(f"   {time.strftime('%H:%M:%S', time.localtime(timestamp))}: {status}")
        
        # 7. 额外测试：检查状态文件机制
        print("\n6. 额外测试：手动创建状态文件...")
        status_file = controller.widget_status_file
        
        # 手动创建关闭状态文件
        with open(status_file, 'w', encoding='utf-8') as f:
            from datetime import datetime
            content = f"closed:{datetime.now().isoformat()}"
            f.write(content)
        
        print(f"   ✅ 手动创建状态文件: {status_file}")
        
        # 触发状态检查
        time.sleep(1)
        controller.check_status()
        
        final_status = controller.is_running()
        print(f"   最终状态: {'运行中' if final_status else '已停止'}")
        
        if not final_status:
            print("   ✅ 状态文件机制工作正常")
        else:
            print("   ❌ 状态文件机制未生效")
        
        app.quit()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        app.quit()


if __name__ == "__main__":
    test_close_sync_realistic()
