# 造神计划 - 快速入门指南

## 🚀 5分钟快速上手

### 第一步：启动程序
```bash
# 方法1：直接运行主程序
cd source_code
python main.py

# 方法2：使用启动器（推荐）
python launcher.py
```

### 第二步：界面介绍
启动后您会看到：
- **顶部标题栏**：显示"🚀 造神计划 - 任务管理系统"
- **工具栏**：新建任务、刷新、导出、设置等按钮
- **主要区域**：周任务视图（默认）
- **底部状态栏**：显示任务数量和桌面小组件状态

### 第三步：创建第一个任务
1. 点击 `➕ 新建任务` 按钮
2. 填写任务标题，例如："完成项目报告"
3. 选择优先级：高/中/低
4. 设置截止日期
5. 点击确定保存

### 第四步：管理任务
- **查看任务**：在周视图中查看任务安排
- **编辑任务**：双击任务进行编辑
- **完成任务**：右键任务选择"标记为完成"
- **删除任务**：选中任务按Delete键

### 第五步：启动桌面小组件
1. 点击 `🖥️ 启动桌面小组件` 按钮
2. 桌面上出现任务管理小组件
3. 可以拖拽小组件到合适位置
4. 通过小组件快速查看和管理任务

## 🎨 个性化设置

### 切换主题
在右上角主题下拉框中选择：
- **湖面蓝**：清新蓝色主题（默认）
- **默认**：简洁白色主题  
- **深色**：护眼深色主题

### 切换视图
在右上角视图下拉框中选择：
- **周视图**：显示一周任务安排（默认）
- **日视图**：显示单日详细任务
- **列表视图**：显示所有任务列表

## 📱 桌面小组件使用

### 基本操作
- **显示/隐藏**：双击系统托盘图标
- **移动位置**：拖拽小组件标题栏
- **快速添加任务**：在小组件中直接添加
- **查看任务详情**：点击任务项

### 小组件功能
- 显示今日重要任务
- 快速标记任务完成
- 一键打开主程序
- 实时同步任务状态

## ⚡ 常用快捷操作

### 键盘快捷键
- `Ctrl + N`：新建任务
- `Ctrl + R`：刷新数据
- `Delete`：删除选中任务
- `F5`：刷新界面
- `Esc`：关闭对话框

### 鼠标操作
- **双击任务**：编辑任务
- **右键任务**：显示上下文菜单
- **拖拽任务**：移动任务到不同时间段
- **滚轮**：滚动查看更多任务

## 🔧 常见问题解决

### Q: 程序启动失败怎么办？
A: 
1. 确保Python 3.9+已安装
2. 安装必要依赖：`pip install PyQt6 sqlalchemy psutil`
3. 使用启动器：`python launcher.py`

### Q: 桌面小组件无法显示？
A: 
1. 检查主程序是否正常运行
2. 重新点击"启动桌面小组件"
3. 查看任务管理器是否有相关进程

### Q: 任务数据丢失了？
A: 
1. 检查配置目录：`%APPDATA%\造神计划\`
2. 查看数据库文件是否存在
3. 从备份恢复数据

### Q: 界面显示异常？
A: 
1. 尝试切换不同主题
2. 重启程序
3. 检查显示器分辨率设置

## 📁 重要文件位置

### 程序文件
```
source_code/
├── main.py              # 主程序入口
├── launcher.py          # 启动器
├── uninstaller.py       # 卸载程序
└── src/                 # 源代码目录
```

### 用户数据
```
%APPDATA%\造神计划\
├── config\              # 配置文件
├── data\                # 数据库文件
└── logs\                # 日志文件
```

## 🎯 使用建议

### 任务管理最佳实践
1. **每日规划**：每天开始前规划当日任务
2. **优先级管理**：重要紧急任务优先处理
3. **时间分配**：合理分配任务到不同时间段
4. **定期回顾**：每周回顾任务完成情况

### 提高效率的技巧
1. **使用标签**：为任务添加项目、类型等标签
2. **桌面小组件**：利用小组件随时查看任务
3. **主题切换**：根据时间和心情切换主题
4. **快捷键**：熟练使用快捷键提高操作速度

## 📞 获取帮助

### 文档资源
- **详细手册**：`用户使用手册.md`
- **技术文档**：`项目完成总结.md`
- **测试报告**：`测试报告.md`

### 在线支持
- **问题反馈**：在GitHub提交Issue
- **功能建议**：发送邮件到************************
- **用户交流**：加入QQ群或微信群

---

**🎉 恭喜！您已经掌握了造神计划的基本使用方法！**

现在开始您的高效管理之旅吧！ 🚀

*快速入门指南 v1.0*  
*造神计划开发团队*