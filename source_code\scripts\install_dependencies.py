#!/usr/bin/env python3
"""
Personal Manager System - Dependency Installation Script

This script installs the required dependencies for the Personal Manager System.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed")
        print(f"Error: {e.stderr.strip()}")
        return False

def install_core_dependencies():
    """Install core dependencies required for the service layer"""
    dependencies = [
        "sqlalchemy>=2.0.0",
        "pyyaml>=6.0.0",
        "psutil>=5.9.0",
        "send2trash>=1.8.0",
        "apscheduler>=3.10.0",
        "cryptography>=41.0.0",
        "keyring>=24.0.0",
        "requests>=2.31.0",
        "watchdog>=3.0.0"
    ]
    
    print("Installing core dependencies...")
    for dep in dependencies:
        success = run_command(f"pip install {dep}", f"Installing {dep}")
        if not success:
            print(f"Warning: Failed to install {dep}")
    
    return True

def install_gui_dependencies():
    """Install GUI dependencies (optional)"""
    gui_dependencies = [
        "PyQt6>=6.6.0",
        "PyQt6-Qt6>=6.6.0"
    ]
    
    print("\nInstalling GUI dependencies (optional)...")
    for dep in gui_dependencies:
        success = run_command(f"pip install {dep}", f"Installing {dep}")
        if not success:
            print(f"Warning: Failed to install {dep}. GUI functionality will not be available.")
    
    return True

def install_development_dependencies():
    """Install development dependencies (optional)"""
    dev_dependencies = [
        "pytest>=7.4.0",
        "black>=23.0.0",
        "flake8>=6.0.0"
    ]
    
    print("\nInstalling development dependencies (optional)...")
    for dep in dev_dependencies:
        success = run_command(f"pip install {dep}", f"Installing {dep}")
        if not success:
            print(f"Warning: Failed to install {dep}")
    
    return True

def test_installation():
    """Test if the installation was successful"""
    print("\nTesting installation...")
    
    # Test core imports
    try:
        import sqlalchemy
        import yaml
        print("✓ Core dependencies working")
    except ImportError as e:
        print(f"✗ Core dependency test failed: {e}")
        return False
    
    # Test optional imports
    try:
        import psutil
        print("✓ System monitoring dependencies working")
    except ImportError:
        print("⚠ System monitoring dependencies not available")
    
    try:
        from PyQt6 import QtWidgets
        print("✓ GUI dependencies working")
    except ImportError:
        print("⚠ GUI dependencies not available (service layer only)")
    
    return True

def main():
    """Main installation function"""
    print("Personal Manager System - Dependency Installation")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("✗ Python 3.8 or higher is required")
        return 1
    
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Install dependencies
    install_core_dependencies()
    
    # Ask user about optional dependencies
    try:
        install_gui = input("\nInstall GUI dependencies? (y/n): ").lower().startswith('y')
        if install_gui:
            install_gui_dependencies()
        
        install_dev = input("Install development dependencies? (y/n): ").lower().startswith('y')
        if install_dev:
            install_development_dependencies()
    except KeyboardInterrupt:
        print("\nInstallation cancelled by user")
        return 1
    
    # Test installation
    if test_installation():
        print("\n" + "=" * 60)
        print("🎉 Installation completed successfully!")
        print("\nNext steps:")
        print("1. Run 'python test_services_nogui.py' to test the service layer")
        print("2. Run 'python main.py' to start the full application (if GUI dependencies are installed)")
        print("3. Check the documentation for configuration options")
        return 0
    else:
        print("\n" + "=" * 60)
        print("❌ Installation completed with some issues")
        print("Please check the error messages above and install missing dependencies manually")
        return 1

if __name__ == "__main__":
    sys.exit(main())
