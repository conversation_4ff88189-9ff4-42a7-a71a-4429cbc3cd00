#!/usr/bin/env python3
"""
Debug script to check task dates and current week range
"""

import sys
from pathlib import Path
from datetime import date, datetime, timedelta

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from core.config import ConfigManager
from core.database import init_db_manager
from modules.task_manager.task_service import TaskService

# Import all models to ensure they are registered
from models import *


def debug_task_dates():
    """Debug task dates and week ranges"""
    print("Debugging Task Dates and Week Ranges")
    print("=" * 50)
    
    try:
        # Initialize database
        config_manager = ConfigManager()
        db_manager = init_db_manager(config_manager)
        
        if not db_manager:
            print("Failed to initialize database")
            return False
        
        service = TaskService()
        
        # Get all tasks
        all_tasks = service.get_tasks(limit=1000)
        print(f"Total tasks in database: {len(all_tasks)}")
        
        # Calculate current week range
        today = date.today()
        monday = today - timedelta(days=today.weekday())  # Get Monday of current week
        sunday = monday + timedelta(days=6)  # Get Sunday of current week
        
        print(f"\nCurrent date: {today}")
        print(f"Current week range: {monday} to {sunday}")
        
        print(f"\nTask details:")
        for i, task in enumerate(all_tasks, 1):
            print(f"Task {i}:")
            print(f"  ID: {task.id}")
            print(f"  Title: {task.title}")
            print(f"  Start Date: {task.start_date}")
            print(f"  Due Date: {task.due_date}")
            print(f"  Is Deleted: {task.is_deleted}")
            
            # Check if task is in current week
            task_date = None
            if task.start_date:
                task_date = task.start_date.date()
            elif task.due_date:
                task_date = task.due_date.date()
            
            if task_date:
                in_current_week = monday <= task_date <= sunday
                print(f"  Task Date: {task_date}")
                print(f"  In Current Week: {in_current_week}")
            else:
                print(f"  Task Date: None")
                print(f"  In Current Week: False (no date)")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    debug_task_dates()
