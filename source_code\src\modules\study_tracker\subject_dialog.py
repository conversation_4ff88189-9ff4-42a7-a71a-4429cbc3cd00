"""
学习科目管理对话框

提供添加和编辑学习科目的界面
"""

from typing import Dict, Any, Optional

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QTextEdit, QPushButton, QComboBox,
    QColorDialog, QMessageBox, QGroupBox, QSpinBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor, QPalette

from core.logger import LoggerMixin


class SubjectDialog(QDialog, LoggerMixin):
    """学习科目管理对话框"""
    
    def __init__(self, parent=None, subject_data: Dict[str, Any] = None):
        super().__init__(parent)
        self.subject_data = subject_data or {}
        self.selected_color = self.subject_data.get('color', '#3498db')
        
        self.init_ui()
        self.load_data()
        
        self.logger.info("Subject dialog initialized")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("学习科目管理")
        self.setModal(True)
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QGridLayout(basic_group)
        
        # 科目名称
        basic_layout.addWidget(QLabel("科目名称:"), 0, 0)
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入科目名称")
        basic_layout.addWidget(self.name_edit, 0, 1)
        
        # 科目描述
        basic_layout.addWidget(QLabel("科目描述:"), 1, 0)
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(60)
        self.description_edit.setPlaceholderText("可选，描述科目内容")
        basic_layout.addWidget(self.description_edit, 1, 1)
        
        layout.addWidget(basic_group)
        
        # 外观设置组
        appearance_group = QGroupBox("外观设置")
        appearance_layout = QGridLayout(appearance_group)
        
        # 颜色选择
        appearance_layout.addWidget(QLabel("科目颜色:"), 0, 0)
        color_layout = QHBoxLayout()
        
        self.color_preview = QPushButton()
        self.color_preview.setFixedSize(30, 30)
        self.color_preview.clicked.connect(self.choose_color)
        self.update_color_preview()
        
        self.color_btn = QPushButton("选择颜色")
        self.color_btn.clicked.connect(self.choose_color)
        
        color_layout.addWidget(self.color_preview)
        color_layout.addWidget(self.color_btn)
        color_layout.addStretch()
        
        appearance_layout.addLayout(color_layout, 0, 1)
        
        # 图标选择
        appearance_layout.addWidget(QLabel("科目图标:"), 1, 0)
        self.icon_combo = QComboBox()
        self.icon_combo.addItems([
            "book", "calculator", "computer", "music", "palette",
            "globe", "flask", "dumbbell", "language", "camera"
        ])
        appearance_layout.addWidget(self.icon_combo, 1, 1)
        
        layout.addWidget(appearance_group)
        
        # 设置组
        settings_group = QGroupBox("其他设置")
        settings_layout = QGridLayout(settings_group)
        
        # 排序顺序
        settings_layout.addWidget(QLabel("排序顺序:"), 0, 0)
        self.sort_order_spin = QSpinBox()
        self.sort_order_spin.setRange(0, 999)
        self.sort_order_spin.setValue(0)
        settings_layout.addWidget(self.sort_order_spin, 0, 1)
        
        layout.addWidget(settings_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept_dialog)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def load_data(self):
        """加载现有数据（编辑模式）"""
        if not self.subject_data:
            return
        
        self.name_edit.setText(self.subject_data.get('name', ''))
        self.description_edit.setPlainText(self.subject_data.get('description', ''))
        
        # 设置颜色
        color = self.subject_data.get('color', '#3498db')
        self.selected_color = color
        self.update_color_preview()
        
        # 设置图标
        icon = self.subject_data.get('icon', 'book')
        index = self.icon_combo.findText(icon)
        if index >= 0:
            self.icon_combo.setCurrentIndex(index)
        
        # 设置排序顺序
        sort_order = self.subject_data.get('sort_order', 0)
        self.sort_order_spin.setValue(sort_order)
    
    def choose_color(self):
        """选择颜色"""
        color = QColorDialog.getColor(QColor(self.selected_color), self)
        if color.isValid():
            self.selected_color = color.name()
            self.update_color_preview()
    
    def update_color_preview(self):
        """更新颜色预览"""
        self.color_preview.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.selected_color};
                border: 2px solid #ccc;
                border-radius: 15px;
            }}
            QPushButton:hover {{
                border: 2px solid #999;
            }}
        """)
    
    def accept_dialog(self):
        """确认对话框"""
        # 验证输入
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "警告", "请输入科目名称！")
            self.name_edit.setFocus()
            return
        
        # 验证名称长度
        if len(name) > 50:
            QMessageBox.warning(self, "警告", "科目名称不能超过50个字符！")
            self.name_edit.setFocus()
            return
        
        self.accept()
    
    def get_subject_data(self) -> Dict[str, Any]:
        """获取科目数据"""
        return {
            'name': self.name_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip() or None,
            'color': self.selected_color,
            'icon': self.icon_combo.currentText(),
            'sort_order': self.sort_order_spin.value()
        }
