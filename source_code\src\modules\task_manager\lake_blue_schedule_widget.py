"""
Lake Blue Theme Schedule Management Widget
湖面蓝主题日程管理界面

基于现有任务管理模块，实现湖面蓝主题的日程管理界面
"""

from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QSplitter, QScrollArea, QGroupBox, QGridLayout, QSpacerItem,
    QSizePolicy, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal, QDate, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QIcon, QPainter, QPen, QBrush, QColor, QLinearGradient

from core.logger import LoggerMixin
from models.task_models import Task, TaskStatus, TaskPriority, TaskCategory
from .task_service import TaskService
from .weekly_task_table import WeeklyTaskTable
from .date_week_display import DateWeekDisplay


class LakeBlueTheme:
    """湖面蓝主题色彩系统"""
    
    # 主色调 - 湖蓝渐变色系
    DEEP_BLUE = "#023047"      # 深蓝
    MEDIUM_BLUE = "#0096C7"    # 中蓝  
    LIGHT_BLUE = "#CAF0F8"     # 浅蓝
    
    # 辅助色彩
    WAVE_WHITE = "#CAF0F8"     # 浪花白
    SEAWEED_GREEN = "#4CC9F0"  # 水藻绿
    MINT_GREEN = "#80FFDB"     # 薄荷绿（已完成）
    CORAL_RED = "#FF9E90"      # 珊瑚红（未完成）
    
    # 透明度变体
    GLASS_WHITE = "rgba(255,255,255,0.2)"  # 磨砂玻璃
    SEMI_TRANSPARENT = "rgba(202,240,248,0.6)"  # 半透明浅蓝
    
    @staticmethod
    def get_gradient_style(start_color: str, end_color: str, direction: str = "to right") -> str:
        """获取渐变样式"""
        return f"background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {start_color}, stop:1 {end_color});"
    
    @staticmethod
    def get_card_style() -> str:
        """获取卡片样式"""
        return f"""
            background-color: white;
            border: 1px solid {LakeBlueTheme.LIGHT_BLUE};
            border-radius: 8px;
            padding: 10px;
            margin: 5px;
        """
    
    @staticmethod
    def get_glass_style() -> str:
        """获取磨砂玻璃样式"""
        return f"""
            background-color: {LakeBlueTheme.GLASS_WHITE};
            border: 1px solid {LakeBlueTheme.LIGHT_BLUE};
            border-radius: 6px;
            color: {LakeBlueTheme.DEEP_BLUE};
        """


class StatusIndicator(QWidget):
    """圆形状态指示器"""
    
    def __init__(self, status: str = "pending", size: int = 12, parent=None):
        super().__init__(parent)
        self.status = status
        self.size = size
        self.setFixedSize(size, size)
    
    def set_status(self, status: str):
        """设置状态"""
        self.status = status
        self.update()
    
    def paintEvent(self, event):
        """绘制状态指示器"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 根据状态选择颜色
        if self.status == "completed":
            color = QColor(LakeBlueTheme.MINT_GREEN)
        else:
            color = QColor(LakeBlueTheme.CORAL_RED)
        
        painter.setBrush(QBrush(color))
        painter.setPen(QPen(color.darker(120), 1))
        painter.drawEllipse(0, 0, self.size, self.size)


class TaskCard(QFrame):
    """任务卡片组件"""
    
    task_clicked = pyqtSignal(dict)
    task_status_changed = pyqtSignal(dict)
    
    def __init__(self, task_data: dict, parent=None):
        super().__init__(parent)
        self.task_data = task_data
        self.init_ui()
        self.setup_style()
    
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(8)
        
        # 状态指示器
        self.status_indicator = StatusIndicator(self.task_data.get('status', 'pending'))
        layout.addWidget(self.status_indicator)
        
        # 任务内容
        content_layout = QVBoxLayout()
        content_layout.setSpacing(2)
        
        # 任务标题
        self.title_label = QLabel(self.task_data.get('title', '未命名任务'))
        self.title_label.setFont(QFont("Inter", 10, QFont.Weight.Bold))
        content_layout.addWidget(self.title_label)
        
        # 任务描述（如果有）
        description = self.task_data.get('description', '')
        if description:
            self.desc_label = QLabel(description[:50] + "..." if len(description) > 50 else description)
            self.desc_label.setFont(QFont("", 8))
            self.desc_label.setStyleSheet(f"color: {LakeBlueTheme.MEDIUM_BLUE};")
            content_layout.addWidget(self.desc_label)
        
        layout.addLayout(content_layout)
        layout.addStretch()
        
        # 优先级标识（如果是高优先级）
        priority = self.task_data.get('priority', 'normal')
        if priority in ['high', 'urgent']:
            priority_label = QLabel("!")
            priority_label.setStyleSheet(f"color: {LakeBlueTheme.CORAL_RED}; font-weight: bold;")
            layout.addWidget(priority_label)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            TaskCard {{
                {LakeBlueTheme.get_card_style()}
            }}
            TaskCard:hover {{
                background-color: {LakeBlueTheme.LIGHT_BLUE};
            }}
        """)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.task_clicked.emit(self.task_data)
        super().mousePressEvent(event)


class TimeSection(QFrame):
    """时间分区卡片"""
    
    def __init__(self, title: str, time_period: str, parent=None):
        super().__init__(parent)
        self.title = title
        self.time_period = time_period
        self.tasks = []
        self.init_ui()
        self.setup_style()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 8, 10, 8)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel(self.title)
        title_label.setFont(QFont("Inter", 11, QFont.Weight.Bold))
        title_label.setStyleSheet(f"color: {LakeBlueTheme.DEEP_BLUE};")
        layout.addWidget(title_label)
        
        # 任务容器
        self.task_container = QVBoxLayout()
        self.task_container.setSpacing(3)
        layout.addLayout(self.task_container)
        
        # 添加按钮
        self.add_btn = QPushButton("+ 添加任务")
        self.add_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {LakeBlueTheme.GLASS_WHITE};
                border: 1px dashed {LakeBlueTheme.MEDIUM_BLUE};
                border-radius: 4px;
                padding: 5px;
                color: {LakeBlueTheme.MEDIUM_BLUE};
                font-size: 9px;
            }}
            QPushButton:hover {{
                background-color: {LakeBlueTheme.LIGHT_BLUE};
            }}
        """)
        layout.addWidget(self.add_btn)
        
        layout.addStretch()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            TimeSection {{
                background-color: {LakeBlueTheme.LIGHT_BLUE};
                border-radius: 8px;
                margin: 2px;
            }}
        """)
    
    def add_task(self, task_data: dict):
        """添加任务"""
        task_card = TaskCard(task_data)
        self.task_container.addWidget(task_card)
        self.tasks.append(task_card)
    
    def clear_tasks(self):
        """清空任务"""
        for task in self.tasks:
            task.deleteLater()
        self.tasks.clear()


class WeekNavigationBar(QFrame):
    """周视图导航栏"""
    
    date_selected = pyqtSignal(date)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_date = date.today()
        self.week_start = self.get_week_start(self.current_date)
        self.init_ui()
        self.setup_style()
    
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(5)
        
        # 周一到周日的日期标签
        self.date_labels = []
        weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        
        for i, weekday in enumerate(weekdays):
            date_widget = QFrame()
            date_layout = QVBoxLayout(date_widget)
            date_layout.setContentsMargins(8, 5, 8, 5)
            date_layout.setSpacing(2)
            
            # 星期标签
            weekday_label = QLabel(weekday)
            weekday_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            weekday_label.setFont(QFont("", 9))
            date_layout.addWidget(weekday_label)
            
            # 日期标签
            current_date = self.week_start + timedelta(days=i)
            date_label = QLabel(str(current_date.day))
            date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            date_label.setFont(QFont("", 12, QFont.Weight.Bold))
            date_layout.addWidget(date_label)
            
            # 设置样式
            if current_date == self.current_date:
                date_widget.setStyleSheet(f"""
                    QFrame {{
                        background-color: {LakeBlueTheme.LIGHT_BLUE};
                        border-radius: 6px;
                        border: 2px solid {LakeBlueTheme.MEDIUM_BLUE};
                    }}
                """)
            else:
                date_widget.setStyleSheet(f"""
                    QFrame {{
                        background-color: {LakeBlueTheme.GLASS_WHITE};
                        border-radius: 6px;
                        border: 1px solid {LakeBlueTheme.LIGHT_BLUE};
                    }}
                    QFrame:hover {{
                        background-color: {LakeBlueTheme.LIGHT_BLUE};
                    }}
                """)
            
            self.date_labels.append((date_widget, current_date))
            layout.addWidget(date_widget)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            WeekNavigationBar {{
                background-color: white;
                border-radius: 8px;
                border: 1px solid {LakeBlueTheme.LIGHT_BLUE};
            }}
        """)
    
    def get_week_start(self, target_date: date) -> date:
        """获取周开始日期（周一）"""
        days_since_monday = target_date.weekday()
        return target_date - timedelta(days=days_since_monday)


class StatusBar(QFrame):
    """顶部状态栏"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.setup_style()
        self.update_date_info()
    
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # 日期信息标签
        self.date_info_label = QLabel()
        self.date_info_label.setFont(QFont("Inter", 14, QFont.Weight.Bold))
        self.date_info_label.setStyleSheet("color: white;")
        layout.addWidget(self.date_info_label)
        
        layout.addStretch()
        
        # 可以添加其他状态信息
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            StatusBar {{
                {LakeBlueTheme.get_gradient_style(LakeBlueTheme.SEAWEED_GREEN, LakeBlueTheme.MEDIUM_BLUE)}
                border-radius: 8px;
                margin: 5px;
            }}
        """)
    
    def update_date_info(self):
        """更新日期信息"""
        today = date.today()
        weekday_names = ["一", "二", "三", "四", "五", "六", "日"]
        weekday = weekday_names[today.weekday()]
        
        # 计算周数和剩余天数
        year_start = date(today.year, 1, 1)
        week_num = ((today - year_start).days // 7) + 1
        year_end = date(today.year, 12, 31)
        remaining_days = (year_end - today).days
        
        date_text = f"{today.year}年{today.month:02d}月{today.day:02d}日 星期{weekday} 第{week_num}周 剩余{remaining_days}天"
        self.date_info_label.setText(date_text)


class MainSchedulePanel(QFrame):
    """主日程面板（占界面60%宽度）"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_date = date.today()
        self.tasks_data = {}
        self.init_ui()
        self.setup_style()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 顶部状态栏
        self.status_bar = StatusBar()
        layout.addWidget(self.status_bar)

        # 周视图导航栏
        self.week_nav = WeekNavigationBar()
        layout.addWidget(self.week_nav)

        # 时间分区容器
        self.create_time_sections()
        layout.addWidget(self.time_sections_frame)

        # 悬浮添加按钮
        self.create_floating_add_button()

    def create_time_sections(self):
        """创建时间分区卡片"""
        self.time_sections_frame = QFrame()
        sections_layout = QVBoxLayout(self.time_sections_frame)
        sections_layout.setContentsMargins(5, 5, 5, 5)
        sections_layout.setSpacing(8)

        # 创建五个时间段
        time_periods = [
            ("上午", "morning"),
            ("下午", "afternoon"),
            ("晚上", "evening"),
            ("全天", "allday"),
            ("周计划", "weekly")
        ]

        self.time_sections = {}
        for title, period in time_periods:
            section = TimeSection(title, period)
            self.time_sections[period] = section
            sections_layout.addWidget(section)

    def create_floating_add_button(self):
        """创建悬浮添加按钮"""
        self.floating_btn = QPushButton("💧")
        self.floating_btn.setFixedSize(50, 50)
        self.floating_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {LakeBlueTheme.DEEP_BLUE};
                color: white;
                border-radius: 25px;
                font-size: 20px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: {LakeBlueTheme.MEDIUM_BLUE};
            }}
            QPushButton:pressed {{
                background-color: {LakeBlueTheme.DEEP_BLUE};
            }}
        """)

        # 定位到右下角
        self.floating_btn.setParent(self)
        self.floating_btn.move(self.width() - 70, self.height() - 70)
        self.floating_btn.show()

    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            MainSchedulePanel {{
                background-color: white;
                border-radius: 10px;
                border: 1px solid {LakeBlueTheme.LIGHT_BLUE};
            }}
        """)

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        if hasattr(self, 'floating_btn'):
            self.floating_btn.move(self.width() - 70, self.height() - 70)

    def load_tasks_for_date(self, target_date: date):
        """加载指定日期的任务"""
        self.current_date = target_date

        # 清空现有任务
        for section in self.time_sections.values():
            section.clear_tasks()

        # 从任务数据中加载任务（这里需要连接到实际的任务服务）
        # 示例数据
        sample_tasks = [
            {"title": "晨会", "description": "团队日常晨会", "status": "pending", "priority": "normal", "time_period": "morning"},
            {"title": "项目评审", "description": "季度项目进度评审", "status": "completed", "priority": "high", "time_period": "afternoon"},
            {"title": "健身", "description": "晚间健身锻炼", "status": "pending", "priority": "normal", "time_period": "evening"},
            {"title": "周计划制定", "description": "制定下周工作计划", "status": "pending", "priority": "normal", "time_period": "weekly"},
        ]

        for task in sample_tasks:
            period = task.get("time_period", "allday")
            if period in self.time_sections:
                self.time_sections[period].add_task(task)


class SummaryPanel(QFrame):
    """侧边总结面板（占界面40%宽度）"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.setup_style()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # 日统计模块
        self.create_daily_stats()
        layout.addWidget(self.daily_stats_group)

        # 周统计模块
        self.create_weekly_stats()
        layout.addWidget(self.weekly_stats_group)

        layout.addStretch()

    def create_daily_stats(self):
        """创建日统计模块"""
        self.daily_stats_group = QGroupBox("日统计")
        self.daily_stats_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 12px;
                color: white;
                {LakeBlueTheme.get_gradient_style(LakeBlueTheme.MEDIUM_BLUE, LakeBlueTheme.SEAWEED_GREEN)}
                border-radius: 8px;
                padding-top: 15px;
                margin-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)

        layout = QVBoxLayout(self.daily_stats_group)
        layout.setSpacing(10)

        # 任务完成率进度条
        self.create_progress_section(layout, "今日完成率", 75)

        # 任务清单
        self.create_task_list_section(layout, "今日任务")

        # 输入框
        self.create_input_section(layout, "日总结")

    def create_weekly_stats(self):
        """创建周统计模块"""
        self.weekly_stats_group = QGroupBox("周统计")
        self.weekly_stats_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 12px;
                color: white;
                {LakeBlueTheme.get_gradient_style(LakeBlueTheme.MEDIUM_BLUE, LakeBlueTheme.SEAWEED_GREEN)}
                border-radius: 8px;
                padding-top: 15px;
                margin-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)

        layout = QVBoxLayout(self.weekly_stats_group)
        layout.setSpacing(10)

        # 周完成率
        self.create_progress_section(layout, "本周完成率", 60)

        # 周任务清单
        self.create_task_list_section(layout, "本周重点")

        # 周总结输入
        self.create_input_section(layout, "周总结")

    def create_progress_section(self, parent_layout, title: str, progress: int):
        """创建进度条部分"""
        progress_frame = QFrame()
        progress_frame.setStyleSheet("background-color: rgba(255,255,255,0.1); border-radius: 6px; padding: 8px;")

        layout = QVBoxLayout(progress_frame)
        layout.setSpacing(5)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(title_label)

        # 进度条（使用水波纹样式）
        progress_bar = QFrame()
        progress_bar.setFixedHeight(20)
        progress_bar.setStyleSheet(f"""
            QFrame {{
                background-color: rgba(255,255,255,0.3);
                border-radius: 10px;
            }}
        """)

        # 进度填充
        progress_fill = QFrame(progress_bar)
        progress_fill.setFixedHeight(18)
        progress_fill.setFixedWidth(int(progress_bar.width() * progress / 100))
        progress_fill.setStyleSheet(f"""
            QFrame {{
                background-color: {LakeBlueTheme.MINT_GREEN};
                border-radius: 9px;
                margin: 1px;
            }}
        """)

        layout.addWidget(progress_bar)

        # 百分比标签
        percent_label = QLabel(f"{progress}%")
        percent_label.setStyleSheet("color: white; font-size: 11px;")
        percent_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout.addWidget(percent_label)

        parent_layout.addWidget(progress_frame)

    def create_task_list_section(self, parent_layout, title: str):
        """创建任务清单部分"""
        list_frame = QFrame()
        list_frame.setStyleSheet("background-color: rgba(255,255,255,0.1); border-radius: 6px; padding: 8px;")

        layout = QVBoxLayout(list_frame)
        layout.setSpacing(5)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(title_label)

        # 示例任务项
        sample_tasks = [
            ("✅", "完成项目文档", True),
            ("❌", "团队会议准备", False),
            ("✅", "代码审查", True),
            ("❌", "客户沟通", False),
        ]

        for icon, task_text, completed in sample_tasks:
            task_item = QLabel(f"{icon} {task_text}")
            task_item.setStyleSheet(f"""
                color: {'white' if completed else '#FFE5E5'};
                font-size: 10px;
                padding: 2px;
                border-bottom: 1px solid rgba(255,255,255,0.2);
            """)
            layout.addWidget(task_item)

        parent_layout.addWidget(list_frame)

    def create_input_section(self, parent_layout, title: str):
        """创建输入框部分"""
        input_frame = QFrame()
        input_frame.setStyleSheet("background-color: rgba(255,255,255,0.1); border-radius: 6px; padding: 8px;")

        layout = QVBoxLayout(input_frame)
        layout.setSpacing(5)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(title_label)

        # 输入框（磨砂玻璃质感）
        from PyQt6.QtWidgets import QTextEdit
        input_edit = QTextEdit()
        input_edit.setMaximumHeight(80)
        input_edit.setStyleSheet(f"""
            QTextEdit {{
                {LakeBlueTheme.get_glass_style()}
                font-size: 10px;
                padding: 5px;
            }}
        """)
        input_edit.setPlaceholderText(f"输入{title}...")
        layout.addWidget(input_edit)

        parent_layout.addWidget(input_frame)

    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            SummaryPanel {{
                background-color: white;
                border-radius: 10px;
                border: 1px solid {LakeBlueTheme.LIGHT_BLUE};
            }}
        """)


class LakeBlueScheduleWidget(QWidget, LoggerMixin):
    """湖面蓝主题日程管理主界面"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.task_service = TaskService()
        self.current_date = date.today()
        self.init_ui()
        self.setup_style()
        self.load_data()

        # 设置定时器定期刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # 每分钟刷新一次

    def init_ui(self):
        """初始化用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 创建主分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 主日程面板（60%宽度）
        self.main_panel = MainSchedulePanel()
        splitter.addWidget(self.main_panel)

        # 侧边总结面板（40%宽度）
        self.summary_panel = SummaryPanel()
        splitter.addWidget(self.summary_panel)

        # 设置分割比例
        splitter.setStretchFactor(0, 6)  # 主面板60%
        splitter.setStretchFactor(1, 4)  # 侧边面板40%

        layout.addWidget(splitter)

        # 连接信号
        self.connect_signals()

    def connect_signals(self):
        """连接信号和槽"""
        # 周导航信号
        if hasattr(self.main_panel, 'week_nav'):
            self.main_panel.week_nav.date_selected.connect(self.on_date_selected)

        # 悬浮按钮信号
        if hasattr(self.main_panel, 'floating_btn'):
            self.main_panel.floating_btn.clicked.connect(self.on_add_task_clicked)

        # 时间分区添加按钮信号
        for section in self.main_panel.time_sections.values():
            section.add_btn.clicked.connect(lambda checked, s=section: self.on_add_task_to_section(s))

    def setup_style(self):
        """设置整体样式"""
        self.setStyleSheet(f"""
            LakeBlueScheduleWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {LakeBlueTheme.WAVE_WHITE},
                    stop:0.5 #F0F8FF,
                    stop:1 {LakeBlueTheme.LIGHT_BLUE});
            }}
        """)

    def load_data(self):
        """加载数据"""
        try:
            # 加载当前日期的任务
            self.main_panel.load_tasks_for_date(self.current_date)

            # 更新统计信息
            self.update_statistics()

            self.logger.info("Lake blue schedule data loaded successfully")

        except Exception as e:
            self.logger.error(f"Failed to load schedule data: {e}")

    def refresh_data(self):
        """刷新数据"""
        self.load_data()

    def on_date_selected(self, selected_date: date):
        """日期选择事件"""
        self.current_date = selected_date
        self.main_panel.load_tasks_for_date(selected_date)
        self.update_statistics()

    def on_add_task_clicked(self):
        """悬浮添加按钮点击事件"""
        self.show_add_task_dialog()

    def on_add_task_to_section(self, section):
        """时间分区添加任务事件"""
        self.show_add_task_dialog(section.time_period)

    def show_add_task_dialog(self, time_period: str = None):
        """显示添加任务对话框"""
        # 这里可以复用现有的任务对话框，或创建新的湖面蓝主题对话框
        from .weekly_task_manager_widget import TaskDialog

        dialog = TaskDialog(parent=self)
        if time_period:
            # 设置默认时间段
            pass

        if dialog.exec() == dialog.DialogCode.Accepted:
            task_data = dialog.get_task_data()
            self.create_task(task_data)

    def create_task(self, task_data: dict):
        """创建新任务"""
        try:
            # 使用现有的任务服务创建任务
            task = Task(
                title=task_data.get('title', ''),
                description=task_data.get('description', ''),
                status=TaskStatus.PENDING,
                priority=TaskPriority.NORMAL,
                due_date=task_data.get('due_date'),
                tags=task_data.get('tags', [])
            )

            created_task = self.task_service.create_task(task)

            # 刷新界面
            self.load_data()

            self.logger.info(f"Task created: {created_task.title}")

        except Exception as e:
            self.logger.error(f"Failed to create task: {e}")

    def update_statistics(self):
        """更新统计信息"""
        try:
            # 获取今日任务统计 - 使用现有的get_tasks方法
            all_tasks = self.task_service.get_tasks(limit=1000)

            # 过滤今日任务（简化版本，实际应该根据日期过滤）
            today_tasks = all_tasks[:10]  # 临时使用前10个任务作为示例

            if today_tasks:
                completed_count = len([t for t in today_tasks if t.status == TaskStatus.COMPLETED])
                total_count = len(today_tasks)
                completion_rate = int((completed_count / total_count) * 100) if total_count > 0 else 0
            else:
                completion_rate = 0

            # 更新进度条（这里需要实际连接到UI组件）
            # self.summary_panel.update_daily_progress(completion_rate)

            self.logger.debug(f"Statistics updated: {completion_rate}% completion rate")

        except Exception as e:
            self.logger.error(f"Failed to update statistics: {e}")

    def get_tasks_by_time_period(self, target_date: date, time_period: str) -> List[Task]:
        """根据时间段获取任务"""
        try:
            # 使用现有的get_tasks方法获取所有任务
            all_tasks = self.task_service.get_tasks(limit=1000)

            # 根据标签过滤时间段
            period_tasks = []
            for task in all_tasks:
                if task.tags:
                    if time_period == "morning" and "上午" in task.tags:
                        period_tasks.append(task)
                    elif time_period == "afternoon" and "下午" in task.tags:
                        period_tasks.append(task)
                    elif time_period == "evening" and "晚上" in task.tags:
                        period_tasks.append(task)
                    elif time_period == "weekly" and "周计划" in task.tags:
                        period_tasks.append(task)
                    elif time_period == "allday" and not any(p in task.tags for p in ["上午", "下午", "晚上", "周计划"]):
                        period_tasks.append(task)

            return period_tasks

        except Exception as e:
            self.logger.error(f"Failed to get tasks by time period: {e}")
            return []
