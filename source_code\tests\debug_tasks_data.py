#!/usr/bin/env python3
"""
Debug script to check tasks_data structure
"""

import sys
from pathlib import Path
from datetime import date, datetime, timedelta

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from core.config import ConfigManager
from core.database import init_db_manager
from modules.task_manager.task_service import TaskService
from modules.task_manager.weekly_task_table import WeeklyTaskTable

# Import all models to ensure they are registered
from models import *


def debug_tasks_data():
    """Debug tasks_data structure"""
    print("Debugging Tasks Data Structure")
    print("=" * 50)
    
    try:
        # Initialize database
        config_manager = ConfigManager()
        db_manager = init_db_manager(config_manager)
        
        if not db_manager:
            print("Failed to initialize database")
            return False
        
        service = TaskService()
        
        # Get all tasks
        all_tasks = service.get_tasks(limit=1000)
        print(f"Total tasks in database: {len(all_tasks)}")
        
        # Test determine_task_category logic manually
        print(f"\nTask category determination:")
        for task in all_tasks:
            # Manually implement determine_task_category logic
            category = "其他"  # Default
            if task.tags:
                tags = task.tags.lower()
                if '周计划' in tags:
                    category = "周计划"
                elif '上午' in tags:
                    category = "上午"
                elif '下午' in tags:
                    category = "下午"
                elif '晚上' in tags:
                    category = "晚上"
                elif '全天' in tags:
                    category = "全天"

            print(f"Task '{task.title}' (tags: {task.tags}) -> Category: '{category}'")

        # Calculate current week range
        today = date.today()
        monday = today - timedelta(days=today.weekday())  # Get Monday of current week
        sunday = monday + timedelta(days=6)  # Get Sunday of current week

        print(f"\nCurrent week range: {monday} to {sunday}")

        # Simulate the tasks_data structure
        tasks_data = {}
        weekly_plan_tasks = []

        for task in all_tasks:
            # Determine category
            category = "其他"  # Default
            if task.tags:
                tags = task.tags.lower()
                if '周计划' in tags:
                    category = "周计划"
                elif '上午' in tags:
                    category = "上午"
                elif '下午' in tags:
                    category = "下午"
                elif '晚上' in tags:
                    category = "晚上"
                elif '全天' in tags:
                    category = "全天"

            # Handle weekly plan tasks
            if category == "周计划":
                weekly_plan_tasks.append({
                    'id': task.id,
                    'title': task.title,
                    'category': category
                })
                continue

            # For regular tasks, use start_date if available, otherwise fall back to due_date
            if task.start_date:
                task_date = task.start_date.date()
            elif task.due_date:
                task_date = task.due_date.date()
            else:
                continue

            # Only show tasks within current week
            if not (monday <= task_date <= sunday):
                continue

            if task_date not in tasks_data:
                tasks_data[task_date] = {}
            if category not in tasks_data[task_date]:
                tasks_data[task_date][category] = []

            tasks_data[task_date][category].append({
                'id': task.id,
                'title': task.title,
                'category': category,
                'date': task_date
            })

        print(f"\nSimulated tasks_data structure:")
        print(f"tasks_data keys (dates): {list(tasks_data.keys())}")

        for task_date, categories in tasks_data.items():
            print(f"\nDate: {task_date}")
            for category, tasks in categories.items():
                print(f"  Category '{category}': {len(tasks)} tasks")
                for i, task in enumerate(tasks):
                    print(f"    Task {i+1}: {task['title']} (ID: {task['id']})")

        print(f"\nWeekly plan tasks: {len(weekly_plan_tasks)} tasks")
        for i, task in enumerate(weekly_plan_tasks):
            print(f"  Task {i+1}: {task['title']} (ID: {task['id']})")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    debug_tasks_data()
