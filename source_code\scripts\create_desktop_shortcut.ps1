# Personal Manager Desktop Shortcut Creator
# This script creates a desktop shortcut for the Personal Manager application

param(
    [string]$ShortcutName = "私人经理",
    [string]$Description = "Personal Manager - 个人管理系统"
)

# Get the current script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Define paths
$VBSFile = Join-Path $ScriptDir "start_personal_manager.vbs"
$IconFile = Join-Path $ScriptDir "resources\icons\app.ico"
$DesktopPath = [Environment]::GetFolderPath("Desktop")
$ShortcutPath = Join-Path $DesktopPath "$ShortcutName.lnk"

Write-Host "Creating desktop shortcut for Personal Manager..."
Write-Host "Script directory: $ScriptDir"
Write-Host "VBS launcher: $VBSFile"
Write-Host "Icon file: $IconFile"
Write-Host "Desktop path: $DesktopPath"
Write-Host "Shortcut path: $ShortcutPath"

# Check if VBS file exists
if (-not (Test-Path $VBSFile)) {
    Write-Error "VBS launcher not found: $VBSFile"
    Write-Host "Please make sure start_personal_manager.vbs exists in the same directory as this script."
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if icon file exists
if (-not (Test-Path $IconFile)) {
    Write-Warning "Icon file not found: $IconFile"
    Write-Host "The shortcut will be created without a custom icon."
    $IconFile = $null
}

try {
    # Create WScript.Shell COM object
    $WshShell = New-Object -ComObject WScript.Shell
    
    # Create the shortcut
    $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
    $Shortcut.TargetPath = $VBSFile
    $Shortcut.WorkingDirectory = $ScriptDir
    $Shortcut.Description = $Description
    $Shortcut.WindowStyle = 1  # Normal window
    
    # Set icon if available
    if ($IconFile -and (Test-Path $IconFile)) {
        $Shortcut.IconLocation = "$IconFile,0"
    }
    
    # Save the shortcut
    $Shortcut.Save()
    
    Write-Host "Desktop shortcut created successfully!" -ForegroundColor Green
    Write-Host "Shortcut location: $ShortcutPath" -ForegroundColor Green
    Write-Host ""
    Write-Host "You can now double-click the '$ShortcutName' icon on your desktop to start Personal Manager."
    
} catch {
    Write-Error "Failed to create desktop shortcut: $($_.Exception.Message)"
    Write-Host "Error details: $($_.Exception.ToString())"
    Read-Host "Press Enter to exit"
    exit 1
}

# Optional: Create Start Menu shortcut as well
$CreateStartMenuShortcut = Read-Host "Would you like to create a Start Menu shortcut as well? (y/n)"
if ($CreateStartMenuShortcut -eq "y" -or $CreateStartMenuShortcut -eq "Y") {
    try {
        $StartMenuPath = [Environment]::GetFolderPath("StartMenu")
        $ProgramsPath = Join-Path $StartMenuPath "Programs"
        $StartMenuShortcutPath = Join-Path $ProgramsPath "$ShortcutName.lnk"
        
        $StartMenuShortcut = $WshShell.CreateShortcut($StartMenuShortcutPath)
        $StartMenuShortcut.TargetPath = $VBSFile
        $StartMenuShortcut.WorkingDirectory = $ScriptDir
        $StartMenuShortcut.Description = $Description
        $StartMenuShortcut.WindowStyle = 1
        
        if ($IconFile -and (Test-Path $IconFile)) {
            $StartMenuShortcut.IconLocation = "$IconFile,0"
        }
        
        $StartMenuShortcut.Save()
        
        Write-Host "Start Menu shortcut created successfully!" -ForegroundColor Green
        Write-Host "Start Menu location: $StartMenuShortcutPath" -ForegroundColor Green
        
    } catch {
        Write-Warning "Failed to create Start Menu shortcut: $($_.Exception.Message)"
    }
}

Write-Host ""
Write-Host "Setup complete! You can now launch Personal Manager from:"
Write-Host "1. Desktop shortcut: '$ShortcutName'"
if ($CreateStartMenuShortcut -eq "y" -or $CreateStartMenuShortcut -eq "Y") {
    Write-Host "2. Start Menu: '$ShortcutName'"
}
Write-Host "3. Directly running: start_personal_manager.vbs"

Read-Host "Press Enter to exit"
