#!/usr/bin/env python3
"""
调试桌面小部件启动问题
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

print("开始调试...")

try:
    print("1. 导入PyQt6...")
    from PyQt6.QtWidgets import QApplication
    print("   PyQt6导入成功")
    
    print("2. 初始化应用...")
    app = QApplication(sys.argv)
    print("   应用初始化成功")
    
    print("3. 导入数据库模块...")
    from core.database import init_database
    print("   数据库模块导入成功")
    
    print("4. 初始化数据库...")
    init_database()
    print("   数据库初始化成功")
    
    print("5. 导入任务服务...")
    from modules.task_manager.task_service import TaskService
    print("   任务服务导入成功")
    
    print("6. 创建任务服务实例...")
    task_service = TaskService()
    print("   任务服务创建成功")
    
    print("7. 测试获取任务...")
    tasks = task_service.get_tasks(limit=10)
    print(f"   获取到 {len(tasks)} 个任务")
    
    # 显示任务信息
    for i, task in enumerate(tasks[:5]):  # 只显示前5个
        print(f"   任务 {i+1}: {task.title}, 标签: {task.tags}")
    
    print("8. 导入桌面小部件...")
    from modules.task_manager.desktop_widget import DesktopTaskWidget
    print("   桌面小部件导入成功")
    
    print("9. 创建桌面小部件...")
    widget = DesktopTaskWidget()
    print("   桌面小部件创建成功")
    
    print("10. 显示桌面小部件...")
    widget.show()
    print("   桌面小部件显示成功")
    
    print("调试完成！桌面小部件应该已经显示。")
    print("按 Ctrl+C 退出...")
    
    # 运行应用
    sys.exit(app.exec())
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
