#!/usr/bin/env python3
"""
Test script for weekly summary database operations
"""

import sys
from pathlib import Path
from datetime import date, datetime, timedelta

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from core.config import ConfigManager
from core.database import init_db_manager, get_session
from models.task_models import WeeklySummary

# Import all models to ensure they are registered
from models import *


def test_weekly_summary_operations():
    """Test basic weekly summary operations"""
    print("Testing Weekly Summary Operations")
    print("=" * 40)
    
    # Initialize database
    config_manager = ConfigManager()
    db_manager = init_db_manager(config_manager)
    
    if not db_manager:
        print("Failed to initialize database")
        return False
    
    try:
        # Create tables if they don't exist
        db_manager.create_tables()
        print("✓ Database tables created/verified")
        
        # Test data
        today = date.today()
        # Get Monday of current week
        days_since_monday = today.weekday()
        week_start = today - timedelta(days=days_since_monday)
        
        test_content = "这是一个测试周总结内容。\n包含多行文本。"
        
        with get_session() as session:
            # Test 1: Create new weekly summary
            print(f"\n1. Creating weekly summary for week starting {week_start}")
            summary = WeeklySummary.create_or_update(
                session=session,
                week_start_date=week_start,
                content=test_content
            )
            session.commit()
            print(f"✓ Created weekly summary with ID: {summary.id}")
            
            # Test 2: Retrieve weekly summary
            print(f"\n2. Retrieving weekly summary for week starting {week_start}")
            retrieved = WeeklySummary.get_by_week_start(session, week_start)
            if retrieved:
                print(f"✓ Retrieved summary: {retrieved.content[:50]}...")
                print(f"  Week start: {retrieved.week_start_date}")
                print(f"  Created: {retrieved.created_at}")
            else:
                print("✗ Failed to retrieve summary")
                return False
            
            # Test 3: Update existing weekly summary
            print(f"\n3. Updating weekly summary")
            updated_content = test_content + "\n\n更新的内容。"
            updated_summary = WeeklySummary.create_or_update(
                session=session,
                week_start_date=week_start,
                content=updated_content
            )
            session.commit()
            print(f"✓ Updated summary content length: {len(updated_summary.content)}")
            
            # Test 4: Verify update
            print(f"\n4. Verifying update")
            final_summary = WeeklySummary.get_by_week_start(session, week_start)
            if final_summary and "更新的内容" in final_summary.content:
                print("✓ Update verified successfully")
            else:
                print("✗ Update verification failed")
                return False
            
            # Test 5: Test with different week
            print(f"\n5. Testing with different week")
            next_week = week_start + timedelta(days=7)
            next_week_summary = WeeklySummary.create_or_update(
                session=session,
                week_start_date=next_week,
                content="下周的总结内容"
            )
            session.commit()
            print(f"✓ Created summary for next week: {next_week}")
            
            # Test 6: List all summaries
            print(f"\n6. Listing all weekly summaries")
            all_summaries = session.query(WeeklySummary).filter(
                WeeklySummary.is_deleted == False
            ).all()
            print(f"✓ Found {len(all_summaries)} weekly summaries:")
            for summary in all_summaries:
                print(f"  - Week {summary.week_start_date.date()}: {len(summary.content)} chars")
        
        print(f"\n✓ All tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_weekly_summary_service():
    """Test WeeklySummaryService"""
    print("\n\nTesting Weekly Summary Service")
    print("=" * 40)
    
    try:
        from modules.task_manager.weekly_summary_service import WeeklySummaryService
        
        service = WeeklySummaryService()
        
        # Test data
        today = date.today()
        days_since_monday = today.weekday()
        week_start = today - timedelta(days=days_since_monday)
        
        test_content = "服务测试内容\n多行文本测试"
        
        # Test save
        print(f"1. Testing save_weekly_summary for week {week_start}")
        success = service.save_weekly_summary(week_start, test_content)
        if success:
            print("✓ Save successful")
        else:
            print("✗ Save failed")
            return False
        
        # Test get
        print(f"2. Testing get_weekly_summary for week {week_start}")
        retrieved_content = service.get_weekly_summary(week_start)
        if retrieved_content == test_content:
            print("✓ Retrieval successful")
        else:
            print(f"✗ Retrieval failed. Expected: {test_content}, Got: {retrieved_content}")
            return False
        
        # Test update
        print(f"3. Testing update")
        updated_content = test_content + "\n更新内容"
        success = service.save_weekly_summary(week_start, updated_content)
        if success:
            retrieved_updated = service.get_weekly_summary(week_start)
            if retrieved_updated == updated_content:
                print("✓ Update successful")
            else:
                print("✗ Update verification failed")
                return False
        else:
            print("✗ Update failed")
            return False
        
        print("✓ All service tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Weekly Summary Test Suite")
    print("=" * 50)
    
    # Run tests
    db_test_passed = test_weekly_summary_operations()
    service_test_passed = test_weekly_summary_service()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"Database Operations: {'PASSED' if db_test_passed else 'FAILED'}")
    print(f"Service Operations: {'PASSED' if service_test_passed else 'FAILED'}")
    
    if db_test_passed and service_test_passed:
        print("\n🎉 All tests passed! Weekly summary functionality is working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
