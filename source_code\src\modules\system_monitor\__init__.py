"""
System Monitor Module for Personal Manager System

This module provides system monitoring functionality including:
- Real-time system resource monitoring (CPU, Memory, Disk, Network)
- Process monitoring and management
- Hardware information display
- System alerts and notifications
- Performance history tracking
"""

from .system_service import SystemService

# Import GUI components only when needed
try:
    from .system_monitor_widget import SystemMonitorWidget
    __all__ = ['SystemMonitorWidget', 'SystemService']
except ImportError:
    # PyQt6 not available, only export service
    __all__ = ['SystemService']