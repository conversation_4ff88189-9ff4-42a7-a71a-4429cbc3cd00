"""
学习时间追踪服务

提供学习时间记录、统计、分析等核心业务逻辑
"""

import csv
import json
from datetime import datetime, timedelta, date
from typing import List, Dict, Optional, Any, Tuple
from pathlib import Path

from sqlalchemy import and_, or_, func, desc
from sqlalchemy.orm import Session

from core.database import get_session
from core.logger import LoggerMixin
from models.study_models import (
    StudySubject, StudySession, StudyGoal, StudyBreak,
    StudyStatus, GoalType
)


class StudyService(LoggerMixin):
    """学习时间追踪服务类"""
    
    def __init__(self):
        super().__init__()
        self.current_session: Optional[StudySession] = None
        self.current_break: Optional[StudyBreak] = None
        # 添加简单的内存缓存来跟踪当前会话信息
        self.current_session_cache = {
            'id': None,
            'start_time': None,
            'status': None,
            'subject_id': None,
            'pause_start_time': None,  # 当前暂停开始时间
            'total_break_seconds': 0   # 累计暂停时间（秒）
        }
    
    # ==================== 科目管理 ====================
    
    def create_subject(self, name: str, description: str = None, 
                      color: str = None, icon: str = None) -> Optional[StudySubject]:
        """创建学习科目"""
        try:
            with get_session() as session:
                # 检查科目名称是否已存在
                existing = session.query(StudySubject).filter(
                    StudySubject.name == name,
                    StudySubject.is_deleted == False
                ).first()
                
                if existing:
                    self.logger.warning(f"Subject '{name}' already exists")
                    return None
                
                subject = StudySubject(
                    name=name,
                    description=description,
                    color=color or "#3498db",
                    icon=icon or "book"
                )
                
                session.add(subject)
                session.commit()
                session.refresh(subject)
                
                self.logger.info(f"Created subject: {name}")
                return subject
                
        except Exception as e:
            self.logger.error(f"Error creating subject: {e}")
            return None
    
    def get_subjects(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """获取学习科目列表（返回字典格式以避免会话问题）"""
        try:
            with get_session() as session:
                query = session.query(StudySubject).filter(
                    StudySubject.is_deleted == False
                )

                if active_only:
                    query = query.filter(StudySubject.is_active == True)

                subjects = query.order_by(StudySubject.sort_order, StudySubject.name).all()

                # 转换为字典格式以避免会话绑定问题
                subject_dicts = []
                for subject in subjects:
                    try:
                        subject_dict = {
                            'id': subject.id,
                            'name': subject.name,
                            'description': subject.description or '',
                            'color': subject.color or '#3498db',
                            'icon': subject.icon or '',
                            'is_active': subject.is_active,
                            'sort_order': subject.sort_order or 0
                        }
                        subject_dicts.append(subject_dict)
                    except Exception as e:
                        self.logger.error(f"Error converting subject to dict: {e}")
                        # 添加一个默认的科目记录
                        subject_dicts.append({
                            'id': 'unknown',
                            'name': '数据加载失败',
                            'description': '',
                            'color': '#3498db',
                            'icon': '',
                            'is_active': True,
                            'sort_order': 0
                        })

                return subject_dicts

        except Exception as e:
            self.logger.error(f"Error getting subjects: {e}")
            return []
    
    def update_subject(self, subject_id: str, **kwargs) -> bool:
        """更新学习科目"""
        try:
            with get_session() as session:
                subject = session.query(StudySubject).filter(
                    StudySubject.id == subject_id,
                    StudySubject.is_deleted == False
                ).first()
                
                if not subject:
                    return False
                
                for key, value in kwargs.items():
                    if hasattr(subject, key):
                        setattr(subject, key, value)
                
                session.commit()
                self.logger.info(f"Updated subject: {subject_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error updating subject: {e}")
            return False
    
    def delete_subject(self, subject_id: str) -> bool:
        """删除学习科目（软删除）"""
        try:
            with get_session() as session:
                subject = session.query(StudySubject).filter(
                    StudySubject.id == subject_id
                ).first()
                
                if not subject:
                    return False
                
                subject.soft_delete()
                session.commit()
                
                self.logger.info(f"Deleted subject: {subject_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error deleting subject: {e}")
            return False
    
    # ==================== 学习会话管理 ====================
    
    def start_session(self, subject_id: str, title: str = None, 
                     planned_duration: int = None) -> Optional[StudySession]:
        """开始学习会话"""
        try:
            # 如果有正在进行的会话，先结束它
            if self.current_session and self.current_session.status == StudyStatus.ACTIVE:
                self.complete_session()
            
            with get_session() as session:
                study_session = StudySession(
                    subject_id=subject_id,
                    title=title,
                    planned_duration_minutes=planned_duration,
                    start_time=datetime.now(),  # 使用本地时间而不是UTC时间
                    status=StudyStatus.ACTIVE
                )
                
                session.add(study_session)
                session.commit()
                session.refresh(study_session)
                
                self.current_session = study_session

                # 更新缓存
                self.current_session_cache = {
                    'id': study_session.id,
                    'start_time': study_session.start_time,
                    'status': 'active',
                    'subject_id': study_session.subject_id,
                    'pause_start_time': None,
                    'total_break_seconds': 0
                }

                self.logger.info(f"Started study session for subject: {subject_id}")
                return study_session
                
        except Exception as e:
            self.logger.error(f"Error starting session: {e}")
            return None
    
    def pause_session(self) -> bool:
        """暂停当前学习会话"""
        try:
            # 检查缓存中的会话状态
            if (self.current_session_cache['id'] is None or
                self.current_session_cache['status'] != 'active'):
                return False

            with get_session() as session:
                # 直接通过ID查询会话
                study_session = session.query(StudySession).filter(
                    StudySession.id == self.current_session_cache['id']
                ).first()

                if study_session:
                    study_session.pause_session()
                    session.commit()

                    # 更新缓存状态，记录暂停开始时间
                    from datetime import datetime
                    self.current_session_cache['status'] = 'paused'
                    self.current_session_cache['pause_start_time'] = datetime.now()

                    self.logger.info("Paused study session")
                    return True
                else:
                    return False

        except Exception as e:
            self.logger.error(f"Error pausing session: {e}")
            return False
    
    def resume_session(self) -> bool:
        """恢复学习会话"""
        try:
            # 检查缓存中的会话状态
            if (self.current_session_cache['id'] is None or
                self.current_session_cache['status'] != 'paused'):
                return False

            with get_session() as session:
                # 直接通过ID查询会话
                study_session = session.query(StudySession).filter(
                    StudySession.id == self.current_session_cache['id']
                ).first()

                if study_session:
                    study_session.resume_session()
                    session.commit()

                    # 计算暂停时长并累加
                    from datetime import datetime
                    if self.current_session_cache['pause_start_time']:
                        pause_duration = datetime.now() - self.current_session_cache['pause_start_time']
                        pause_seconds = int(pause_duration.total_seconds())
                        self.current_session_cache['total_break_seconds'] += pause_seconds

                    # 更新缓存状态
                    self.current_session_cache['status'] = 'active'
                    self.current_session_cache['pause_start_time'] = None

                    self.logger.info("Resumed study session")
                    return True
                else:
                    return False

        except Exception as e:
            self.logger.error(f"Error resuming session: {e}")
            return False
    
    def complete_session(self, notes: str = None, focus_rating: int = None,
                        difficulty_rating: int = None, satisfaction_rating: int = None) -> bool:
        """完成学习会话"""
        try:
            if not self.current_session:
                return False
            
            with get_session() as session:
                study_session = session.merge(self.current_session)
                study_session.complete_session()
                
                # 更新评分和笔记
                if notes:
                    study_session.notes = notes
                if focus_rating:
                    study_session.focus_rating = focus_rating
                if difficulty_rating:
                    study_session.difficulty_rating = difficulty_rating
                if satisfaction_rating:
                    study_session.satisfaction_rating = satisfaction_rating
                
                session.commit()
                
                # 更新科目统计
                self._update_subject_statistics(study_session.subject_id, session)
                
                self.current_session = None

                # 清除缓存
                self.current_session_cache = {
                    'id': None,
                    'start_time': None,
                    'status': None,
                    'subject_id': None,
                    'pause_start_time': None,
                    'total_break_seconds': 0
                }

                self.logger.info("Completed study session")
                return True
                
        except Exception as e:
            self.logger.error(f"Error completing session: {e}")
            return False
    
    def cancel_session(self) -> bool:
        """取消学习会话"""
        try:
            if not self.current_session:
                return False
            
            with get_session() as session:
                study_session = session.merge(self.current_session)
                study_session.cancel_session()
                session.commit()
                
                self.current_session = None

                # 清除缓存
                self.current_session_cache = {
                    'id': None,
                    'start_time': None,
                    'status': None,
                    'subject_id': None,
                    'pause_start_time': None,
                    'total_break_seconds': 0
                }

                self.logger.info("Cancelled study session")
                return True
                
        except Exception as e:
            self.logger.error(f"Error cancelling session: {e}")
            return False
    
    def get_current_session(self) -> Optional[StudySession]:
        """获取当前学习会话"""
        try:
            # 如果有缓存的会话，先检查它是否仍然有效
            if self.current_session:
                try:
                    # 尝试访问会话属性，如果失败说明会话已失效
                    _ = self.current_session.id
                    _ = self.current_session.start_time
                    return self.current_session
                except:
                    # 会话已失效，清除缓存
                    self.current_session = None

            # 从数据库重新获取当前活动会话
            with get_session() as session:
                current_session = session.query(StudySession).filter(
                    and_(
                        StudySession.status == StudyStatus.ACTIVE,
                        StudySession.is_deleted == False
                    )
                ).order_by(desc(StudySession.start_time)).first()

                if current_session:
                    # 创建一个新的会话对象，避免会话绑定问题
                    session_data = {
                        'id': current_session.id,
                        'subject_id': current_session.subject_id,
                        'title': current_session.title,
                        'start_time': current_session.start_time,
                        'end_time': current_session.end_time,
                        'status': current_session.status,
                        'planned_duration_minutes': current_session.planned_duration_minutes
                    }

                    # 更新缓存
                    self.current_session = current_session
                    return current_session

                return None

        except Exception as e:
            self.logger.error(f"Error getting current session: {e}")
            return None
    
    def get_current_duration(self) -> int:
        """获取当前会话的学习时长（分钟）"""
        if self.current_session:
            return self.current_session.get_current_duration()
        return 0

    def get_current_duration_seconds(self) -> int:
        """获取当前会话的学习时长（秒）"""
        try:
            # 使用缓存而不是ORM对象
            if (self.current_session_cache['id'] is not None and
                self.current_session_cache['start_time'] is not None):

                from datetime import datetime
                start_time = self.current_session_cache['start_time']

                # 确保start_time是datetime对象
                if isinstance(start_time, str):
                    # 如果是字符串，尝试解析
                    start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))

                # 计算总时长
                if self.current_session_cache['status'] == 'active':
                    # 活动状态：从开始到现在的时间
                    now = datetime.now()
                    total_duration = now - start_time
                elif self.current_session_cache['status'] == 'paused':
                    # 暂停状态：从开始到暂停开始的时间
                    pause_start = self.current_session_cache['pause_start_time']
                    if pause_start:
                        total_duration = pause_start - start_time
                    else:
                        # 如果没有暂停开始时间，使用当前时间
                        now = datetime.now()
                        total_duration = now - start_time
                else:
                    # 其他状态，返回0
                    return 0

                total_seconds = int(total_duration.total_seconds())

                # 减去累计暂停时间
                net_seconds = total_seconds - self.current_session_cache['total_break_seconds']

                # 确保返回非负数
                return max(0, net_seconds)
            return 0
        except Exception as e:
            self.logger.error(f"Error calculating current duration: {e}")
            return 0
    
    # ==================== 休息管理 ====================
    
    def start_break(self, break_type: str = "short") -> bool:
        """开始休息"""
        try:
            if not self.current_session or self.current_session.status != StudyStatus.ACTIVE:
                return False
            
            # 暂停当前会话
            self.pause_session()
            
            with get_session() as session:
                study_break = StudyBreak(
                    session_id=self.current_session.id,
                    break_type=break_type
                )
                study_break.start_break()
                
                session.add(study_break)
                session.commit()
                session.refresh(study_break)
                
                self.current_break = study_break
                self.logger.info(f"Started {break_type} break")
                return True
                
        except Exception as e:
            self.logger.error(f"Error starting break: {e}")
            return False
    
    def end_break(self) -> bool:
        """结束休息"""
        try:
            if not self.current_break:
                return False
            
            with get_session() as session:
                study_break = session.merge(self.current_break)
                study_break.end_break()
                session.commit()
                
                # 更新会话的休息时长
                if self.current_session:
                    study_session = session.merge(self.current_session)
                    study_session.break_minutes += study_break.duration_minutes
                    session.commit()
                
                self.current_break = None
                
                # 恢复学习会话
                self.resume_session()
                
                self.logger.info("Ended break")
                return True

        except Exception as e:
            self.logger.error(f"Error ending break: {e}")
            return False

    # ==================== 数据查询和统计 ====================

    def get_sessions(self, subject_id: str = None, start_date: date = None,
                    end_date: date = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取学习会话列表（返回字典格式以避免会话问题）"""
        try:
            from sqlalchemy.orm import joinedload

            with get_session() as session:
                # 预加载关联的科目对象
                query = session.query(StudySession).options(
                    joinedload(StudySession.subject)
                ).filter(
                    StudySession.is_deleted == False
                )

                if subject_id:
                    query = query.filter(StudySession.subject_id == subject_id)

                if start_date:
                    query = query.filter(StudySession.start_time >= start_date)

                if end_date:
                    end_datetime = datetime.combine(end_date, datetime.max.time())
                    query = query.filter(StudySession.start_time <= end_datetime)

                sessions = query.order_by(desc(StudySession.start_time)).limit(limit).all()

                # 转换为字典格式以避免会话绑定问题
                session_dicts = []
                for session_obj in sessions:
                    try:
                        session_dict = {
                            'id': session_obj.id,
                            'title': session_obj.title or '',
                            'start_time': session_obj.start_time,
                            'end_time': session_obj.end_time,
                            'duration_minutes': session_obj.duration_minutes or 0,
                            'status': session_obj.status.value if session_obj.status else 'unknown',
                            'focus_rating': session_obj.focus_rating,
                            'difficulty_rating': session_obj.difficulty_rating,
                            'satisfaction_rating': session_obj.satisfaction_rating,
                            'notes': session_obj.notes or '',
                            'subject_name': session_obj.subject.name if session_obj.subject else '未知',
                            'subject_id': session_obj.subject_id
                        }
                        session_dicts.append(session_dict)
                    except Exception as e:
                        self.logger.error(f"Error converting session to dict: {e}")
                        # 添加一个默认的会话记录
                        session_dicts.append({
                            'id': 'unknown',
                            'title': '数据加载失败',
                            'start_time': datetime.now(),
                            'end_time': None,
                            'duration_minutes': 0,
                            'status': 'unknown',
                            'focus_rating': None,
                            'difficulty_rating': None,
                            'satisfaction_rating': None,
                            'notes': '',
                            'subject_name': '未知',
                            'subject_id': None
                        })

                return session_dicts

        except Exception as e:
            self.logger.error(f"Error getting sessions: {e}")
            return []

    def get_daily_statistics(self, target_date: date = None) -> Dict[str, Any]:
        """获取日学习统计"""
        if target_date is None:
            target_date = date.today()

        try:
            from datetime import datetime
            with get_session() as session:
                start_datetime = datetime.combine(target_date, datetime.min.time())
                end_datetime = datetime.combine(target_date, datetime.max.time())

                # 查询所有相关会话（包括已完成和正在进行的）
                sessions = session.query(StudySession).filter(
                    and_(
                        StudySession.start_time >= start_datetime,
                        StudySession.start_time <= end_datetime,
                        StudySession.status.in_([StudyStatus.COMPLETED, StudyStatus.ACTIVE, StudyStatus.PAUSED]),
                        StudySession.is_deleted == False
                    )
                ).all()

                # 计算总时长，包括当前正在进行的会话
                total_minutes = 0
                for s in sessions:
                    if s.status == StudyStatus.COMPLETED:
                        # 已完成的会话使用记录的时长
                        total_minutes += s.duration_minutes
                    elif s.status in [StudyStatus.ACTIVE, StudyStatus.PAUSED]:
                        # 正在进行的会话计算实时时长
                        if s.id == self.current_session_cache.get('id'):
                            # 使用缓存计算当前会话时长
                            current_seconds = self.get_current_duration_seconds()
                            current_minutes = current_seconds // 60
                            total_minutes += current_minutes
                        else:
                            # 其他活动会话（理论上不应该存在）
                            if s.start_time:
                                duration = datetime.now() - s.start_time
                                session_minutes = int(duration.total_seconds() // 60)
                                total_minutes += session_minutes
                total_sessions = len(sessions)

                # 按科目统计
                subject_stats = {}
                for s in sessions:
                    subject_name = s.subject.name if s.subject else "未知"
                    if subject_name not in subject_stats:
                        subject_stats[subject_name] = {
                            'sessions': 0,
                            'minutes': 0,
                            'color': s.subject.color if s.subject else "#cccccc"
                        }
                    subject_stats[subject_name]['sessions'] += 1

                    # 计算会话时长
                    if s.status == StudyStatus.COMPLETED:
                        subject_stats[subject_name]['minutes'] += s.duration_minutes
                    elif s.status in [StudyStatus.ACTIVE, StudyStatus.PAUSED]:
                        if s.id == self.current_session_cache.get('id'):
                            # 当前会话使用实时时长
                            current_seconds = self.get_current_duration_seconds()
                            current_minutes = current_seconds // 60
                            subject_stats[subject_name]['minutes'] += current_minutes
                        else:
                            # 其他活动会话
                            if s.start_time:
                                duration = datetime.now() - s.start_time
                                session_minutes = int(duration.total_seconds() // 60)
                                subject_stats[subject_name]['minutes'] += session_minutes

                return {
                    'date': target_date,
                    'total_minutes': total_minutes,
                    'total_sessions': total_sessions,
                    'average_session_minutes': total_minutes / total_sessions if total_sessions > 0 else 0,
                    'subject_stats': subject_stats,
                    'sessions': sessions
                }

        except Exception as e:
            self.logger.error(f"Error getting daily statistics: {e}")
            return {}

    def get_hourly_statistics(self, target_date: date = None) -> Dict[int, int]:
        """获取24小时学习时间分布统计

        Args:
            target_date: 目标日期，默认为今天

        Returns:
            Dict[int, int]: 小时数据，格式为 {hour: minutes}
                           hour: 0-23的小时数
                           minutes: 该小时内的学习分钟数
        """
        if target_date is None:
            target_date = date.today()

        try:
            from datetime import datetime
            with get_session() as session:
                start_datetime = datetime.combine(target_date, datetime.min.time())
                end_datetime = datetime.combine(target_date, datetime.max.time())

                # 查询当天所有已完成的会话
                sessions = session.query(StudySession).filter(
                    and_(
                        StudySession.start_time >= start_datetime,
                        StudySession.start_time <= end_datetime,
                        StudySession.status == StudyStatus.COMPLETED,
                        StudySession.is_deleted == False
                    )
                ).all()

                # 初始化24小时数据
                hourly_data = {hour: 0 for hour in range(24)}

                # 统计每个小时的学习时间
                for session_obj in sessions:
                    if session_obj.start_time and session_obj.end_time:
                        # 计算会话的开始和结束时间
                        start_time = session_obj.start_time
                        end_time = session_obj.end_time

                        # 确保时间在目标日期内
                        if start_time.date() == target_date:
                            # 如果会话跨越多个小时，需要按小时分配时间
                            current_time = start_time

                            while current_time < end_time:
                                # 当前小时
                                current_hour = current_time.hour

                                # 计算当前小时的结束时间
                                hour_end = current_time.replace(minute=59, second=59, microsecond=999999)
                                if hour_end > end_time:
                                    hour_end = end_time

                                # 计算在当前小时内的学习时间（分钟）
                                duration_in_hour = (hour_end - current_time).total_seconds() / 60
                                hourly_data[current_hour] += int(duration_in_hour)

                                # 移动到下一个小时
                                current_time = (current_time + timedelta(hours=1)).replace(minute=0, second=0, microsecond=0)

                # 处理当前正在进行的会话
                if self.current_session_cache.get('id'):
                    current_session_id = self.current_session_cache.get('id')
                    current_session = session.query(StudySession).filter(
                        StudySession.id == current_session_id,
                        StudySession.status.in_([StudyStatus.ACTIVE, StudyStatus.PAUSED])
                    ).first()

                    if current_session and current_session.start_time:
                        start_time = current_session.start_time
                        current_time = datetime.now()

                        # 确保是今天的会话
                        if start_time.date() == target_date:
                            # 计算当前会话在各个小时的分布
                            session_current_time = start_time

                            while session_current_time < current_time:
                                current_hour = session_current_time.hour

                                # 计算当前小时的结束时间
                                hour_end = session_current_time.replace(minute=59, second=59, microsecond=999999)
                                if hour_end > current_time:
                                    hour_end = current_time

                                # 计算在当前小时内的学习时间（分钟）
                                duration_in_hour = (hour_end - session_current_time).total_seconds() / 60
                                hourly_data[current_hour] += int(duration_in_hour)

                                # 移动到下一个小时
                                session_current_time = (session_current_time + timedelta(hours=1)).replace(minute=0, second=0, microsecond=0)

                return hourly_data

        except Exception as e:
            self.logger.error(f"Error getting hourly statistics: {e}")
            return {hour: 0 for hour in range(24)}

    def get_weekly_statistics(self, target_date: date = None) -> Dict[str, Any]:
        """获取周学习统计"""
        if target_date is None:
            target_date = date.today()

        # 计算周的开始和结束日期
        start_of_week = target_date - timedelta(days=target_date.weekday())
        end_of_week = start_of_week + timedelta(days=6)

        try:
            with get_session() as session:
                start_datetime = datetime.combine(start_of_week, datetime.min.time())
                end_datetime = datetime.combine(end_of_week, datetime.max.time())

                sessions = session.query(StudySession).filter(
                    and_(
                        StudySession.start_time >= start_datetime,
                        StudySession.start_time <= end_datetime,
                        StudySession.status == StudyStatus.COMPLETED,
                        StudySession.is_deleted == False
                    )
                ).all()

                total_minutes = sum(s.duration_minutes for s in sessions)
                total_sessions = len(sessions)

                # 按日期统计
                daily_stats = {}
                for i in range(7):
                    current_date = start_of_week + timedelta(days=i)
                    daily_stats[current_date.strftime('%Y-%m-%d')] = {
                        'date': current_date,
                        'minutes': 0,
                        'sessions': 0
                    }

                for session in sessions:
                    session_date = session.start_time.date().strftime('%Y-%m-%d')
                    if session_date in daily_stats:
                        daily_stats[session_date]['minutes'] += session.duration_minutes
                        daily_stats[session_date]['sessions'] += 1

                return {
                    'start_date': start_of_week,
                    'end_date': end_of_week,
                    'total_minutes': total_minutes,
                    'total_sessions': total_sessions,
                    'average_daily_minutes': total_minutes / 7,
                    'daily_stats': daily_stats,
                    'sessions': sessions
                }

        except Exception as e:
            self.logger.error(f"Error getting weekly statistics: {e}")
            return {}

    def get_monthly_statistics(self, year: int = None, month: int = None) -> Dict[str, Any]:
        """获取月学习统计"""
        if year is None or month is None:
            today = date.today()
            year = today.year
            month = today.month

        try:
            with get_session() as session:
                start_date = date(year, month, 1)
                if month == 12:
                    end_date = date(year + 1, 1, 1) - timedelta(days=1)
                else:
                    end_date = date(year, month + 1, 1) - timedelta(days=1)

                start_datetime = datetime.combine(start_date, datetime.min.time())
                end_datetime = datetime.combine(end_date, datetime.max.time())

                sessions = session.query(StudySession).filter(
                    and_(
                        StudySession.start_time >= start_datetime,
                        StudySession.start_time <= end_datetime,
                        StudySession.status == StudyStatus.COMPLETED,
                        StudySession.is_deleted == False
                    )
                ).all()

                total_minutes = sum(s.duration_minutes for s in sessions)
                total_sessions = len(sessions)

                return {
                    'year': year,
                    'month': month,
                    'start_date': start_date,
                    'end_date': end_date,
                    'total_minutes': total_minutes,
                    'total_sessions': total_sessions,
                    'average_daily_minutes': total_minutes / (end_date - start_date).days if (end_date - start_date).days > 0 else 0,
                    'sessions': sessions
                }

        except Exception as e:
            self.logger.error(f"Error getting monthly statistics: {e}")
            return {}

    # ==================== 目标管理 ====================

    def create_goal(self, title: str, goal_type: GoalType, target_minutes: int,
                   subject_id: str = None, target_sessions: int = None,
                   start_date: date = None, end_date: date = None) -> Optional[StudyGoal]:
        """创建学习目标"""
        try:
            if start_date is None:
                start_date = date.today()

            if end_date is None:
                if goal_type == GoalType.DAILY:
                    end_date = start_date
                elif goal_type == GoalType.WEEKLY:
                    end_date = start_date + timedelta(days=6)
                elif goal_type == GoalType.MONTHLY:
                    if start_date.month == 12:
                        end_date = date(start_date.year + 1, 1, 1) - timedelta(days=1)
                    else:
                        end_date = date(start_date.year, start_date.month + 1, 1) - timedelta(days=1)

            with get_session() as session:
                goal = StudyGoal(
                    title=title,
                    goal_type=goal_type,
                    target_minutes=target_minutes,
                    target_sessions=target_sessions,
                    subject_id=subject_id,
                    start_date=datetime.combine(start_date, datetime.min.time()),
                    end_date=datetime.combine(end_date, datetime.max.time())
                )

                session.add(goal)
                session.commit()
                session.refresh(goal)

                self.logger.info(f"Created goal: {title}")
                return goal

        except Exception as e:
            self.logger.error(f"Error creating goal: {e}")
            return None

    def get_active_goals(self, subject_id: str = None) -> List[StudyGoal]:
        """获取活跃的学习目标"""
        try:
            with get_session() as session:
                query = session.query(StudyGoal).filter(
                    and_(
                        StudyGoal.is_active == True,
                        StudyGoal.is_deleted == False,
                        StudyGoal.end_date >= datetime.utcnow()
                    )
                )

                if subject_id:
                    query = query.filter(StudyGoal.subject_id == subject_id)

                goals = query.order_by(StudyGoal.end_date).all()

                # 更新目标进度
                for goal in goals:
                    goal.update_progress()

                session.commit()
                return goals

        except Exception as e:
            self.logger.error(f"Error getting active goals: {e}")
            return []

    # ==================== 数据导出 ====================

    def export_to_csv(self, file_path: str, start_date: date = None,
                     end_date: date = None, subject_id: str = None) -> bool:
        """导出学习记录到CSV文件"""
        try:
            sessions = self.get_sessions(subject_id, start_date, end_date, limit=10000)

            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    '日期', '科目', '标题', '开始时间', '结束时间',
                    '学习时长(分钟)', '休息时长(分钟)', '状态', '专注度', '难度', '满意度', '笔记'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for session in sessions:
                    writer.writerow({
                        '日期': session.start_time.strftime('%Y-%m-%d'),
                        '科目': session.subject.name if session.subject else '',
                        '标题': session.title or '',
                        '开始时间': session.start_time.strftime('%H:%M:%S'),
                        '结束时间': session.end_time.strftime('%H:%M:%S') if session.end_time else '',
                        '学习时长(分钟)': session.duration_minutes,
                        '休息时长(分钟)': session.break_minutes,
                        '状态': session.status.value,
                        '专注度': session.focus_rating or '',
                        '难度': session.difficulty_rating or '',
                        '满意度': session.satisfaction_rating or '',
                        '笔记': session.notes or ''
                    })

            self.logger.info(f"Exported {len(sessions)} sessions to {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error exporting to CSV: {e}")
            return False

    def export_to_excel(self, file_path: str, start_date: date = None,
                       end_date: date = None, subject_id: str = None) -> bool:
        """导出学习记录到Excel文件"""
        try:
            # 尝试导入openpyxl
            try:
                from openpyxl import Workbook
                from openpyxl.styles import Font, PatternFill, Alignment
                from openpyxl.utils.dataframe import dataframe_to_rows
            except ImportError:
                self.logger.warning("openpyxl not available, falling back to CSV export")
                # 如果没有openpyxl，则导出为CSV
                csv_path = file_path.replace('.xlsx', '.csv').replace('.xls', '.csv')
                return self.export_to_csv(csv_path, start_date, end_date, subject_id)

            sessions = self.get_sessions(subject_id, start_date, end_date, limit=10000)

            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "学习记录"

            # 设置标题行
            headers = [
                '日期', '科目', '标题', '开始时间', '结束时间',
                '学习时长(分钟)', '休息时长(分钟)', '状态', '专注度', '难度', '满意度', '笔记'
            ]

            # 写入标题行
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")

            # 写入数据行
            for row, session in enumerate(sessions, 2):
                ws.cell(row=row, column=1, value=session.start_time.strftime('%Y-%m-%d'))
                ws.cell(row=row, column=2, value=session.subject.name if session.subject else '')
                ws.cell(row=row, column=3, value=session.title or '')
                ws.cell(row=row, column=4, value=session.start_time.strftime('%H:%M:%S'))
                ws.cell(row=row, column=5, value=session.end_time.strftime('%H:%M:%S') if session.end_time else '')
                ws.cell(row=row, column=6, value=session.duration_minutes)
                ws.cell(row=row, column=7, value=session.break_minutes)
                ws.cell(row=row, column=8, value=session.status.value)
                ws.cell(row=row, column=9, value=session.focus_rating or '')
                ws.cell(row=row, column=10, value=session.difficulty_rating or '')
                ws.cell(row=row, column=11, value=session.satisfaction_rating or '')
                ws.cell(row=row, column=12, value=session.notes or '')

            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # 保存文件
            wb.save(file_path)

            self.logger.info(f"Exported {len(sessions)} sessions to Excel: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error exporting to Excel: {e}")
            return False

    def export_statistics_report(self, file_path: str, start_date: date = None,
                               end_date: date = None) -> bool:
        """导出学习统计报告"""
        try:
            if end_date is None:
                end_date = date.today()
            if start_date is None:
                start_date = end_date - timedelta(days=30)

            # 获取统计数据
            sessions = self.get_sessions(start_date=start_date, end_date=end_date, limit=10000)
            subjects = self.get_subjects()

            # 生成报告内容
            report_lines = []
            report_lines.append("=" * 50)
            report_lines.append("学习时间统计报告")
            report_lines.append("=" * 50)
            report_lines.append(f"统计期间: {start_date} 至 {end_date}")
            report_lines.append("")

            # 总体统计
            total_minutes = sum(s.duration_minutes for s in sessions if s.status.value == 'completed')
            total_sessions = len([s for s in sessions if s.status.value == 'completed'])

            report_lines.append("总体统计:")
            report_lines.append(f"  总学习时间: {total_minutes // 60}小时{total_minutes % 60}分钟")
            report_lines.append(f"  总学习次数: {total_sessions}次")
            if total_sessions > 0:
                avg_minutes = total_minutes / total_sessions
                report_lines.append(f"  平均每次: {avg_minutes:.1f}分钟")
            report_lines.append("")

            # 按科目统计
            subject_stats = {}
            for session in sessions:
                if session.status.value == 'completed' and session.subject:
                    subject_name = session.subject.name
                    if subject_name not in subject_stats:
                        subject_stats[subject_name] = {'minutes': 0, 'sessions': 0}
                    subject_stats[subject_name]['minutes'] += session.duration_minutes
                    subject_stats[subject_name]['sessions'] += 1

            if subject_stats:
                report_lines.append("科目统计:")
                for subject, stats in sorted(subject_stats.items(), key=lambda x: x[1]['minutes'], reverse=True):
                    hours = stats['minutes'] // 60
                    minutes = stats['minutes'] % 60
                    report_lines.append(f"  {subject}: {hours}h{minutes}m ({stats['sessions']}次)")
                report_lines.append("")

            # 按日期统计
            daily_stats = {}
            for session in sessions:
                if session.status.value == 'completed':
                    date_str = session.start_time.strftime('%Y-%m-%d')
                    if date_str not in daily_stats:
                        daily_stats[date_str] = 0
                    daily_stats[date_str] += session.duration_minutes

            if daily_stats:
                report_lines.append("每日学习时间:")
                for date_str in sorted(daily_stats.keys()):
                    minutes = daily_stats[date_str]
                    hours = minutes // 60
                    mins = minutes % 60
                    report_lines.append(f"  {date_str}: {hours}h{mins}m")

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))

            self.logger.info(f"Exported statistics report to: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error exporting statistics report: {e}")
            return False

    # ==================== 辅助方法 ====================

    def _update_subject_statistics(self, subject_id: str, session: Session):
        """更新科目统计信息"""
        try:
            subject = session.query(StudySubject).filter(
                StudySubject.id == subject_id
            ).first()

            if subject:
                subject.update_statistics()
                session.commit()

        except Exception as e:
            self.logger.error(f"Error updating subject statistics: {e}")
