#!/usr/bin/env python3
"""
Core functionality test script for Personal Manager System

This script tests the core functionality without GUI dependencies.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_core_imports():
    """Test if core modules can be imported successfully"""
    print("Testing core imports...")
    
    try:
        # Test core modules
        from core.config import ConfigManager
        from core.logger import setup_logging, get_logger
        from core.database import DatabaseManager
        print("✓ Core modules imported successfully")
        
        # Test data models
        from models.base import BaseModel
        from models.file_models import FileBookmark, FileTag
        from models.task_models import Task, TaskCategory
        from models.system_models import SystemMetrics
        from models.app_models import Application, Note
        print("✓ Data models imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_configuration():
    """Test configuration management"""
    print("\nTesting configuration...")
    
    try:
        from core.config import ConfigManager
        
        config = ConfigManager()
        
        # Test basic configuration operations
        config.set('test.key', 'test_value')
        value = config.get('test.key')
        assert value == 'test_value', f"Expected 'test_value', got '{value}'"
        
        # Test default values
        default_value = config.get('non.existent.key', 'default')
        assert default_value == 'default', f"Expected 'default', got '{default_value}'"
        
        # Test nested configuration
        app_name = config.get('app.name')
        assert app_name == 'Personal Manager', f"Expected 'Personal Manager', got '{app_name}'"
        
        print("✓ Configuration management working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_database():
    """Test database connectivity and table creation"""
    print("\nTesting database...")

    try:
        from core.config import ConfigManager
        from core.database import init_db_manager, get_session

        config = ConfigManager()
        db_manager = init_db_manager(config=config)

        print("✓ Database tables created successfully")

        # Test session creation
        with get_session() as session:
            # Simple query to test connection
            from sqlalchemy import text
            result = session.execute(text("SELECT 1")).scalar()
            assert result == 1, "Database query failed"

        print("✓ Database connectivity working correctly")
        return True

    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_models():
    """Test data model functionality"""
    print("\nTesting data models...")
    
    try:
        from core.database import get_session
        from models.task_models import Task, TaskCategory
        from datetime import datetime
        
        # Test task creation
        with get_session() as session:
            # Create a test category
            category = TaskCategory(
                name="Test Category",
                description="Test category for testing",
                color="#FF0000"
            )
            session.add(category)
            session.flush()
            
            # Create a test task
            task = Task(
                title="Test Task",
                description="Test task for testing",
                category_id=category.id,
                priority="medium",
                status="PENDING"
            )
            session.add(task)
            session.commit()
            
            # Verify task was created
            retrieved_task = Task.get_by_id(session, task.id)
            assert retrieved_task is not None, "Task was not created"
            assert retrieved_task.title == "Test Task", "Task title mismatch"
            
            print("✓ Data models working correctly")
            return True
        
    except Exception as e:
        print(f"✗ Model test failed: {e}")
        return False

def test_services():
    """Test service layer functionality (without GUI dependencies)"""
    print("\nTesting services...")

    try:
        # Test task service
        from modules.task_manager.task_service import TaskService
        task_service = TaskService()

        # Test category creation
        category = task_service.create_category("Test Service Category", "Test description")
        assert category is not None, "Failed to create category"

        # Test task creation
        task = task_service.create_task(
            title="Test Service Task",
            description="Test task created by service",
            category_id=category.id,
            priority="high"
        )
        assert task is not None, "Failed to create task"

        # Test task retrieval
        retrieved_task = task_service.get_task(task.id)
        assert retrieved_task is not None, "Failed to retrieve task"
        assert retrieved_task.title == "Test Service Task", "Task title mismatch"

        # Test task update
        success = task_service.update_task(task.id, status="in_progress")
        assert success, "Failed to update task"

        # Test task completion
        success = task_service.complete_task(task.id)
        assert success, "Failed to complete task"

        print("✓ Task service working correctly")

        # Test system service (basic functionality)
        try:
            from modules.system_monitor.system_service import SystemService
            system_service = SystemService()

            # Test basic system info retrieval
            system_info = system_service.get_system_info()
            assert isinstance(system_info, dict), "System info should be a dictionary"
            assert 'platform' in system_info, "System info should contain platform"

            print("✓ System service working correctly")
        except ImportError:
            print("⚠ System service requires psutil (skipping)")

        # Test file service (basic functionality)
        try:
            from modules.file_manager.file_service import FileService
            file_service = FileService()

            # Test basic file operations (without actual file system operations)
            home_path = str(Path.home())
            print(f"✓ File service initialized, home path: {home_path}")
        except ImportError:
            print("⚠ File service requires additional dependencies (skipping)")

        return True

    except Exception as e:
        print(f"✗ Service test failed: {e}")
        return False

def main():
    """Run all core tests"""
    print("Personal Manager System - Core Test Suite")
    print("=" * 50)
    
    tests = [
        test_core_imports,
        test_configuration,
        test_database,
        test_models,
        test_services
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All core tests passed! The application core functionality is working correctly.")
        print("\nNote: GUI functionality requires PyQt6 to be installed.")
        print("To install dependencies, run: pip install -r requirements.txt")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
