#!/usr/bin/env python3
"""
Test Script for New Weekly Task Manager Interface

This script tests the new weekly task management interface to ensure
all functionality works correctly.
"""

import sys
import os
from datetime import datetime, date, timedelta

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt

# Import the new task manager components
from modules.task_manager import TaskManagerWidget, TaskService


class TestMainWindow(QMainWindow):
    """Test main window for the new task manager interface"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the test UI"""
        self.setWindowTitle("测试新任务管理界面")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Create the new task manager widget
        self.task_manager = TaskManagerWidget()
        layout.addWidget(self.task_manager)
        
        # Set window properties
        self.setMinimumSize(1000, 700)


def test_task_service():
    """测试任务服务功能"""
    print("正在测试任务服务...")

    service = TaskService()

    # 测试创建任务
    test_task = service.create_task(
        title="测试任务",
        description="这是一个测试任务",
        priority="normal",
        due_date=datetime.now() + timedelta(days=1),
        tags=["测试", "新界面"]
    )

    if test_task:
        print(f"✓ 任务创建成功: {test_task.title}")

        # 测试更新任务
        success = service.update_task(test_task.id, title="更新的测试任务")
        if success:
            print("✓ 任务更新成功")
        else:
            print("✗ 任务更新失败")

        # 测试获取任务
        tasks = service.get_tasks(limit=10)
        print(f"✓ 获取到 {len(tasks)} 个任务")

        # 测试删除任务
        success = service.delete_task(test_task.id)
        if success:
            print("✓ 任务删除成功")
        else:
            print("✗ 任务删除失败")
    else:
        print("✗ 任务创建失败")


def main():
    """主测试函数"""
    print("开始新任务管理界面测试...")
    print("=" * 50)

    # 首先测试服务层
    test_task_service()

    print("\n" + "=" * 50)
    print("开始图形界面测试...")

    # 创建应用程序
    app = QApplication(sys.argv)

    # 创建并显示测试窗口
    window = TestMainWindow()
    window.show()

    print("图形界面测试窗口打开成功！")
    print("\n测试说明:")
    print("1. 检查日期和周显示是否正确")
    print("2. 测试周导航按钮（上周、本周、下周）")
    print("3. 测试在表格中添加新任务")
    print("4. 测试内联编辑任务标题")
    print("5. 测试右键菜单功能")
    print("6. 测试任务状态更新")
    print("7. 测试搜索和过滤功能")
    print("\n关闭窗口以结束测试。")
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
