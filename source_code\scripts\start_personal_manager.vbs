' Personal Manager VBScript Launcher
' This script starts the application without showing any console windows

Dim objShell, objFSO, scriptDir, pythonCmd

' Get the directory where this script is located
Set objFSO = CreateObject("Scripting.FileSystemObject")
scriptDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' Create shell object
Set objShell = CreateObject("WScript.Shell")

' Set working directory to script directory
objShell.CurrentDirectory = scriptDir

' Set environment variables for better Qt experience
objShell.Environment("Process")("QT_AUTO_SCREEN_SCALE_FACTOR") = "1"
objShell.Environment("Process")("QT_ENABLE_HIGHDPI_SCALING") = "1"
objShell.Environment("Process")("QT_SCALE_FACTOR_ROUNDING_POLICY") = "RoundPreferFloor"

' Build the command to run Python with main.py
pythonCmd = "pythonw.exe main.py"

' Run the command without showing a window (WindowStyle = 0 means hidden)
objShell.Run pythonCmd, 0, False

' Clean up objects
Set objShell = Nothing
Set objFSO = Nothing
