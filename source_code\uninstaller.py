#!/usr/bin/env python3
"""
Uninstaller - 造神计划
完整的卸载程序，安全终止所有进程并完全清理所有文件
"""

import os
import sys
import time
import shutil
import psutil
import winreg
from pathlib import Path
from typing import List, Dict, Any


class Uninstaller:
    """卸载程序 - 造神计划"""
    
    def __init__(self):
        self.app_name = "造神计划"
        self.app_id = "12345678-1234-1234-1234-123456789ABC"
        
        # 路径配置
        self.install_dir = Path(sys.executable).parent if getattr(sys, 'frozen', False) else Path(__file__).parent
        self.user_data_dir = Path.home() / "AppData" / "Roaming" / self.app_name
        self.local_data_dir = Path.home() / "AppData" / "Local" / self.app_name
        
        # 注册表路径
        self.registry_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{" + self.app_id + "}",
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
            r"SOFTWARE\Classes\Applications\造神计划.exe",
        ]
        
        self.terminated_processes = []
        self.deleted_files = []
        self.deleted_registry_keys = []
        self.errors = []
        
        print(f"造神计划卸载程序初始化")
    
    def find_all_related_processes(self) -> List[psutil.Process]:
        """查找所有相关进程"""
        related_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'exe']):
                try:
                    proc_info = proc.info
                    cmdline = proc_info.get('cmdline', [])
                    exe_path = proc_info.get('exe', '')
                    
                    if not cmdline:
                        continue
                    
                    cmdline_str = ' '.join(cmdline).lower()
                    exe_path_str = exe_path.lower() if exe_path else ''
                    
                    # 检查是否是相关进程
                    keywords = [
                        '造神计划',
                        'personal_manager',
                        'universal_widget_daemon',
                        'desktop_widget',
                        'study_floating',
                        'launcher.py',
                        'main.py'
                    ]
                    
                    is_related = False
                    for keyword in keywords:
                        if (keyword in cmdline_str or 
                            keyword in exe_path_str or
                            any(keyword in arg.lower() for arg in cmdline)):
                            is_related = True
                            break
                    
                    # 检查安装目录
                    if str(self.install_dir).lower() in exe_path_str:
                        is_related = True
                    
                    if is_related:
                        related_processes.append(proc)
                
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
        
        except Exception as e:
            self.errors.append(f"查找进程时出错: {e}")
        
        return related_processes
    
    def terminate_all_processes(self):
        """终止所有相关进程"""
        print("正在终止所有相关进程...")
        
        related_processes = self.find_all_related_processes()
        
        if not related_processes:
            print("  未发现相关进程")
            return
        
        print(f"  发现 {len(related_processes)} 个相关进程")
        
        # 首先尝试优雅终止
        for proc in related_processes:
            try:
                print(f"  正在终止进程: {proc.pid} - {proc.name()}")
                proc.terminate()
                self.terminated_processes.append({
                    'pid': proc.pid,
                    'name': proc.name(),
                    'method': 'terminate'
                })
            except Exception as e:
                self.errors.append(f"终止进程 {proc.pid} 失败: {e}")
        
        # 等待进程结束
        print("  等待进程结束...")
        time.sleep(3)
        
        # 强制终止仍在运行的进程
        still_running = self.find_all_related_processes()
        for proc in still_running:
            try:
                print(f"  强制终止进程: {proc.pid} - {proc.name()}")
                proc.kill()
                self.terminated_processes.append({
                    'pid': proc.pid,
                    'name': proc.name(),
                    'method': 'kill'
                })
            except Exception as e:
                self.errors.append(f"强制终止进程 {proc.pid} 失败: {e}")
        
        print(f"  已终止 {len(self.terminated_processes)} 个进程")
    
    def clean_registry(self):
        """清理注册表"""
        print("正在清理注册表...")
        
        for reg_path in self.registry_paths:
            try:
                # 尝试删除HKEY_CURRENT_USER
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_ALL_ACCESS)
                    winreg.DeleteKey(key, "")
                    winreg.CloseKey(key)
                    print(f"  删除注册表项: HKCU\\{reg_path}")
                    self.deleted_registry_keys.append(f"HKCU\\{reg_path}")
                except FileNotFoundError:
                    pass  # 键不存在
                except Exception as e:
                    # 尝试删除特定值
                    if "Run" in reg_path:
                        try:
                            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_SET_VALUE)
                            winreg.DeleteValue(key, self.app_name)
                            winreg.CloseKey(key)
                            print(f"  删除注册表值: HKCU\\{reg_path}\\{self.app_name}")
                            self.deleted_registry_keys.append(f"HKCU\\{reg_path}\\{self.app_name}")
                        except FileNotFoundError:
                            pass
                        except Exception as e2:
                            self.errors.append(f"删除注册表值失败: {e2}")
                    else:
                        self.errors.append(f"删除注册表项失败: {e}")
                
                # 尝试删除HKEY_LOCAL_MACHINE
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path, 0, winreg.KEY_ALL_ACCESS)
                    winreg.DeleteKey(key, "")
                    winreg.CloseKey(key)
                    print(f"  删除注册表项: HKLM\\{reg_path}")
                    self.deleted_registry_keys.append(f"HKLM\\{reg_path}")
                except FileNotFoundError:
                    pass  # 键不存在
                except Exception as e:
                    pass  # 可能没有权限，这是正常的
            
            except Exception as e:
                self.errors.append(f"处理注册表路径 {reg_path} 时出错: {e}")
    
    def clean_files_and_directories(self):
        """清理文件和目录"""
        print("正在清理文件和目录...")
        
        # 要清理的目录列表
        directories_to_clean = [
            self.install_dir,
            self.user_data_dir,
            self.local_data_dir,
        ]
        
        # 要清理的特定文件
        files_to_clean = [
            Path.home() / f".{self.app_name.lower()}_lock",
            Path.home() / f".{self.app_name.lower()}_launcher_lock",
            Path.home() / "Desktop" / f"{self.app_name}.lnk",
            Path.home() / "AppData" / "Roaming" / "Microsoft" / "Windows" / "Start Menu" / "Programs" / f"{self.app_name}.lnk",
        ]
        
        # 清理文件
        for file_path in files_to_clean:
            try:
                if file_path.exists():
                    file_path.unlink()
                    print(f"  删除文件: {file_path}")
                    self.deleted_files.append(str(file_path))
            except Exception as e:
                self.errors.append(f"删除文件 {file_path} 失败: {e}")
        
        # 清理目录
        for dir_path in directories_to_clean:
            try:
                if dir_path.exists() and dir_path.is_dir():
                    # 特殊处理安装目录（可能包含卸载程序本身）
                    if dir_path == self.install_dir:
                        self.clean_install_directory(dir_path)
                    else:
                        shutil.rmtree(dir_path)
                        print(f"  删除目录: {dir_path}")
                        self.deleted_files.append(str(dir_path))
            except Exception as e:
                self.errors.append(f"删除目录 {dir_path} 失败: {e}")
    
    def clean_install_directory(self, install_dir: Path):
        """清理安装目录（避免删除正在运行的卸载程序）"""
        try:
            current_exe = Path(sys.executable) if getattr(sys, 'frozen', False) else Path(__file__)
            
            for item in install_dir.iterdir():
                try:
                    # 跳过当前运行的卸载程序
                    if item.samefile(current_exe):
                        continue
                    
                    if item.is_file():
                        item.unlink()
                        print(f"  删除文件: {item}")
                        self.deleted_files.append(str(item))
                    elif item.is_dir():
                        shutil.rmtree(item)
                        print(f"  删除目录: {item}")
                        self.deleted_files.append(str(item))
                
                except Exception as e:
                    self.errors.append(f"删除 {item} 失败: {e}")
            
            # 创建一个批处理文件来删除安装目录和卸载程序本身
            self.create_self_delete_script(install_dir)
        
        except Exception as e:
            self.errors.append(f"清理安装目录失败: {e}")
    
    def create_self_delete_script(self, install_dir: Path):
        """创建自删除脚本"""
        try:
            script_content = f'''@echo off
timeout /t 2 /nobreak >nul
del /f /q "{sys.executable if getattr(sys, 'frozen', False) else __file__}"
rmdir /s /q "{install_dir}"
del "%~f0"
'''
            
            script_path = Path.home() / "AppData" / "Local" / "Temp" / f"{self.app_name}_cleanup.bat"
            with open(script_path, 'w', encoding='gbk') as f:
                f.write(script_content)
            
            # 启动自删除脚本
            import subprocess
            subprocess.Popen(
                [str(script_path)],
                creationflags=subprocess.CREATE_NO_WINDOW,
                shell=True
            )
            
            print(f"  创建自删除脚本: {script_path}")
        
        except Exception as e:
            self.errors.append(f"创建自删除脚本失败: {e}")
    
    def ask_user_confirmation(self) -> bool:
        """询问用户确认"""
        print("=" * 50)
        print("造神计划卸载程序")
        print("=" * 50)
        print("此操作将：")
        print("1. 终止所有相关进程")
        print("2. 删除所有程序文件")
        print("3. 清理注册表项")
        print("4. 删除用户数据（可选）")
        print()
        
        while True:
            choice = input("是否继续卸载？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                return True
            elif choice in ['n', 'no', '否']:
                return False
            else:
                print("请输入 y 或 n")
    
    def ask_delete_user_data(self) -> bool:
        """询问是否删除用户数据"""
        print()
        print("用户数据包括：")
        print("- 配置文件")
        print("- 数据库文件")
        print("- 日志文件")
        print("- 个人设置")
        print()
        
        while True:
            choice = input("是否删除用户数据？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                return True
            elif choice in ['n', 'no', '否']:
                return False
            else:
                print("请输入 y 或 n")
    
    def show_summary(self):
        """显示卸载摘要"""
        print("=" * 50)
        print("卸载完成摘要")
        print("=" * 50)
        
        print(f"终止进程: {len(self.terminated_processes)} 个")
        for proc in self.terminated_processes:
            print(f"  - PID {proc['pid']}: {proc['name']} ({proc['method']})")
        
        print(f"\n删除文件/目录: {len(self.deleted_files)} 个")
        for file_path in self.deleted_files[:10]:  # 只显示前10个
            print(f"  - {file_path}")
        if len(self.deleted_files) > 10:
            print(f"  ... 还有 {len(self.deleted_files) - 10} 个文件/目录")
        
        print(f"\n清理注册表项: {len(self.deleted_registry_keys)} 个")
        for reg_key in self.deleted_registry_keys:
            print(f"  - {reg_key}")
        
        if self.errors:
            print(f"\n错误: {len(self.errors)} 个")
            for error in self.errors[:5]:  # 只显示前5个错误
                print(f"  - {error}")
            if len(self.errors) > 5:
                print(f"  ... 还有 {len(self.errors) - 5} 个错误")
        
        print("\n造神计划已成功卸载！")
    
    def run(self):
        """运行卸载程序"""
        try:
            # 询问用户确认
            if not self.ask_user_confirmation():
                print("卸载已取消")
                return
            
            print("\n开始卸载...")
            
            # 1. 终止所有进程
            self.terminate_all_processes()
            
            # 2. 清理注册表
            self.clean_registry()
            
            # 3. 清理文件和目录
            self.clean_files_and_directories()
            
            # 4. 显示摘要
            self.show_summary()
            
            print("\n按任意键退出...")
            input()
        
        except KeyboardInterrupt:
            print("\n卸载被用户中断")
        except Exception as e:
            print(f"\n卸载过程中发生错误: {e}")


def main():
    """主函数"""
    uninstaller = Uninstaller()
    uninstaller.run()


if __name__ == "__main__":
    main()