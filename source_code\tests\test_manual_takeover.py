#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试手动接管功能
验证桌面端应用启动小组件时能够停止自启动流程并重新打开
"""

import os
import sys
import time
import psutil
import subprocess
from pathlib import Path


def check_daemon_running():
    """检查守护进程是否在运行"""
    project_root = Path(__file__).parent
    pid_file = project_root / "daemon.pid"
    
    if not pid_file.exists():
        return False, None
    
    try:
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())
        
        if psutil.pid_exists(pid):
            process = psutil.Process(pid)
            cmdline = ' '.join(process.cmdline())
            if 'desktop_widget_daemon.py' in cmdline:
                return True, pid
    except:
        pass
    
    return False, None


def send_manual_takeover_command():
    """发送手动接管命令"""
    project_root = Path(__file__).parent
    command_file = project_root / "daemon_command.txt"
    
    try:
        with open(command_file, 'w', encoding='utf-8') as f:
            f.write("manual_takeover")
        print("✅ 手动接管命令已发送")
        return True
    except Exception as e:
        print(f"❌ 发送手动接管命令失败: {e}")
        return False


def start_auto_daemon():
    """启动自启动守护进程（模拟自启动）"""
    project_root = Path(__file__).parent
    daemon_script = project_root / "desktop_widget_daemon.py"
    
    try:
        print("🚀 启动自启动守护进程...")
        process = subprocess.Popen(
            [sys.executable, str(daemon_script)],  # 不使用 --manual 参数
            cwd=str(project_root),
            creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        
        # 等待启动
        for i in range(10):
            time.sleep(0.5)
            is_running, pid = check_daemon_running()
            if is_running:
                print(f"✅ 自启动守护进程启动成功，PID: {pid}")
                return True
        
        print("❌ 自启动守护进程启动失败")
        return False
        
    except Exception as e:
        print(f"❌ 启动自启动守护进程失败: {e}")
        return False


def wait_for_daemon_restart():
    """等待守护进程重启"""
    print("⏳ 等待守护进程重启...")
    
    # 等待一段时间让守护进程处理命令
    time.sleep(3)
    
    # 检查是否有新的守护进程
    is_running, new_pid = check_daemon_running()
    if is_running:
        print(f"✅ 检测到守护进程重启，新PID: {new_pid}")
        return True
    else:
        print("❌ 未检测到守护进程重启")
        return False


def check_daemon_log():
    """检查守护进程日志"""
    project_root = Path(__file__).parent
    log_file = project_root / "daemon.log"
    
    if not log_file.exists():
        print("❌ 日志文件不存在")
        return False
    
    try:
        # 读取最后几行日志
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找最近的手动接管相关日志
        recent_lines = lines[-20:] if len(lines) > 20 else lines
        
        manual_takeover_found = False
        for line in recent_lines:
            if "手动接管" in line or "manual_takeover" in line:
                print(f"📝 日志: {line.strip()}")
                manual_takeover_found = True
        
        if manual_takeover_found:
            print("✅ 在日志中找到手动接管相关记录")
            return True
        else:
            print("❌ 在日志中未找到手动接管相关记录")
            return False
            
    except Exception as e:
        print(f"❌ 读取日志失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("手动接管功能测试")
    print("=" * 60)
    
    # 步骤1：启动自启动守护进程
    print("\n📋 步骤1：启动自启动守护进程")
    if not start_auto_daemon():
        print("❌ 测试失败：无法启动自启动守护进程")
        return
    
    # 记录原始PID
    is_running, original_pid = check_daemon_running()
    print(f"📝 原始守护进程PID: {original_pid}")
    
    # 步骤2：等待一段时间让自启动流程开始
    print("\n📋 步骤2：等待自启动流程开始")
    print("⏳ 等待5秒...")
    time.sleep(5)
    
    # 步骤3：发送手动接管命令
    print("\n📋 步骤3：发送手动接管命令")
    if not send_manual_takeover_command():
        print("❌ 测试失败：无法发送手动接管命令")
        return
    
    # 步骤4：等待守护进程处理命令
    print("\n📋 步骤4：等待守护进程处理手动接管")
    if not wait_for_daemon_restart():
        print("❌ 测试失败：守护进程未重启")
        return
    
    # 步骤5：验证新的守护进程
    print("\n📋 步骤5：验证新的守护进程")
    is_running, new_pid = check_daemon_running()
    if is_running:
        if new_pid != original_pid:
            print(f"✅ 守护进程已重启：{original_pid} -> {new_pid}")
        else:
            print(f"⚠️ 守护进程PID未变化，可能是同一进程")
    else:
        print("❌ 新的守护进程未运行")
        return
    
    # 步骤6：检查日志
    print("\n📋 步骤6：检查日志记录")
    check_daemon_log()
    
    print("\n" + "=" * 60)
    print("✅ 手动接管功能测试完成")
    print("💡 如果看到手动接管相关日志，说明功能正常工作")
    print("=" * 60)


if __name__ == "__main__":
    main()
