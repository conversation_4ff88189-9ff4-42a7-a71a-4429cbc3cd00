"""
Widget Communication Manager - Handles inter-process communication for widgets
"""

import os
from pathlib import Path
from datetime import datetime
from typing import Optional
from PyQt6.QtCore import QObject, pyqtSignal

from core.logger import LoggerMixin


class WidgetCommunicationManager(QObject, LoggerMixin):
    """小组件通信管理器"""
    
    command_received = pyqtSignal(str)  # 接收到命令信号
    
    def __init__(self, widget_type: str, parent=None):
        super().__init__(parent)
        self.widget_type = widget_type
        
        # 设置通信文件路径
        self.project_root = self._get_project_root()
        self.setup_communication_files()
    
    def _get_project_root(self) -> Path:
        """获取项目根目录"""
        current_file = Path(__file__)
        # 从 src/modules/common/ 向上找到 source_code/
        return current_file.parent.parent.parent.parent
    
    def setup_communication_files(self):
        """设置通信文件路径"""
        # 命令文件：主应用 -> 守护进程
        self.daemon_command_file = self.project_root / f"{self.widget_type}_daemon_command.txt"
        
        # 显示命令文件：守护进程 -> 小组件
        self.show_command_file = self.project_root / f"{self.widget_type}_show_command.txt"
        
        # 状态文件：小组件 -> 主应用
        self.status_file = self.project_root.parent / f"{self.widget_type}_widget_status.txt"
    
    def check_commands(self):
        """检查是否有新命令"""
        try:
            # 检查显示命令
            if self.show_command_file.exists():
                with open(self.show_command_file, 'r', encoding='utf-8') as f:
                    command = f.read().strip()
                
                # 删除命令文件
                self.show_command_file.unlink()
                
                if command:
                    self.command_received.emit(command)
                    self.logger.debug(f"Received command: {command}")
        
        except Exception as e:
            # 静默处理错误，避免日志噪音
            pass
    
    def send_command_to_daemon(self, command: str) -> bool:
        """发送命令给守护进程"""
        try:
            with open(self.daemon_command_file, 'w', encoding='utf-8') as f:
                f.write(command)
            
            self.logger.info(f"Sent command to daemon: {command}")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to send command to daemon: {e}")
            return False
    
    def send_show_command(self, command: str = "show") -> bool:
        """发送显示命令给小组件"""
        try:
            with open(self.show_command_file, 'w', encoding='utf-8') as f:
                f.write(command)
            
            self.logger.info(f"Sent show command: {command}")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to send show command: {e}")
            return False
    
    def notify_closed(self):
        """通知主应用小组件已关闭"""
        try:
            # 确保父目录存在
            self.status_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                content = f"closed:{datetime.now().isoformat()}"
                f.write(content)
                f.flush()
            
            self.logger.info("Notified main app that widget is closed")
        
        except Exception as e:
            self.logger.warning(f"Failed to notify widget closed: {e}")
    
    def notify_status(self, status: str):
        """通知状态变化"""
        try:
            self.status_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                content = f"{status}:{datetime.now().isoformat()}"
                f.write(content)
                f.flush()
            
            self.logger.debug(f"Notified status: {status}")
        
        except Exception as e:
            self.logger.warning(f"Failed to notify status: {e}")
    
    def cleanup_files(self):
        """清理通信文件"""
        files_to_clean = [
            self.daemon_command_file,
            self.show_command_file,
            self.status_file
        ]
        
        for file_path in files_to_clean:
            try:
                if file_path.exists():
                    file_path.unlink()
            except Exception as e:
                self.logger.warning(f"Failed to cleanup file {file_path}: {e}")