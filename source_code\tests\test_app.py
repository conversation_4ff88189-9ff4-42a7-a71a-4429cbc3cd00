#!/usr/bin/env python3
"""
Test script for Personal Manager System

This script tests the basic functionality of the application.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """Test if all modules can be imported successfully"""
    print("Testing imports...")
    
    try:
        # Test core modules
        from core.config import ConfigManager
        from core.logger import setup_logging, get_logger
        from core.database import DatabaseManager
        print("✓ Core modules imported successfully")
        
        # Test data models
        from models.base import BaseModel
        from models.file_models import FileBookmark, FileTag
        from models.task_models import Task, TaskCategory
        from models.system_models import SystemMetrics
        from models.app_models import Application, Note
        print("✓ Data models imported successfully")
        
        # Test GUI modules
        from gui.main_window import MainWindow
        print("✓ GUI modules imported successfully")
        
        # Test feature modules
        from modules.file_manager import FileManagerWidget, FileService
        from modules.system_monitor import SystemMonitorWidget, SystemService
        from modules.task_manager import TaskManagerWidget, TaskService
        print("✓ Feature modules imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_configuration():
    """Test configuration management"""
    print("\nTesting configuration...")
    
    try:
        from core.config import ConfigManager
        config = ConfigManager()
        
        # Test basic configuration operations
        config.set('test.key', 'test_value')
        value = config.get('test.key')
        assert value == 'test_value', f"Expected 'test_value', got '{value}'"
        
        # Test default values
        default_value = config.get('non.existent.key', 'default')
        assert default_value == 'default', f"Expected 'default', got '{default_value}'"
        
        print("✓ Configuration management working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_database():
    """Test database connectivity and table creation"""
    print("\nTesting database...")
    
    try:
        from core.config import ConfigManager
        from core.database import DatabaseManager, init_db_manager

        config = ConfigManager()
        db_manager = init_db_manager(config)
        
        print("✓ Database initialized successfully")
        
        # Test session creation
        from core.database import get_session
        from sqlalchemy import text
        with get_session() as session:
            # Simple query to test connection
            result = session.execute(text("SELECT 1")).scalar()
            assert result == 1, "Database query failed"
        
        print("✓ Database connectivity working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_services():
    """Test service layer functionality"""
    print("\nTesting services...")
    
    try:
        # Test file service
        from modules.file_manager.file_service import FileService
        file_service = FileService()
        
        # Test basic file operations (without actual file system operations)
        home_path = str(Path.home())
        print(f"✓ File service initialized, home path: {home_path}")
        
        # Test system service
        from modules.system_monitor.system_service import SystemService
        system_service = SystemService()
        
        # Test basic system info retrieval
        system_info = system_service.get_system_info()
        assert isinstance(system_info, dict), "System info should be a dictionary"
        print("✓ System service working correctly")
        
        # Test task service
        from modules.task_manager.task_service import TaskService
        task_service = TaskService()
        print("✓ Task service initialized")
        
        return True
        
    except Exception as e:
        print(f"✗ Service test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Personal Manager System - Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_configuration,
        test_database,
        test_services
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
