"""
测试最终修复：HH:MM:SS时间显示和暂停功能
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件
from modules.study_tracker.study_floating_widget import StudyFloatingWidget


class FinalTestWindow(QMainWindow):
    """最终修复测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("最终修复测试：HH:MM:SS + 暂停功能")
        self.setGeometry(100, 100, 600, 500)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("测试最终修复功能")
        layout.addWidget(self.status_label)
        
        # 信息标签
        info_label = QLabel("""
最终修复测试：

✅ 修复内容：
1. 时间显示格式：始终显示为 HH:MM:SS
2. 暂停功能：修复暂停/继续按钮功能

🧪 测试步骤：
1. 点击"创建悬浮小组件"
2. 点击悬浮小组件的"开始"按钮
3. 观察时间显示格式：应该是 00:00:01, 00:00:02, 00:00:03...
4. 点击"暂停"按钮测试暂停功能
5. 点击"继续"按钮测试恢复功能
6. 点击"停止"按钮结束会话

预期结果：
- 时间格式：HH:MM:SS (00:00:01, 00:00:02...)
- 暂停功能：正常工作，无错误信息
- 按钮状态：根据会话状态正确切换
        """)
        layout.addWidget(info_label)
        
        # 控制按钮
        create_btn = QPushButton("创建悬浮小组件")
        create_btn.clicked.connect(self.create_floating_widget)
        layout.addWidget(create_btn)
        
        test_start_btn = QPushButton("测试开始功能")
        test_start_btn.clicked.connect(self.test_start)
        layout.addWidget(test_start_btn)
        
        test_pause_btn = QPushButton("测试暂停功能")
        test_pause_btn.clicked.connect(self.test_pause)
        layout.addWidget(test_pause_btn)
        
        test_resume_btn = QPushButton("测试继续功能")
        test_resume_btn.clicked.connect(self.test_resume)
        layout.addWidget(test_resume_btn)
        
        test_stop_btn = QPushButton("测试停止功能")
        test_stop_btn.clicked.connect(self.test_stop)
        layout.addWidget(test_stop_btn)
        
        # 状态显示
        self.time_display_label = QLabel("当前时间: 00:00:00")
        layout.addWidget(self.time_display_label)
        
        self.session_status_label = QLabel("会话状态: 无")
        layout.addWidget(self.session_status_label)
        
        # 悬浮小组件实例
        self.floating_widget = None
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_monitor)
        self.monitor_timer.start(1000)  # 每秒更新
    
    def create_floating_widget(self):
        """创建悬浮小组件"""
        try:
            if self.floating_widget is None:
                self.status_label.setText("正在创建悬浮小组件...")
                self.floating_widget = StudyFloatingWidget()
                
                # 连接信号
                self.floating_widget.start_study.connect(self.on_start_signal)
                self.floating_widget.pause_study.connect(self.on_pause_signal)
                self.floating_widget.resume_study.connect(self.on_resume_signal)
                self.floating_widget.stop_study.connect(self.on_stop_signal)
            
            self.floating_widget.show()
            self.floating_widget.raise_()
            self.floating_widget.activateWindow()
            self.status_label.setText("✅ 悬浮小组件已创建")
            print("✅ 悬浮小组件已创建")
            
        except Exception as e:
            self.status_label.setText(f"创建失败: {str(e)}")
            print(f"Error creating floating widget: {e}")
            import traceback
            traceback.print_exc()
    
    def test_start(self):
        """测试开始功能"""
        if self.floating_widget:
            print("🔥 测试开始功能...")
            self.floating_widget.start_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def test_pause(self):
        """测试暂停功能"""
        if self.floating_widget:
            print("⏸️ 测试暂停功能...")
            self.floating_widget.pause_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def test_resume(self):
        """测试继续功能"""
        if self.floating_widget:
            print("▶️ 测试继续功能...")
            self.floating_widget.resume_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def test_stop(self):
        """测试停止功能"""
        if self.floating_widget:
            print("⏹️ 测试停止功能...")
            self.floating_widget.stop_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def update_monitor(self):
        """更新监控信息"""
        if self.floating_widget and hasattr(self.floating_widget, 'study_service'):
            try:
                # 获取时间（秒）
                duration_seconds = self.floating_widget.study_service.get_current_duration_seconds()
                
                # 格式化为 HH:MM:SS
                hours = duration_seconds // 3600
                minutes = (duration_seconds % 3600) // 60
                seconds = duration_seconds % 60
                time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                
                self.time_display_label.setText(f"当前时间: {time_str}")
                
                # 获取会话状态
                cache = self.floating_widget.study_service.current_session_cache
                if cache['id'] is not None:
                    status = cache['status']
                    self.session_status_label.setText(f"会话状态: {status}")
                    
                    # 根据状态设置颜色
                    if status == 'active':
                        self.session_status_label.setStyleSheet("color: green;")
                    elif status == 'paused':
                        self.session_status_label.setStyleSheet("color: orange;")
                    else:
                        self.session_status_label.setStyleSheet("color: gray;")
                else:
                    self.session_status_label.setText("会话状态: 无")
                    self.session_status_label.setStyleSheet("color: gray;")
                    
            except Exception as e:
                self.time_display_label.setText("当前时间: 错误")
                self.session_status_label.setText("会话状态: 错误")
                print(f"Monitor error: {e}")
    
    def on_start_signal(self):
        """开始学习信号"""
        self.status_label.setText("✅ 学习已开始，观察HH:MM:SS格式")
        print("✅ 学习已开始，时间应显示为HH:MM:SS格式")
    
    def on_pause_signal(self):
        """暂停学习信号"""
        self.status_label.setText("⏸️ 学习已暂停")
        print("⏸️ 学习已暂停")
    
    def on_resume_signal(self):
        """恢复学习信号"""
        self.status_label.setText("▶️ 学习已恢复")
        print("▶️ 学习已恢复")
    
    def on_stop_signal(self):
        """停止学习信号"""
        self.status_label.setText("⏹️ 学习已停止")
        print("⏹️ 学习已停止")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.floating_widget:
            self.floating_widget.close()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建测试窗口
        window = FinalTestWindow()
        window.show()
        
        print("最终修复测试程序已启动")
        print("测试重点：")
        print("1. 时间显示格式：HH:MM:SS")
        print("2. 暂停功能：无错误信息")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
