"""
Task Service for Task Manager Module

This service provides business logic for task management operations.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from core.database import get_session
from core.logger import LoggerMixin
from models.task_models import Task, TaskStatus, TaskPriority, TaskCategory, TaskReminder, TaskTemplate, TaskComment


class TaskService(LoggerMixin):
    """Service class for task management operations"""
    
    def __init__(self):
        pass
    
    def create_task(self, title: str, description: str = None, category_id: str = None,
                   priority: str = 'medium', due_date: datetime = None,
                   start_date: datetime = None, reminder_time: datetime = None,
                   tags: List[str] = None) -> Optional[Task]:
        """Create a new task

        Args:
            title: Task title
            description: Task description
            category_id: Category ID
            priority: Task priority (low, medium, high, urgent)
            due_date: Due date
            start_date: Task start/execution date
            reminder_time: Reminder time
            tags: List of tags

        Returns:
            Created task or None if failed
        """
        try:
            with get_session() as session:
                # Convert priority string to enum if needed
                from models.task_models import TaskPriority
                if isinstance(priority, str):
                    priority_map = {
                        'low': TaskPriority.LOW,
                        'normal': TaskPriority.NORMAL,
                        'high': TaskPriority.HIGH,
                        'urgent': TaskPriority.URGENT
                    }
                    priority = priority_map.get(priority, TaskPriority.NORMAL)

                # Create task
                task = Task(
                    title=title,
                    description=description,
                    category_id=category_id,
                    priority=priority,
                    due_date=due_date,
                    start_date=start_date,
                    tags=','.join(tags) if tags else None
                )
                session.add(task)
                session.flush()
                
                # Create reminder if specified
                if reminder_time:
                    reminder = TaskReminder(
                        task_id=task.id,
                        reminder_time=reminder_time,
                        reminder_type='notification'
                    )
                    session.add(reminder)
                
                session.commit()

                # Refresh to ensure all attributes are loaded
                session.refresh(task)

                self.logger.info(f"Task created: {title}")

                # Make the task object detached from session
                session.expunge(task)
                return task
                
        except Exception as e:
            self.logger.error(f"Error creating task: {e}")
            return None
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID
        
        Args:
            task_id: Task ID
            
        Returns:
            Task or None if not found
        """
        try:
            with get_session() as session:
                task = Task.get_by_id(session, task_id)
                if task:
                    # Make the task object detached from session
                    session.expunge(task)
                    return task
                return None
        except Exception as e:
            self.logger.error(f"Error getting task {task_id}: {e}")
            return None
    
    def update_task(self, task_id: str, **kwargs) -> bool:
        """Update a task

        Args:
            task_id: Task ID
            **kwargs: Fields to update

        Returns:
            True if successful, False otherwise
        """
        try:
            with get_session() as session:
                task = Task.get_by_id(session, task_id)
                if not task:
                    return False

                # Update fields with proper enum conversion
                for key, value in kwargs.items():
                    if hasattr(task, key):
                        # Convert status string to enum if needed
                        if key == 'status' and isinstance(value, str):
                            from models.task_models import TaskStatus
                            status_map = {
                                'PENDING': TaskStatus.PENDING,
                                'pending': TaskStatus.PENDING,
                                '待处理': TaskStatus.PENDING,
                                'IN_PROGRESS': TaskStatus.IN_PROGRESS,
                                'in_progress': TaskStatus.IN_PROGRESS,
                                '进行中': TaskStatus.IN_PROGRESS,
                                'COMPLETED': TaskStatus.COMPLETED,
                                'completed': TaskStatus.COMPLETED,
                                '已完成': TaskStatus.COMPLETED,
                                'CANCELLED': TaskStatus.CANCELLED,
                                'cancelled': TaskStatus.CANCELLED,
                                '已取消': TaskStatus.CANCELLED,
                                'PAUSED': TaskStatus.PAUSED,
                                'paused': TaskStatus.PAUSED,
                                '已暂停': TaskStatus.PAUSED
                            }
                            value = status_map.get(value, TaskStatus.PENDING)

                        # Convert priority string to enum if needed
                        elif key == 'priority' and isinstance(value, str):
                            from models.task_models import TaskPriority
                            priority_map = {
                                'low': TaskPriority.LOW,
                                'normal': TaskPriority.NORMAL,
                                'high': TaskPriority.HIGH,
                                'urgent': TaskPriority.URGENT,
                                '低': TaskPriority.LOW,
                                '普通': TaskPriority.NORMAL,
                                '高': TaskPriority.HIGH,
                                '紧急': TaskPriority.URGENT
                            }
                            value = priority_map.get(value, TaskPriority.NORMAL)

                        setattr(task, key, value)

                task.updated_at = datetime.utcnow()
                session.commit()

                self.logger.info(f"Task updated: {task_id}")
                return True

        except Exception as e:
            self.logger.error(f"Error updating task {task_id}: {e}")
            return False
    
    def delete_task(self, task_id: str) -> bool:
        """Delete a task
        
        Args:
            task_id: Task ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with get_session() as session:
                task = Task.get_by_id(session, task_id)
                if not task:
                    return False
                
                task.soft_delete()
                session.commit()
                
                self.logger.info(f"Task deleted: {task_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error deleting task {task_id}: {e}")
            return False
    
    def get_tasks(self, status: str = None, category_id: str = None,
                 priority: str = None, limit: int = 100) -> List[Task]:
        """Get tasks with filters
        
        Args:
            status: Filter by status
            category_id: Filter by category
            priority: Filter by priority
            limit: Maximum number of tasks to return
            
        Returns:
            List of tasks
        """
        try:
            with get_session() as session:
                query = session.query(Task).filter(Task.is_deleted == False)

                if status:
                    query = query.filter(Task.status == status)
                if category_id:
                    query = query.filter(Task.category_id == category_id)
                if priority:
                    query = query.filter(Task.priority == priority)

                tasks = query.order_by(Task.created_at.desc()).limit(limit).all()

                # Make all task objects detached from session
                for task in tasks:
                    session.expunge(task)

                return tasks
                
        except Exception as e:
            self.logger.error(f"Error getting tasks: {e}")
            return []

    def get_tasks_by_tag(self, tag: str, limit: int = 100) -> List[Task]:
        """Get tasks filtered by tag

        Args:
            tag: Tag to filter by
            limit: Maximum number of tasks to return

        Returns:
            List of tasks containing the specified tag
        """
        try:
            with get_session() as session:
                query = session.query(Task).filter(
                    Task.is_deleted == False,
                    Task.tags.like(f'%{tag}%')
                )

                tasks = query.order_by(Task.created_at.desc()).limit(limit).all()

                # Make all task objects detached from session
                for task in tasks:
                    session.expunge(task)

                return tasks

        except Exception as e:
            self.logger.error(f"Error getting tasks by tag: {e}")
            return []
    
    def search_tasks(self, query: str) -> List[Task]:
        """Search tasks by title and description
        
        Args:
            query: Search query
            
        Returns:
            List of matching tasks
        """
        try:
            with get_session() as session:
                tasks = Task.search_tasks(session, query)
                # Make all task objects detached from session
                for task in tasks:
                    session.expunge(task)
                return tasks
        except Exception as e:
            self.logger.error(f"Error searching tasks: {e}")
            return []
    
    def get_overdue_tasks(self) -> List[Task]:
        """Get overdue tasks
        
        Returns:
            List of overdue tasks
        """
        try:
            with get_session() as session:
                now = datetime.utcnow()
                tasks = session.query(Task).filter(
                    and_(
                        Task.due_date < now,
                        Task.status != 'completed',
                        Task.is_deleted == False
                    )
                ).all()

                # Make all task objects detached from session
                for task in tasks:
                    session.expunge(task)

                return tasks
        except Exception as e:
            self.logger.error(f"Error getting overdue tasks: {e}")
            return []
    
    def get_upcoming_tasks(self, days: int = 7) -> List[Task]:
        """Get upcoming tasks
        
        Args:
            days: Number of days to look ahead
            
        Returns:
            List of upcoming tasks
        """
        try:
            with get_session() as session:
                now = datetime.utcnow()
                future = now + timedelta(days=days)

                tasks = session.query(Task).filter(
                    and_(
                        Task.due_date >= now,
                        Task.due_date <= future,
                        Task.status != 'completed',
                        Task.is_deleted == False
                    )
                ).order_by(Task.due_date).all()

                # Make all task objects detached from session
                for task in tasks:
                    session.expunge(task)

                return tasks
        except Exception as e:
            self.logger.error(f"Error getting upcoming tasks: {e}")
            return []
    
    def complete_task(self, task_id: str) -> bool:
        """Mark a task as completed

        Args:
            task_id: Task ID

        Returns:
            True if successful, False otherwise
        """
        try:
            with get_session() as session:
                task = Task.get_by_id(session, task_id)
                if not task:
                    return False

                from models.task_models import TaskStatus
                task.status = TaskStatus.COMPLETED
                task.completed_date = datetime.utcnow()
                task.updated_at = datetime.utcnow()
                task.progress = 100
                session.commit()

                self.logger.info(f"Task completed: {task_id}")
                return True

        except Exception as e:
            self.logger.error(f"Error completing task {task_id}: {e}")
            return False

    def update_task_status(self, task_id: str, completed: bool) -> bool:
        """Update task completion status

        Args:
            task_id: Task ID
            completed: True to mark as completed, False to mark as pending

        Returns:
            True if successful, False otherwise
        """
        try:
            with get_session() as session:
                task = Task.get_by_id(session, task_id)
                if not task:
                    return False

                from models.task_models import TaskStatus
                if completed:
                    task.status = TaskStatus.COMPLETED
                    task.completed_date = datetime.utcnow()
                    task.progress = 100
                else:
                    task.status = TaskStatus.PENDING
                    task.completed_date = None
                    task.progress = 0

                task.updated_at = datetime.utcnow()
                session.commit()

                self.logger.info(f"Task status updated: {task_id} -> {'completed' if completed else 'pending'}")
                return True

        except Exception as e:
            self.logger.error(f"Error updating task status {task_id}: {e}")
            return False
    
    # Category management
    def create_category(self, name: str, description: str = None, color: str = '#007ACC') -> Optional[TaskCategory]:
        """Create a new task category
        
        Args:
            name: Category name
            description: Category description
            color: Category color
            
        Returns:
            Created category or None if failed
        """
        try:
            with get_session() as session:
                category = TaskCategory(
                    name=name,
                    description=description,
                    color=color
                )
                session.add(category)
                session.commit()

                # Refresh to ensure all attributes are loaded
                session.refresh(category)

                # Create a detached copy with all attributes loaded
                category_dict = {
                    'id': category.id,
                    'name': category.name,
                    'description': category.description,
                    'color': category.color,
                    'created_at': category.created_at,
                    'updated_at': category.updated_at
                }

                self.logger.info(f"Category created: {name}")

                # Return a new instance with loaded attributes
                detached_category = TaskCategory(**category_dict)
                return detached_category
                
        except Exception as e:
            self.logger.error(f"Error creating category: {e}")
            return None
    
    def get_categories(self) -> List[TaskCategory]:
        """Get all task categories
        
        Returns:
            List of categories
        """
        try:
            with get_session() as session:
                return session.query(TaskCategory).filter(
                    TaskCategory.is_deleted == False
                ).order_by(TaskCategory.name).all()
        except Exception as e:
            self.logger.error(f"Error getting categories: {e}")
            return []
    
    def delete_category(self, category_id: str) -> bool:
        """Delete a category
        
        Args:
            category_id: Category ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with get_session() as session:
                category = TaskCategory.get_by_id(session, category_id)
                if not category:
                    return False
                
                category.soft_delete()
                session.commit()
                
                self.logger.info(f"Category deleted: {category_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error deleting category {category_id}: {e}")
            return False
    
    # Reminder management
    def create_reminder(self, task_id: str, reminder_time: datetime,
                       reminder_type: str = 'notification', message: str = None) -> Optional[TaskReminder]:
        """Create a task reminder
        
        Args:
            task_id: Task ID
            reminder_time: When to remind
            reminder_type: Type of reminder
            message: Custom reminder message
            
        Returns:
            Created reminder or None if failed
        """
        try:
            with get_session() as session:
                reminder = TaskReminder(
                    task_id=task_id,
                    reminder_time=reminder_time,
                    reminder_type=reminder_type,
                    message=message
                )
                session.add(reminder)
                session.commit()
                
                self.logger.info(f"Reminder created for task: {task_id}")
                return reminder
                
        except Exception as e:
            self.logger.error(f"Error creating reminder: {e}")
            return None
    
    def get_pending_reminders(self) -> List[TaskReminder]:
        """Get pending reminders
        
        Returns:
            List of pending reminders
        """
        try:
            with get_session() as session:
                now = datetime.utcnow()
                return session.query(TaskReminder).filter(
                    and_(
                        TaskReminder.reminder_time <= now,
                        TaskReminder.is_sent == False,
                        TaskReminder.is_deleted == False
                    )
                ).all()
        except Exception as e:
            self.logger.error(f"Error getting pending reminders: {e}")
            return []
