#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的进程间通信测试
测试命令文件机制是否正常工作
"""

import os
import time
from pathlib import Path


def test_command_file_creation():
    """测试命令文件创建和删除"""
    print("=" * 50)
    print("测试命令文件机制")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    command_file = project_root / "daemon_command.txt"
    
    # 清理可能存在的旧文件
    if command_file.exists():
        command_file.unlink()
        print("🧹 清理了旧的命令文件")
    
    # 测试创建命令文件
    print("📝 创建命令文件...")
    try:
        with open(command_file, 'w', encoding='utf-8') as f:
            f.write("show_widget")
        print("✅ 命令文件创建成功")
        
        # 验证文件内容
        with open(command_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        print(f"📖 文件内容: '{content}'")
        
        if content == "show_widget":
            print("✅ 文件内容正确")
        else:
            print("❌ 文件内容不正确")
            
    except Exception as e:
        print(f"❌ 创建命令文件失败: {e}")
        return False
    
    # 测试删除命令文件
    print("🗑️ 删除命令文件...")
    try:
        command_file.unlink()
        print("✅ 命令文件删除成功")
        
        if not command_file.exists():
            print("✅ 确认文件已删除")
        else:
            print("❌ 文件仍然存在")
            
    except Exception as e:
        print(f"❌ 删除命令文件失败: {e}")
        return False
    
    return True


def test_widget_command_file():
    """测试小组件命令文件"""
    print("\n" + "=" * 50)
    print("测试小组件命令文件机制")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    widget_command_file = project_root / "widget_show_command.txt"
    
    # 清理可能存在的旧文件
    if widget_command_file.exists():
        widget_command_file.unlink()
        print("🧹 清理了旧的小组件命令文件")
    
    # 测试创建小组件命令文件
    print("📝 创建小组件命令文件...")
    try:
        with open(widget_command_file, 'w', encoding='utf-8') as f:
            f.write("show")
        print("✅ 小组件命令文件创建成功")
        
        # 验证文件内容
        with open(widget_command_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        print(f"📖 文件内容: '{content}'")
        
        if content == "show":
            print("✅ 文件内容正确")
        else:
            print("❌ 文件内容不正确")
            
    except Exception as e:
        print(f"❌ 创建小组件命令文件失败: {e}")
        return False
    
    # 测试删除小组件命令文件
    print("🗑️ 删除小组件命令文件...")
    try:
        widget_command_file.unlink()
        print("✅ 小组件命令文件删除成功")
        
        if not widget_command_file.exists():
            print("✅ 确认文件已删除")
        else:
            print("❌ 文件仍然存在")
            
    except Exception as e:
        print(f"❌ 删除小组件命令文件失败: {e}")
        return False
    
    return True


def test_file_permissions():
    """测试文件权限"""
    print("\n" + "=" * 50)
    print("测试文件权限")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    
    # 测试目录权限
    print(f"📁 项目目录: {project_root}")
    print(f"📁 目录存在: {project_root.exists()}")
    print(f"📁 目录可读: {os.access(project_root, os.R_OK)}")
    print(f"📁 目录可写: {os.access(project_root, os.W_OK)}")
    
    # 测试创建临时文件
    test_file = project_root / "test_permissions.tmp"
    try:
        with open(test_file, 'w') as f:
            f.write("test")
        print("✅ 可以创建文件")
        
        test_file.unlink()
        print("✅ 可以删除文件")
        
    except Exception as e:
        print(f"❌ 文件操作失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("🧪 进程间通信机制测试")
    
    success_count = 0
    total_tests = 3
    
    # 测试1：命令文件机制
    if test_command_file_creation():
        success_count += 1
    
    # 测试2：小组件命令文件机制
    if test_widget_command_file():
        success_count += 1
    
    # 测试3：文件权限
    if test_file_permissions():
        success_count += 1
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"✅ 成功: {success_count}/{total_tests}")
    print(f"❌ 失败: {total_tests - success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！进程间通信机制工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查文件权限和路径配置。")
    
    return success_count == total_tests


if __name__ == "__main__":
    main()
