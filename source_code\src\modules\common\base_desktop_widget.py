"""
Base Desktop Widget - Common base class for all desktop widgets
"""

import sys
from abc import abstractmethod
from pathlib import Path
from PyQt6.QtWidgets import QWidget, QApplication
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon

from core.logger import LoggerMixin
from core.config import ConfigManager
from .communication_manager import WidgetCommunicationManager


class BaseDesktopWidget(QWidget):
    """通用桌面小组件基类"""

    # 通用信号
    widget_closed = pyqtSignal()
    show_main_window = pyqtSignal()
    status_changed = pyqtSignal(str)  # 状态变化信号

    def __init__(self, widget_type: str, parent=None):
        super().__init__(parent)
        self.widget_type = widget_type
        self.config_manager = ConfigManager()

        # 添加日志功能
        self._logger_mixin = LoggerMixin()

    @property
    def logger(self):
        """获取日志器"""
        return self._logger_mixin.logger
        
        # 通信管理器
        self.communication_manager = WidgetCommunicationManager(widget_type)
        self.communication_manager.command_received.connect(self.handle_command)
        
        # 基础设置
        self.setup_base_properties()
        self.setup_command_timer()
        
        # 子类实现具体UI
        self.init_widget_ui()
        
        self.logger.info(f"{widget_type} widget initialized")
    
    def setup_base_properties(self):
        """设置基础窗口属性"""
        # 设置窗口标志
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        
        # 设置窗口属性
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setWindowTitle(f"Personal Manager - {self.widget_type}")
        
        # 加载保存的位置和大小
        self.load_geometry()
    
    def setup_command_timer(self):
        """设置命令检查定时器"""
        self.command_timer = QTimer()
        self.command_timer.timeout.connect(self.communication_manager.check_commands)
        self.command_timer.start(1000)  # 每秒检查一次
    
    @abstractmethod
    def init_widget_ui(self):
        """初始化具体的小组件UI - 子类必须实现"""
        pass
    
    @abstractmethod
    def get_widget_data(self):
        """获取小组件数据 - 子类必须实现"""
        pass
    
    def handle_command(self, command: str):
        """处理接收到的命令"""
        if command == "show":
            self.show_widget()
        elif command == "hide":
            self.hide_widget()
        elif command == "toggle":
            self.toggle_widget()
        elif command == "refresh":
            self.refresh_data()
        elif command == "manual_takeover":
            self.handle_manual_takeover()
        else:
            self.handle_custom_command(command)
    
    def handle_custom_command(self, command: str):
        """处理自定义命令 - 子类可以重写"""
        self.logger.debug(f"Unhandled command: {command}")
    
    def show_widget(self):
        """显示小组件"""
        self.show()
        self.raise_()
        self.activateWindow()
        self.status_changed.emit("shown")
    
    def hide_widget(self):
        """隐藏小组件"""
        self.hide()
        self.status_changed.emit("hidden")
    
    def toggle_widget(self):
        """切换小组件显示状态"""
        if self.isVisible():
            self.hide_widget()
        else:
            self.show_widget()
    
    def refresh_data(self):
        """刷新数据 - 子类可以重写"""
        pass
    
    def handle_manual_takeover(self):
        """处理手动接管 - 子类可以重写"""
        self.show_widget()
    
    def load_geometry(self):
        """加载保存的窗口几何信息"""
        try:
            geometry_key = f"{self.widget_type}_widget.geometry"
            geometry = self.config_manager.get(geometry_key)
            
            if geometry:
                self.setGeometry(
                    geometry.get('x', 100),
                    geometry.get('y', 100), 
                    geometry.get('width', 300),
                    geometry.get('height', 200)
                )
        except Exception as e:
            self.logger.warning(f"Failed to load geometry: {e}")
    
    def save_geometry(self):
        """保存窗口几何信息"""
        try:
            geometry = {
                'x': self.x(),
                'y': self.y(),
                'width': self.width(),
                'height': self.height()
            }
            
            geometry_key = f"{self.widget_type}_widget.geometry"
            self.config_manager.set(geometry_key, geometry)
            self.config_manager.save()
        except Exception as e:
            self.logger.warning(f"Failed to save geometry: {e}")
    
    def closeEvent(self, event):
        """关闭事件处理"""
        self.save_geometry()
        self.communication_manager.notify_closed()
        self.widget_closed.emit()
        super().closeEvent(event)
    
    def mousePressEvent(self, event):
        """鼠标按下事件 - 支持拖拽"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 实现拖拽"""
        if event.buttons() == Qt.MouseButton.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()


def create_widget_application(widget_class, widget_type: str, *args, **kwargs):
    """创建小组件应用的通用函数"""
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)
    
    # 设置应用信息
    app.setApplicationName(f"Personal Manager - {widget_type}")
    app.setApplicationVersion("1.0.0")
    
    # 创建并显示小组件
    widget = widget_class(widget_type, *args, **kwargs)
    widget.show()
    
    return app, widget