"""
数据库初始化脚本

创建所有数据表并插入初始数据
"""

import os
import sys
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.database import init_db_manager
from core.config import Config
from models import *


def create_tables():
    """创建所有数据表"""
    print("正在创建数据表...")
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)

        # 创建所有表
        db_manager.create_tables()
        print("✓ 数据表创建成功")
        
        return db_manager
    except Exception as e:
        print(f"✗ 数据表创建失败: {e}")
        return None


def insert_initial_data(db_manager):
    """插入初始数据"""
    print("正在插入初始数据...")
    
    try:
        with db_manager.get_session() as session:
            # 插入默认任务分类
            task_categories = [
                {"name": "工作", "description": "工作相关任务", "color": "#FF6B6B", "icon": "work", "sort_order": 1},
                {"name": "学习", "description": "学习相关任务", "color": "#4ECDC4", "icon": "school", "sort_order": 2},
                {"name": "生活", "description": "日常生活任务", "color": "#45B7D1", "icon": "home", "sort_order": 3},
                {"name": "健康", "description": "健康相关任务", "color": "#96CEB4", "icon": "fitness", "sort_order": 4},
                {"name": "娱乐", "description": "娱乐休闲任务", "color": "#FFEAA7", "icon": "entertainment", "sort_order": 5}
            ]
            
            for cat_data in task_categories:
                if not TaskCategory.get_by_name(session, cat_data["name"]):
                    category = TaskCategory(**cat_data)
                    session.add(category)
            
            # 插入默认笔记分类
            note_categories = [
                {"name": "工作笔记", "description": "工作相关笔记", "color": "#FF6B6B", "icon": "work", "sort_order": 1},
                {"name": "学习笔记", "description": "学习相关笔记", "color": "#4ECDC4", "icon": "book", "sort_order": 2},
                {"name": "生活记录", "description": "生活记录笔记", "color": "#45B7D1", "icon": "life", "sort_order": 3},
                {"name": "想法灵感", "description": "想法和灵感记录", "color": "#96CEB4", "icon": "idea", "sort_order": 4},
                {"name": "技术文档", "description": "技术相关文档", "color": "#FFEAA7", "icon": "code", "sort_order": 5}
            ]
            
            for cat_data in note_categories:
                if not NoteCategory.get_by_name(session, cat_data["name"]):
                    category = NoteCategory(**cat_data)
                    session.add(category)
            
            # 插入默认文件标签
            file_tags = [
                {"name": "重要", "color": "#FF6B6B"},
                {"name": "工作", "color": "#4ECDC4"},
                {"name": "学习", "color": "#45B7D1"},
                {"name": "项目", "color": "#96CEB4"},
                {"name": "临时", "color": "#FFEAA7"}
            ]
            
            for tag_data in file_tags:
                if not FileTag.get_by_name(session, tag_data["name"]):
                    tag = FileTag(**tag_data)
                    session.add(tag)
            
            # 插入系统设置
            default_settings = [
                {
                    "key": "app.theme",
                    "value": "light",
                    "description": "应用主题",
                    "category": "appearance"
                },
                {
                    "key": "app.language",
                    "value": "zh_CN",
                    "description": "应用语言",
                    "category": "general"
                },
                {
                    "key": "app.auto_start",
                    "value": "false",
                    "description": "开机自启动",
                    "category": "general"
                },
                {
                    "key": "file_manager.show_hidden",
                    "value": "false",
                    "description": "显示隐藏文件",
                    "category": "file_manager"
                },
                {
                    "key": "task_manager.default_priority",
                    "value": "normal",
                    "description": "默认任务优先级",
                    "category": "task_manager"
                },
                {
                    "key": "system_monitor.update_interval",
                    "value": "5",
                    "description": "系统监控更新间隔（秒）",
                    "category": "system_monitor"
                },
                {
                    "key": "backup.auto_backup",
                    "value": "true",
                    "description": "自动备份",
                    "category": "backup"
                },
                {
                    "key": "backup.backup_interval",
                    "value": "24",
                    "description": "备份间隔（小时）",
                    "category": "backup"
                }
            ]
            
            for setting_data in default_settings:
                existing = Setting.get_setting(session, setting_data["key"])
                if existing is None:
                    Setting.set_setting(
                        session,
                        setting_data["key"],
                        setting_data["value"],
                        setting_data["description"],
                        setting_data["category"]
                    )
            
            # 提交所有更改
            session.commit()
            print("✓ 初始数据插入成功")
            
    except Exception as e:
        print(f"✗ 初始数据插入失败: {e}")
        session.rollback()


def create_sample_data(db_manager):
    """创建示例数据（可选）"""
    print("正在创建示例数据...")
    
    try:
        with db_manager.get_session() as session:
            # 创建示例任务
            work_category = TaskCategory.get_by_name(session, "工作")
            if work_category:
                sample_tasks = [
                    {
                        "title": "完成项目文档",
                        "description": "编写项目的技术文档和用户手册",
                        "priority": TaskPriority.HIGH,
                        "category_id": work_category.id,
                        "tags": "文档,项目",
                        "estimated_hours": 480  # 8小时
                    },
                    {
                        "title": "代码审查",
                        "description": "审查团队成员提交的代码",
                        "priority": TaskPriority.NORMAL,
                        "category_id": work_category.id,
                        "tags": "代码,审查",
                        "estimated_hours": 120  # 2小时
                    }
                ]
                
                for task_data in sample_tasks:
                    task = Task(**task_data)
                    session.add(task)
            
            # 创建示例笔记
            work_note_category = NoteCategory.get_by_name(session, "工作笔记")
            if work_note_category:
                sample_notes = [
                    {
                        "title": "项目会议记录",
                        "content": "今天的项目会议讨论了以下内容：\n1. 项目进度回顾\n2. 下阶段计划\n3. 资源分配",
                        "category_id": work_note_category.id,
                        "tags": "会议,项目",
                        "content_type": "text"
                    },
                    {
                        "title": "技术方案设计",
                        "content": "# 技术方案\n\n## 架构设计\n\n### 前端\n- PyQt6\n- 响应式设计\n\n### 后端\n- SQLAlchemy\n- SQLite数据库",
                        "category_id": work_note_category.id,
                        "tags": "技术,设计",
                        "content_type": "markdown"
                    }
                ]
                
                for note_data in sample_notes:
                    note = Note(**note_data)
                    note.update_word_count()
                    session.add(note)
            
            # 创建示例应用
            sample_apps = [
                {
                    "name": "notepad",
                    "display_name": "记事本",
                    "description": "Windows系统记事本",
                    "exe_path": "C:\\Windows\\System32\\notepad.exe",
                    "category": "系统工具",
                    "tags": "文本编辑,系统",
                    "is_favorite": True
                },
                {
                    "name": "calculator",
                    "display_name": "计算器",
                    "description": "Windows系统计算器",
                    "exe_path": "C:\\Windows\\System32\\calc.exe",
                    "category": "系统工具",
                    "tags": "计算,系统",
                    "is_pinned": True
                }
            ]
            
            for app_data in sample_apps:
                app = Application(**app_data)
                session.add(app)
            
            # 提交所有更改
            session.commit()
            print("✓ 示例数据创建成功")
            
    except Exception as e:
        print(f"✗ 示例数据创建失败: {e}")
        session.rollback()


def main():
    """主函数"""
    print("Personal Manager System - 数据库初始化")
    print("=" * 50)
    
    # 创建数据表
    db_manager = create_tables()
    if not db_manager:
        return
    
    # 插入初始数据
    insert_initial_data(db_manager)
    
    # 询问是否创建示例数据
    create_samples = input("\n是否创建示例数据？(y/N): ").lower().strip()
    if create_samples in ['y', 'yes']:
        create_sample_data(db_manager)
    
    print("\n数据库初始化完成！")
    print("您现在可以启动Personal Manager System了。")


if __name__ == "__main__":
    main()
