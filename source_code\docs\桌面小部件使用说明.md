# 桌面任务小部件使用说明

## 概述

桌面任务小部件是一个独立的桌面应用程序，可以在桌面上显示当日任务，提供快速的任务管理功能。

## 功能特性

### 显示功能
- **当前日期显示**: 显示格式为"YYYY年MM月DD日 星期X"
- **任务分类显示**: 按时间段分类显示任务
  - 上午任务
  - 下午任务  
  - 晚上任务
  - 全天任务
  - 其他任务
- **任务状态**: 显示任务的完成状态和优先级

### 操作功能
- **添加任务**: 点击"添加任务"按钮快速创建新任务
- **刷新**: 手动刷新任务数据，与主应用保持同步
- **打开应用**: 一键启动主应用程序并跳转到任务管理模块

### 界面特性
- **蓝色机械风格**: 深蓝色背景，金属质感边框和按钮
- **置顶显示**: 始终保持在其他窗口之上
- **可拖拽**: 支持鼠标拖拽改变位置
- **系统托盘**: 支持最小化到系统托盘

## 启动方式

### 方式一：批处理文件启动（推荐）
1. 双击 `start_desktop_widget.bat` 文件
2. 系统会自动检查Python环境和依赖包
3. 如果缺少依赖会自动尝试安装

### 方式二：Python脚本启动
1. 打开命令行，切换到项目目录
2. 运行命令：`python start_desktop_widget.py`

### 方式三：直接运行模块
1. 打开命令行，切换到项目目录
2. 运行命令：`python -m src.modules.task_manager.desktop_widget`

## 使用说明

### 基本操作

1. **查看任务**
   - 小部件启动后会自动加载当日任务
   - 任务按分类显示，每个分类显示任务数量
   - 点击任务卡片可查看任务详情

2. **添加任务**
   - 点击"添加任务"按钮
   - 在弹出的对话框中填写任务信息：
     - 任务标题（必填）
     - 任务描述（可选）
     - 任务分类（上午/下午/晚上/全天/其他）
     - 优先级（低/普通/高/紧急）
   - 点击"确定"创建任务

3. **刷新数据**
   - 点击"刷新"按钮手动更新任务数据
   - 小部件每30秒自动刷新一次

4. **打开主应用**
   - 点击"打开应用"按钮启动主应用程序
   - 可以在主应用中进行更详细的任务管理

### 窗口操作

1. **移动位置**
   - 按住鼠标左键拖拽小部件到任意位置

2. **最小化**
   - 点击标题栏的"−"按钮最小化到系统托盘
   - 双击托盘图标可重新显示

3. **关闭**
   - 点击标题栏的"×"按钮关闭小部件
   - 如果有系统托盘，会最小化到托盘而不是真正关闭

### 系统托盘功能

右键点击系统托盘图标可以：
- **显示**: 重新显示小部件窗口
- **刷新**: 刷新任务数据
- **退出**: 完全退出应用程序

## 技术要求

### 系统要求
- Windows 7/8/10/11
- Python 3.8 或更高版本

### 依赖包
- PyQt6
- SQLAlchemy
- 其他项目依赖包

### 数据同步
- 小部件与主应用共享同一个数据库
- 支持实时数据同步
- 在小部件中创建的任务会立即在主应用中可见

## 故障排除

### 常见问题

1. **启动失败**
   - 检查Python环境是否正确安装
   - 确认所有依赖包已安装
   - 查看错误日志文件

2. **任务不显示**
   - 点击"刷新"按钮手动更新
   - 检查任务的日期设置是否正确
   - 确认任务分类标签设置

3. **无法创建任务**
   - 检查数据库连接是否正常
   - 确认任务标题不为空
   - 查看错误提示信息

4. **系统托盘不显示**
   - 检查系统是否支持系统托盘
   - 确认托盘区域设置

### 日志文件
- 日志文件位置：`config/logs/app.log`
- 可以查看详细的错误信息和运行状态

## 自定义配置

### 样式自定义
- 可以修改 `desktop_widget.py` 中的样式表
- 支持自定义颜色、字体、尺寸等

### 刷新间隔
- 默认30秒自动刷新
- 可以在代码中修改 `refresh_timer` 的间隔时间

### 窗口位置
- 默认显示在屏幕右上角
- 可以修改 `setup_window_properties()` 方法中的位置设置

## 注意事项

1. **数据安全**: 小部件与主应用共享数据库，请确保数据库文件安全
2. **性能影响**: 小部件会定期刷新数据，对系统性能影响很小
3. **网络要求**: 无需网络连接，完全本地运行
4. **多实例**: 建议只运行一个小部件实例，避免冲突

## 更新日志

### v1.0.0
- 初始版本发布
- 基本的任务显示和管理功能
- 蓝色机械风格界面
- 系统托盘支持
- 与主应用数据同步
