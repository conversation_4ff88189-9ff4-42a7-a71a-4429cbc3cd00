"""
Process Monitor - 造神计划
监控应用程序进程，实现自动清理和资源管理
"""

import os
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Callable
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

from .logger import LoggerMixin


class ProcessInfo:
    """进程信息类"""
    
    def __init__(self, pid: int, name: str, cmdline: List[str], start_time: datetime):
        self.pid = pid
        self.name = name
        self.cmdline = cmdline
        self.start_time = start_time
        self.last_seen = datetime.now()
        self.memory_usage = 0.0
        self.cpu_percent = 0.0
        self.status = "running"
    
    def update_stats(self):
        """更新进程统计信息"""
        try:
            if psutil.pid_exists(self.pid):
                proc = psutil.Process(self.pid)
                self.memory_usage = proc.memory_info().rss / 1024 / 1024  # MB
                self.cpu_percent = proc.cpu_percent()
                self.status = proc.status()
                self.last_seen = datetime.now()
            else:
                self.status = "terminated"
        except Exception:
            self.status = "error"
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'pid': self.pid,
            'name': self.name,
            'cmdline': ' '.join(self.cmdline),
            'start_time': self.start_time.isoformat(),
            'last_seen': self.last_seen.isoformat(),
            'memory_mb': round(self.memory_usage, 2),
            'cpu_percent': round(self.cpu_percent, 2),
            'status': self.status
        }


class ProcessMonitor(QObject, LoggerMixin):
    """进程监控器 - 造神计划"""
    
    # 信号
    process_started = pyqtSignal(int, str)  # pid, name
    process_terminated = pyqtSignal(int, str)  # pid, name
    resource_warning = pyqtSignal(str)  # warning message
    cleanup_completed = pyqtSignal(int)  # cleaned process count
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.app_name = "造神计划"
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 进程跟踪
        self.tracked_processes: Dict[int, ProcessInfo] = {}
        self.process_keywords = [
            '造神计划',
            'personal_manager',
            'universal_widget_daemon',
            'desktop_widget',
            'study_floating',
            'launcher.py',
            'main.py'
        ]
        
        # 监控配置
        self.monitor_interval = 5  # 秒
        self.memory_warning_threshold = 500  # MB
        self.cpu_warning_threshold = 80  # %
        self.max_process_age = timedelta(hours=24)  # 最大进程存活时间
        
        # 自动清理配置
        self.auto_cleanup_enabled = True
        self.cleanup_orphaned_processes = True
        self.cleanup_zombie_processes = True
        
        # 统计信息
        self.total_processes_monitored = 0
        self.total_processes_cleaned = 0
        self.last_cleanup_time: Optional[datetime] = None
        
        # 定时器用于Qt主线程中的信号发送
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_process_stats)
        
        self.logger.info("Process monitor initialized")
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            self.logger.warning("Process monitor already running")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        # 启动统计更新定时器
        self.stats_timer.start(self.monitor_interval * 1000)
        
        self.logger.info("Process monitoring started")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        self.stats_timer.stop()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("Process monitoring stopped")
    
    def _monitor_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                # 扫描相关进程
                self._scan_processes()
                
                # 检查资源使用
                self._check_resource_usage()
                
                # 自动清理
                if self.auto_cleanup_enabled:
                    self._auto_cleanup()
                
                # 休眠
                time.sleep(self.monitor_interval)
            
            except Exception as e:
                self.logger.error(f"Error in monitor loop: {e}")
                time.sleep(self.monitor_interval)
    
    def _scan_processes(self):
        """扫描相关进程"""
        current_pids = set()
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    proc_info = proc.info
                    pid = proc_info['pid']
                    name = proc_info['name']
                    cmdline = proc_info.get('cmdline', [])
                    
                    if not cmdline:
                        continue
                    
                    # 检查是否是相关进程
                    if self._is_related_process(name, cmdline):
                        current_pids.add(pid)
                        
                        # 如果是新进程，添加到跟踪列表
                        if pid not in self.tracked_processes:
                            start_time = datetime.fromtimestamp(proc_info['create_time'])
                            process_info = ProcessInfo(pid, name, cmdline, start_time)
                            self.tracked_processes[pid] = process_info
                            self.total_processes_monitored += 1
                            
                            # 在主线程中发送信号
                            self.process_started.emit(pid, name)
                            self.logger.info(f"New process detected: {pid} - {name}")
                
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
        
        except Exception as e:
            self.logger.error(f"Error scanning processes: {e}")
        
        # 检查已终止的进程
        terminated_pids = set(self.tracked_processes.keys()) - current_pids
        for pid in terminated_pids:
            process_info = self.tracked_processes.pop(pid)
            self.process_terminated.emit(pid, process_info.name)
            self.logger.info(f"Process terminated: {pid} - {process_info.name}")
    
    def _is_related_process(self, name: str, cmdline: List[str]) -> bool:
        """检查是否是相关进程"""
        name_lower = name.lower()
        cmdline_str = ' '.join(cmdline).lower()
        
        for keyword in self.process_keywords:
            if (keyword in name_lower or 
                keyword in cmdline_str or
                any(keyword in arg.lower() for arg in cmdline)):
                return True
        
        return False
    
    def _check_resource_usage(self):
        """检查资源使用情况"""
        total_memory = 0
        high_cpu_processes = []
        
        for process_info in self.tracked_processes.values():
            try:
                process_info.update_stats()
                total_memory += process_info.memory_usage
                
                if process_info.cpu_percent > self.cpu_warning_threshold:
                    high_cpu_processes.append(process_info)
            
            except Exception as e:
                self.logger.debug(f"Error updating process stats for {process_info.pid}: {e}")
        
        # 内存警告
        if total_memory > self.memory_warning_threshold:
            warning_msg = f"总内存使用过高: {total_memory:.1f} MB"
            self.resource_warning.emit(warning_msg)
            self.logger.warning(warning_msg)
        
        # CPU警告
        for process_info in high_cpu_processes:
            warning_msg = f"进程 {process_info.pid} CPU使用过高: {process_info.cpu_percent:.1f}%"
            self.resource_warning.emit(warning_msg)
            self.logger.warning(warning_msg)
    
    def _auto_cleanup(self):
        """自动清理"""
        cleaned_count = 0
        current_time = datetime.now()
        
        # 清理僵尸进程
        if self.cleanup_zombie_processes:
            zombie_pids = []
            for pid, process_info in self.tracked_processes.items():
                if process_info.status in ['zombie', 'error']:
                    zombie_pids.append(pid)
            
            for pid in zombie_pids:
                if self._cleanup_process(pid):
                    cleaned_count += 1
        
        # 清理长时间运行的进程
        old_process_pids = []
        for pid, process_info in self.tracked_processes.items():
            if current_time - process_info.start_time > self.max_process_age:
                old_process_pids.append(pid)
        
        for pid in old_process_pids:
            if self._cleanup_process(pid):
                cleaned_count += 1
        
        # 清理孤儿进程
        if self.cleanup_orphaned_processes:
            orphaned_pids = self._find_orphaned_processes()
            for pid in orphaned_pids:
                if self._cleanup_process(pid):
                    cleaned_count += 1
        
        if cleaned_count > 0:
            self.total_processes_cleaned += cleaned_count
            self.last_cleanup_time = current_time
            self.cleanup_completed.emit(cleaned_count)
            self.logger.info(f"Auto cleanup completed: {cleaned_count} processes cleaned")
    
    def _find_orphaned_processes(self) -> List[int]:
        """查找孤儿进程"""
        orphaned_pids = []
        
        try:
            # 这里可以实现更复杂的孤儿进程检测逻辑
            # 例如：检查进程的父进程是否还存在
            for pid, process_info in self.tracked_processes.items():
                try:
                    if psutil.pid_exists(pid):
                        proc = psutil.Process(pid)
                        parent = proc.parent()
                        
                        # 如果父进程不存在或不是相关进程，可能是孤儿进程
                        if parent is None or not self._is_related_process(parent.name(), parent.cmdline()):
                            # 进一步检查是否真的是孤儿进程
                            if self._is_likely_orphaned(process_info):
                                orphaned_pids.append(pid)
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        
        except Exception as e:
            self.logger.error(f"Error finding orphaned processes: {e}")
        
        return orphaned_pids
    
    def _is_likely_orphaned(self, process_info: ProcessInfo) -> bool:
        """判断进程是否可能是孤儿进程"""
        # 简单的启发式规则
        current_time = datetime.now()
        
        # 如果进程运行时间很长且最近没有活动
        if (current_time - process_info.start_time > timedelta(hours=1) and
            current_time - process_info.last_seen > timedelta(minutes=10)):
            return True
        
        # 如果进程状态异常
        if process_info.status in ['zombie', 'stopped']:
            return True
        
        return False
    
    def _cleanup_process(self, pid: int) -> bool:
        """清理指定进程"""
        try:
            if psutil.pid_exists(pid):
                proc = psutil.Process(pid)
                proc.terminate()
                
                # 等待进程结束
                try:
                    proc.wait(timeout=3)
                except psutil.TimeoutExpired:
                    proc.kill()
                
                self.logger.info(f"Cleaned up process: {pid}")
                return True
        
        except Exception as e:
            self.logger.error(f"Error cleaning up process {pid}: {e}")
        
        return False
    
    def update_process_stats(self):
        """更新进程统计信息（在主线程中调用）"""
        # 这个方法在主线程中运行，可以安全地发送信号
        pass
    
    def force_cleanup_all(self):
        """强制清理所有相关进程"""
        self.logger.info("Force cleanup all related processes")
        
        cleaned_count = 0
        for pid in list(self.tracked_processes.keys()):
            if self._cleanup_process(pid):
                cleaned_count += 1
        
        self.total_processes_cleaned += cleaned_count
        self.last_cleanup_time = datetime.now()
        self.cleanup_completed.emit(cleaned_count)
        
        return cleaned_count
    
    def get_process_list(self) -> List[Dict]:
        """获取进程列表"""
        return [info.to_dict() for info in self.tracked_processes.values()]
    
    def get_statistics(self) -> Dict:
        """获取监控统计信息"""
        total_memory = sum(info.memory_usage for info in self.tracked_processes.values())
        avg_cpu = sum(info.cpu_percent for info in self.tracked_processes.values()) / max(len(self.tracked_processes), 1)
        
        return {
            'is_monitoring': self.is_monitoring,
            'tracked_processes': len(self.tracked_processes),
            'total_memory_mb': round(total_memory, 2),
            'average_cpu_percent': round(avg_cpu, 2),
            'total_monitored': self.total_processes_monitored,
            'total_cleaned': self.total_processes_cleaned,
            'last_cleanup': self.last_cleanup_time.isoformat() if self.last_cleanup_time else None,
            'auto_cleanup_enabled': self.auto_cleanup_enabled
        }
    
    def set_auto_cleanup(self, enabled: bool):
        """设置自动清理"""
        self.auto_cleanup_enabled = enabled
        self.logger.info(f"Auto cleanup {'enabled' if enabled else 'disabled'}")
    
    def set_thresholds(self, memory_mb: int = None, cpu_percent: int = None):
        """设置警告阈值"""
        if memory_mb is not None:
            self.memory_warning_threshold = memory_mb
        if cpu_percent is not None:
            self.cpu_warning_threshold = cpu_percent
        
        self.logger.info(f"Thresholds updated: memory={self.memory_warning_threshold}MB, cpu={self.cpu_warning_threshold}%")