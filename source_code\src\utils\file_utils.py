"""
文件操作工具类

提供文件和目录操作的常用功能，包括：
- 文件大小计算和格式化
- 文件哈希计算
- 文件类型检测
- 目录操作
- 文件搜索
- 安全删除
"""

import os
import shutil
import hashlib
import mimetypes
from pathlib import Path
from typing import List, Optional, Generator, Tuple
import send2trash
from datetime import datetime


class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小为人类可读的格式
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            格式化后的文件大小字符串
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB", "PB"]
        i = 0
        while size_bytes >= 1024.0 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    @staticmethod
    def get_file_hash(file_path: str, algorithm: str = 'md5') -> Optional[str]:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 ('md5', 'sha1', 'sha256')
            
        Returns:
            文件哈希值，如果文件不存在返回None
        """
        if not os.path.exists(file_path):
            return None
        
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception:
            return None
    
    @staticmethod
    def get_file_type(file_path: str) -> Tuple[str, str]:
        """
        获取文件类型和MIME类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            (文件扩展名, MIME类型)
        """
        ext = Path(file_path).suffix.lower()
        mime_type, _ = mimetypes.guess_type(file_path)
        return ext, mime_type or 'application/octet-stream'
    
    @staticmethod
    def is_image_file(file_path: str) -> bool:
        """
        检查是否为图片文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为图片文件
        """
        _, mime_type = FileUtils.get_file_type(file_path)
        return mime_type.startswith('image/')
    
    @staticmethod
    def is_text_file(file_path: str) -> bool:
        """
        检查是否为文本文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为文本文件
        """
        _, mime_type = FileUtils.get_file_type(file_path)
        return mime_type.startswith('text/')
    
    @staticmethod
    def get_directory_size(directory: str) -> int:
        """
        计算目录总大小
        
        Args:
            directory: 目录路径
            
        Returns:
            目录大小（字节）
        """
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, FileNotFoundError):
                        continue
        except Exception:
            pass
        return total_size
    
    @staticmethod
    def find_files(directory: str, pattern: str = "*", 
                   recursive: bool = True) -> Generator[str, None, None]:
        """
        搜索文件
        
        Args:
            directory: 搜索目录
            pattern: 文件名模式
            recursive: 是否递归搜索
            
        Yields:
            匹配的文件路径
        """
        path = Path(directory)
        if not path.exists():
            return
        
        if recursive:
            yield from path.rglob(pattern)
        else:
            yield from path.glob(pattern)
    
    @staticmethod
    def find_duplicate_files(directory: str) -> List[List[str]]:
        """
        查找重复文件
        
        Args:
            directory: 搜索目录
            
        Returns:
            重复文件组列表，每组包含相同内容的文件路径
        """
        hash_map = {}
        
        for file_path in FileUtils.find_files(directory):
            if os.path.isfile(file_path):
                file_hash = FileUtils.get_file_hash(str(file_path))
                if file_hash:
                    if file_hash not in hash_map:
                        hash_map[file_hash] = []
                    hash_map[file_hash].append(str(file_path))
        
        # 返回有重复的文件组
        return [files for files in hash_map.values() if len(files) > 1]
    
    @staticmethod
    def safe_delete(file_path: str, use_trash: bool = True) -> bool:
        """
        安全删除文件
        
        Args:
            file_path: 文件路径
            use_trash: 是否移动到回收站
            
        Returns:
            删除是否成功
        """
        try:
            if not os.path.exists(file_path):
                return False
            
            if use_trash:
                send2trash.send2trash(file_path)
            else:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            
            return True
        except Exception:
            return False
    
    @staticmethod
    def create_backup_name(file_path: str) -> str:
        """
        创建备份文件名
        
        Args:
            file_path: 原文件路径
            
        Returns:
            备份文件路径
        """
        path = Path(file_path)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{path.stem}_backup_{timestamp}{path.suffix}"
        return str(path.parent / backup_name)
    
    @staticmethod
    def copy_with_progress(src: str, dst: str, 
                          callback: Optional[callable] = None) -> bool:
        """
        带进度回调的文件复制
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            callback: 进度回调函数 callback(copied_bytes, total_bytes)
            
        Returns:
            复制是否成功
        """
        try:
            if not os.path.exists(src):
                return False
            
            total_size = os.path.getsize(src)
            copied_size = 0
            
            with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
                while True:
                    chunk = fsrc.read(64 * 1024)  # 64KB chunks
                    if not chunk:
                        break
                    fdst.write(chunk)
                    copied_size += len(chunk)
                    
                    if callback:
                        callback(copied_size, total_size)
            
            return True
        except Exception:
            return False
    
    @staticmethod
    def ensure_directory(directory: str) -> bool:
        """
        确保目录存在，如果不存在则创建
        
        Args:
            directory: 目录路径
            
        Returns:
            目录是否存在或创建成功
        """
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_file_info(file_path: str) -> Optional[dict]:
        """
        获取文件详细信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典，如果文件不存在返回None
        """
        try:
            if not os.path.exists(file_path):
                return None
            
            stat = os.stat(file_path)
            ext, mime_type = FileUtils.get_file_type(file_path)
            
            return {
                'path': file_path,
                'name': os.path.basename(file_path),
                'size': stat.st_size,
                'size_formatted': FileUtils.format_file_size(stat.st_size),
                'extension': ext,
                'mime_type': mime_type,
                'created_time': datetime.fromtimestamp(stat.st_ctime),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'accessed_time': datetime.fromtimestamp(stat.st_atime),
                'is_file': os.path.isfile(file_path),
                'is_directory': os.path.isdir(file_path),
                'is_hidden': os.path.basename(file_path).startswith('.'),
                'hash_md5': FileUtils.get_file_hash(file_path, 'md5') if os.path.isfile(file_path) else None
            }
        except Exception:
            return None
