#!/usr/bin/env python3
"""
Test script for daily summary database operations
"""

import sys
from pathlib import Path
from datetime import date, datetime

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from core.config import Config<PERSON>anager
from core.database import init_db_manager, get_session
from models.task_models import DailySummary

# Import all models to ensure they are registered
from models import *

def test_daily_summary():
    """Test daily summary database operations"""
    
    # Initialize database
    config = ConfigManager()
    db_manager = init_db_manager(config=config)
    
    test_date = date.today()
    test_content = "这是一个测试日总结内容"
    
    print(f"Testing daily summary for date: {test_date}")
    
    # Test 1: Create new summary
    print("\n1. Creating new daily summary...")
    with get_session() as session:
        summary = DailySummary.create_or_update(
            session=session,
            summary_date=test_date,
            content=test_content
        )
        session.commit()
        print(f"Created summary with ID: {summary.id}")
    
    # Test 2: Retrieve summary
    print("\n2. Retrieving daily summary...")
    with get_session() as session:
        retrieved = DailySummary.get_by_date(session, test_date)
        if retrieved:
            print(f"Retrieved summary: {retrieved.content}")
            print(f"Summary date: {retrieved.summary_date}")
        else:
            print("No summary found!")
    
    # Test 3: Update existing summary
    print("\n3. Updating daily summary...")
    updated_content = "这是更新后的日总结内容"
    with get_session() as session:
        summary = DailySummary.create_or_update(
            session=session,
            summary_date=test_date,
            content=updated_content
        )
        session.commit()
        print(f"Updated summary content: {summary.content}")
    
    # Test 4: Verify update
    print("\n4. Verifying update...")
    with get_session() as session:
        retrieved = DailySummary.get_by_date(session, test_date)
        if retrieved:
            print(f"Final content: {retrieved.content}")
            if retrieved.content == updated_content:
                print("✅ Update successful!")
            else:
                print("❌ Update failed!")
        else:
            print("❌ No summary found after update!")
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_daily_summary()
