"""
测试增强版学习追踪悬浮小组件的所有新功能
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件
from modules.study_tracker.study_floating_widget import StudyFloatingWidget


class EnhancedTestWindow(QMainWindow):
    """增强功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("增强版学习追踪悬浮小组件功能测试")
        self.setGeometry(100, 100, 600, 500)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("测试增强版学习追踪悬浮小组件的所有新功能")
        layout.addWidget(self.status_label)
        
        # 信息标签
        info_label = QLabel("""
新功能测试清单：
✅ 1. 时间显示精确到秒 (00:00:00格式)
✅ 2. 窗口尺寸增加到1.5倍 (300x225)
✅ 3. 可拖动边框
✅ 4. 半显示/全显示模式切换
✅ 5. 折叠/展开功能
✅ 6. 主界面跳转功能

测试步骤：
1. 点击"创建悬浮小组件"
2. 测试拖动功能（拖动窗口边框）
3. 测试显示模式切换（全显示/半显示下拉菜单）
4. 测试折叠/展开（点击 - 或 + 按钮）
5. 测试学习功能（开始按钮，观察秒数变化）
6. 测试主界面跳转（点击主界面按钮）
        """)
        layout.addWidget(info_label)
        
        # 控制按钮
        create_btn = QPushButton("创建悬浮小组件")
        create_btn.clicked.connect(self.create_floating_widget)
        layout.addWidget(create_btn)
        
        test_drag_btn = QPushButton("测试拖动功能")
        test_drag_btn.clicked.connect(self.test_drag_function)
        layout.addWidget(test_drag_btn)
        
        test_display_btn = QPushButton("测试显示模式")
        test_display_btn.clicked.connect(self.test_display_mode)
        layout.addWidget(test_display_btn)
        
        test_collapse_btn = QPushButton("测试折叠功能")
        test_collapse_btn.clicked.connect(self.test_collapse_function)
        layout.addWidget(test_collapse_btn)
        
        test_main_btn = QPushButton("测试主界面跳转")
        test_main_btn.clicked.connect(self.test_main_window_function)
        layout.addWidget(test_main_btn)
        
        # 时间显示标签
        self.time_display_label = QLabel("当前时间: 00:00:00")
        layout.addWidget(self.time_display_label)
        
        # 悬浮小组件实例
        self.floating_widget = None
        
        # 测试定时器
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.update_time_display)
        self.test_timer.start(1000)  # 每秒更新
    
    def create_floating_widget(self):
        """创建悬浮小组件"""
        try:
            if self.floating_widget is None:
                self.status_label.setText("正在创建增强版悬浮小组件...")
                self.floating_widget = StudyFloatingWidget()
                
                # 连接信号
                self.floating_widget.start_study.connect(self.on_start_signal)
                self.floating_widget.stop_study.connect(self.on_stop_signal)
                self.floating_widget.show_main_window.connect(self.on_show_main_signal)
            
            self.floating_widget.show()
            self.floating_widget.raise_()
            self.floating_widget.activateWindow()
            self.status_label.setText("✅ 悬浮小组件已创建 - 尺寸: 300x225 (1.5倍)")
            print("✅ 增强版悬浮小组件已创建")
            print("📏 窗口尺寸: 300x225 (原来200x150的1.5倍)")
            print("🖱️ 可以拖动窗口边框移动位置")
            print("🔄 可以切换显示模式（全显示/半显示）")
            print("📁 可以折叠/展开内容")
            
        except Exception as e:
            self.status_label.setText(f"创建失败: {str(e)}")
            print(f"Error creating floating widget: {e}")
            import traceback
            traceback.print_exc()
    
    def test_drag_function(self):
        """测试拖动功能"""
        if self.floating_widget:
            self.status_label.setText("🖱️ 请尝试拖动悬浮小组件的边框")
            print("🖱️ 拖动测试：请用鼠标拖动悬浮小组件的任意位置")
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def test_display_mode(self):
        """测试显示模式"""
        if self.floating_widget:
            self.status_label.setText("🔄 请在悬浮小组件中切换显示模式下拉菜单")
            print("🔄 显示模式测试：")
            print("   - 全显示：始终置顶显示")
            print("   - 半显示：可被其他应用覆盖")
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def test_collapse_function(self):
        """测试折叠功能"""
        if self.floating_widget:
            self.status_label.setText("📁 请点击悬浮小组件的 - 或 + 按钮测试折叠")
            print("📁 折叠功能测试：")
            print("   - 点击 '-' 按钮折叠内容")
            print("   - 点击 '+' 按钮展开内容")
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def test_main_window_function(self):
        """测试主界面跳转功能"""
        if self.floating_widget:
            self.status_label.setText("🏠 请点击悬浮小组件的'主界面'按钮")
            print("🏠 主界面跳转测试：点击'主界面'按钮应该激活这个测试窗口")
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def update_time_display(self):
        """更新时间显示"""
        if self.floating_widget and hasattr(self.floating_widget, 'study_service'):
            try:
                current_seconds = self.floating_widget.study_service.get_current_duration_seconds()
                hours = current_seconds // 3600
                minutes = (current_seconds % 3600) // 60
                seconds = current_seconds % 60
                
                if hours > 0:
                    time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                else:
                    time_str = f"{minutes:02d}:{seconds:02d}"
                
                self.time_display_label.setText(f"当前时间: {time_str} (总计{current_seconds}秒)")
            except:
                self.time_display_label.setText("当前时间: 00:00:00")
    
    def on_start_signal(self):
        """开始学习信号"""
        self.status_label.setText("✅ 学习已开始，观察时间精确到秒的变化")
        print("✅ 学习已开始，时间显示应该精确到秒")
    
    def on_stop_signal(self):
        """停止学习信号"""
        self.status_label.setText("⏹️ 学习已停止")
        print("⏹️ 学习已停止")
    
    def on_show_main_signal(self):
        """显示主窗口信号"""
        self.status_label.setText("🏠 收到主界面跳转信号 - 功能正常！")
        print("🏠 主界面跳转信号触发 - 功能正常！")
        self.show()
        self.raise_()
        self.activateWindow()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.floating_widget:
            self.floating_widget.close()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建测试窗口
        window = EnhancedTestWindow()
        window.show()
        
        print("增强版悬浮小组件功能测试程序已启动")
        print("请按照界面提示进行全面测试")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
