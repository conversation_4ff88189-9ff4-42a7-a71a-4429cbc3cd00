"""
View Strategy Pattern for Task Manager

Provides different view layouts for the task management interface.
"""

from abc import ABC, abstractmethod
from datetime import date, datetime, timedelta
from typing import List, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
    QLabel, QPushButton, QFrame, QSplitter
)
from PyQt6.QtCore import Qt, pyqtSignal

from core.logger import LoggerMixin


class ViewStrategy(ABC):
    """抽象视图策略基类"""
    
    def __init__(self, parent_widget: QWidget):
        self.parent_widget = parent_widget
        self.layout = None
    
    @abstractmethod
    def create_layout(self) -> QVBoxLayout:
        """创建视图布局"""
        pass
    
    @abstractmethod
    def load_data(self, tasks: List[Dict], categories: List[Dict]):
        """加载数据到视图"""
        pass
    
    @abstractmethod
    def refresh_view(self):
        """刷新视图"""
        pass


class WeeklyView(ViewStrategy):
    """周视图策略"""
    
    def __init__(self, parent_widget: QWidget):
        super().__init__(parent_widget)
        self.weekly_table = None
        self.date_display = None
        self.current_week_start = None
    
    def create_layout(self) -> QVBoxLayout:
        """创建周视图布局"""
        layout = QVBoxLayout()
        
        # 创建顶部导航栏
        nav_layout = self.create_navigation_bar()
        layout.addLayout(nav_layout)
        
        # 创建日期显示
        self.date_display = self.create_date_display()
        layout.addWidget(self.date_display)
        
        # 创建周任务表格
        self.weekly_table = self.create_weekly_table()
        layout.addWidget(self.weekly_table)
        
        # 创建底部状态栏
        status_layout = self.create_status_bar()
        layout.addLayout(status_layout)
        
        self.layout = layout
        return layout
    
    def create_navigation_bar(self) -> QHBoxLayout:
        """创建导航栏"""
        nav_layout = QHBoxLayout()
        
        # 周导航按钮
        prev_week_btn = QPushButton("◀ 上周")
        current_week_btn = QPushButton("本周")
        next_week_btn = QPushButton("下周 ▶")
        
        prev_week_btn.clicked.connect(lambda: self.navigate_week(-1))
        current_week_btn.clicked.connect(lambda: self.navigate_week(0))
        next_week_btn.clicked.connect(lambda: self.navigate_week(1))
        
        nav_layout.addWidget(prev_week_btn)
        nav_layout.addWidget(current_week_btn)
        nav_layout.addWidget(next_week_btn)
        nav_layout.addStretch()
        
        return nav_layout
    
    def create_date_display(self) -> QLabel:
        """创建日期显示"""
        date_label = QLabel()
        date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        date_label.setStyleSheet("font-size: 14px; font-weight: bold; padding: 10px;")
        return date_label
    
    def create_weekly_table(self) -> QTableWidget:
        """创建周任务表格"""
        # 这里可以复用现有的 WeeklyTaskTable
        try:
            from ..weekly_task_table import WeeklyTaskTable
            table = WeeklyTaskTable()
            return table
        except ImportError:
            # 如果导入失败，创建基础表格
            table = QTableWidget()
            table.setColumnCount(8)  # 时间段 + 7天
            return table
    
    def create_status_bar(self) -> QHBoxLayout:
        """创建状态栏"""
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("就绪")
        self.task_count_label = QLabel("任务: 0")
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.task_count_label)
        
        return status_layout
    
    def navigate_week(self, offset: int):
        """导航到不同周"""
        if hasattr(self.weekly_table, 'navigate_week'):
            self.weekly_table.navigate_week(offset)
        self.update_date_display()
    
    def update_date_display(self):
        """更新日期显示"""
        if hasattr(self.weekly_table, 'current_week_start') and self.weekly_table.current_week_start:
            week_start = self.weekly_table.current_week_start
            week_end = week_start + timedelta(days=6)
            date_text = f"{week_start.strftime('%Y年%m月%d日')} - {week_end.strftime('%m月%d日')}"
            self.date_display.setText(date_text)
    
    def load_data(self, tasks: List[Dict], categories: List[Dict]):
        """加载数据到周视图"""
        if hasattr(self.weekly_table, 'refresh_data'):
            self.weekly_table.refresh_data()
        
        # 更新任务计数
        task_count = len(tasks)
        self.task_count_label.setText(f"任务: {task_count}")
        
        self.update_date_display()
    
    def refresh_view(self):
        """刷新周视图"""
        if hasattr(self.weekly_table, 'refresh_data'):
            self.weekly_table.refresh_data()


class DailyView(ViewStrategy):
    """日视图策略"""
    
    def __init__(self, parent_widget: QWidget):
        super().__init__(parent_widget)
        self.current_date = date.today()
        self.task_list = None
    
    def create_layout(self) -> QVBoxLayout:
        """创建日视图布局"""
        layout = QVBoxLayout()
        
        # 日期导航
        nav_layout = QHBoxLayout()
        prev_day_btn = QPushButton("◀ 昨天")
        today_btn = QPushButton("今天")
        next_day_btn = QPushButton("明天 ▶")
        
        prev_day_btn.clicked.connect(lambda: self.navigate_day(-1))
        today_btn.clicked.connect(lambda: self.navigate_day(0))
        next_day_btn.clicked.connect(lambda: self.navigate_day(1))
        
        nav_layout.addWidget(prev_day_btn)
        nav_layout.addWidget(today_btn)
        nav_layout.addWidget(next_day_btn)
        nav_layout.addStretch()
        
        layout.addLayout(nav_layout)
        
        # 日期显示
        self.date_label = QLabel()
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.date_label.setStyleSheet("font-size: 16px; font-weight: bold; padding: 15px;")
        layout.addWidget(self.date_label)
        
        # 任务列表
        self.task_list = QTableWidget()
        self.task_list.setColumnCount(4)  # 时间、标题、状态、操作
        self.task_list.setHorizontalHeaderLabels(["时间", "任务", "状态", "操作"])
        layout.addWidget(self.task_list)
        
        self.layout = layout
        self.update_date_display()
        return layout
    
    def navigate_day(self, offset: int):
        """导航到不同日期"""
        if offset == 0:
            self.current_date = date.today()
        else:
            self.current_date += timedelta(days=offset)
        
        self.update_date_display()
        self.refresh_view()
    
    def update_date_display(self):
        """更新日期显示"""
        date_text = self.current_date.strftime('%Y年%m月%d日 %A')
        self.date_label.setText(date_text)
    
    def load_data(self, tasks: List[Dict], categories: List[Dict]):
        """加载数据到日视图"""
        # 过滤当天的任务
        daily_tasks = [
            task for task in tasks 
            if task.get('start_date', '').startswith(self.current_date.strftime('%Y-%m-%d'))
        ]
        
        self.task_list.setRowCount(len(daily_tasks))
        
        for row, task in enumerate(daily_tasks):
            # 填充任务数据到表格
            pass  # 具体实现
    
    def refresh_view(self):
        """刷新日视图"""
        # 重新加载当天数据
        pass


class ListView(ViewStrategy):
    """列表视图策略"""
    
    def __init__(self, parent_widget: QWidget):
        super().__init__(parent_widget)
        self.task_table = None
        self.filter_frame = None
    
    def create_layout(self) -> QVBoxLayout:
        """创建列表视图布局"""
        layout = QVBoxLayout()
        
        # 创建过滤器
        self.filter_frame = self.create_filter_frame()
        layout.addWidget(self.filter_frame)
        
        # 创建任务表格
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(6)  # 标题、分类、优先级、状态、截止日期、操作
        self.task_table.setHorizontalHeaderLabels([
            "任务标题", "分类", "优先级", "状态", "截止日期", "操作"
        ])
        layout.addWidget(self.task_table)
        
        self.layout = layout
        return layout
    
    def create_filter_frame(self) -> QFrame:
        """创建过滤器框架"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box)
        
        filter_layout = QHBoxLayout(frame)
        
        # 添加过滤控件
        filter_layout.addWidget(QLabel("过滤:"))
        # 这里可以添加更多过滤控件
        filter_layout.addStretch()
        
        return frame
    
    def load_data(self, tasks: List[Dict], categories: List[Dict]):
        """加载数据到列表视图"""
        self.task_table.setRowCount(len(tasks))
        
        for row, task in enumerate(tasks):
            # 填充任务数据到表格
            pass  # 具体实现
    
    def refresh_view(self):
        """刷新列表视图"""
        # 重新加载数据
        pass