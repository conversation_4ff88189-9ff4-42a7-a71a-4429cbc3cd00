"""
测试暂停时时间显示修复
"""

import sys
from pathlib import Path
import time

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config
from modules.study_tracker.study_service import StudyService


def test_pause_time_display():
    """测试暂停时时间显示"""
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建学习服务
        study_service = StudyService()
        
        # 确保有科目
        subjects = study_service.get_subjects()
        if not subjects:
            print("创建默认科目...")
            subject = study_service.create_subject(
                name="暂停测试科目",
                description="用于测试暂停时间显示的科目",
                color="#e74c3c"
            )
            if subject:
                subject_id = subject.id
                print(f"创建科目成功，ID: {subject_id}")
            else:
                print("创建科目失败")
                return
        else:
            subject_id = subjects[0]['id']
            print(f"使用现有科目，ID: {subject_id}")
        
        # 开始学习会话
        print("\n🔥 开始学习会话...")
        session = study_service.start_session(
            subject_id=subject_id,
            title="暂停时间显示测试",
            planned_duration=10
        )
        
        if session:
            print("✅ 学习会话创建成功")
            
            # 运行5秒
            print("\n📚 学习5秒...")
            for i in range(5):
                duration_seconds = study_service.get_current_duration_seconds()
                hours = duration_seconds // 3600
                minutes = (duration_seconds % 3600) // 60
                seconds = duration_seconds % 60
                time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                
                print(f"  第{i+1}秒: {time_str} (状态: {study_service.current_session_cache['status']})")
                time.sleep(1)
            
            # 暂停
            print("\n⏸️ 暂停学习...")
            pause_success = study_service.pause_session()
            if pause_success:
                print("✅ 暂停成功")
                
                # 获取暂停时的时间
                pause_time_seconds = study_service.get_current_duration_seconds()
                hours = pause_time_seconds // 3600
                minutes = (pause_time_seconds % 3600) // 60
                seconds = pause_time_seconds % 60
                pause_time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                
                print(f"📍 暂停时的时间: {pause_time_str}")
                
                # 等待3秒，检查时间是否保持不变
                print("\n⏳ 等待3秒，检查时间是否保持不变...")
                for i in range(3):
                    duration_seconds = study_service.get_current_duration_seconds()
                    hours = duration_seconds // 3600
                    minutes = (duration_seconds % 3600) // 60
                    seconds = duration_seconds % 60
                    time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                    
                    status = study_service.current_session_cache['status']
                    print(f"  等待第{i+1}秒: {time_str} (状态: {status})")
                    
                    # 检查时间是否保持不变
                    if time_str == pause_time_str:
                        print(f"    ✅ 时间保持不变: {time_str}")
                    else:
                        print(f"    ❌ 时间发生变化: {pause_time_str} → {time_str}")
                    
                    time.sleep(1)
                
                # 恢复学习
                print("\n▶️ 恢复学习...")
                resume_success = study_service.resume_session()
                if resume_success:
                    print("✅ 恢复成功")
                    
                    # 继续运行3秒
                    print("\n📚 继续学习3秒...")
                    for i in range(3):
                        duration_seconds = study_service.get_current_duration_seconds()
                        hours = duration_seconds // 3600
                        minutes = (duration_seconds % 3600) // 60
                        seconds = duration_seconds % 60
                        time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                        
                        status = study_service.current_session_cache['status']
                        total_break = study_service.current_session_cache['total_break_seconds']
                        print(f"  恢复第{i+1}秒: {time_str} (状态: {status}, 累计暂停: {total_break}秒)")
                        time.sleep(1)
                    
                else:
                    print("❌ 恢复失败")
            else:
                print("❌ 暂停失败")
            
            # 停止会话
            print("\n⏹️ 停止学习会话...")
            success = study_service.complete_session()
            if success:
                print("✅ 学习会话已完成")
            else:
                print("❌ 停止会话失败")
                
        else:
            print("❌ 学习会话创建失败")
        
        print("\n📊 测试总结:")
        print("✅ 预期结果: 暂停时时间应该保持在暂停时的值")
        print("✅ 预期结果: 恢复后时间应该从暂停时的值继续增长")
        print("✅ 预期结果: 累计暂停时间应该被正确扣除")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_pause_time_display()
