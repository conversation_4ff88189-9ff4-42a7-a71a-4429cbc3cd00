# 造神计划 - 用户使用手册

## 📖 软件简介

**造神计划**是一个功能强大的个人管理系统，集成了任务管理、文件管理、系统监控、学习追踪等多种功能，帮助您高效管理日常工作和生活。

### 🌟 主要特色
- 🎯 **统一任务管理** - 支持多种视图和主题
- 📁 **智能文件管理** - 文件书签、标签、历史记录
- 📊 **系统监控** - 实时监控系统性能
- 📚 **学习追踪** - 记录学习时间和进度
- 🖥️ **桌面小组件** - 便捷的桌面工具
- 🎨 **多主题支持** - 湖面蓝、默认、深色主题

## 🚀 快速开始

### 系统要求
- **操作系统**: Windows 10/11
- **Python版本**: 3.9 或更高版本
- **内存**: 至少 4GB RAM
- **存储空间**: 至少 500MB 可用空间

### 安装方法

#### 方法一：直接运行（开发环境）
1. 确保已安装 Python 3.9+
2. 安装依赖包：
   ```bash
   pip install PyQt6 sqlalchemy psutil cryptography keyring requests pyyaml watchdog send2trash apscheduler pandas openpyxl pillow tqdm click
   ```
3. 下载项目文件到本地
4. 进入 `source_code` 目录
5. 运行主程序：
   ```bash
   python main.py
   ```

#### 方法二：使用启动器
```bash
python launcher.py
```
启动器会自动检测是否已有实例运行，避免重复启动。

#### 方法三：构建安装包（推荐）
1. 运行构建脚本：
   ```bash
   python build_installer.py
   ```
2. 在 `installer` 目录找到生成的安装程序
3. 双击安装程序进行安装

## 🎯 主要功能使用指南

### 1. 任务管理系统

#### 启动任务管理器
- 运行主程序后，默认显示任务管理界面
- 界面采用湖面蓝主题，显示周视图

#### 基本操作

**创建新任务**
1. 点击 `➕ 新建任务` 按钮
2. 填写任务信息：
   - 任务标题（必填）
   - 任务描述
   - 优先级（高/中/低）
   - 截止日期
   - 开始日期
   - 标签
3. 点击确定保存

**编辑任务**
1. 在任务列表中双击任务
2. 修改任务信息
3. 保存更改

**删除任务**
1. 选中要删除的任务
2. 右键选择删除，或按 Delete 键
3. 确认删除

**任务状态管理**
- 待办：新创建的任务
- 进行中：正在执行的任务
- 已完成：完成的任务
- 已取消：取消的任务

#### 视图切换

**周视图（默认）**
- 显示一周的任务安排
- 可以拖拽任务到不同时间段
- 支持周导航（上周/本周/下周）

**日视图**
- 显示单日的详细任务
- 按时间段排列任务
- 支持日期导航

**列表视图**
- 显示所有任务的列表
- 支持排序和过滤
- 适合批量管理任务

#### 主题切换
在界面右上角选择主题：
- **湖面蓝**：清新的蓝色渐变主题（推荐）
- **默认**：简洁的白色主题
- **深色**：护眼的深色主题

### 2. 桌面小组件

#### 启动桌面小组件
1. 在主界面点击 `🖥️ 启动桌面小组件`
2. 桌面上会出现任务管理小组件
3. 小组件显示当前任务和快捷操作

#### 小组件功能
- **任务快览**：显示今日重要任务
- **快速添加**：直接在桌面添加任务
- **状态更新**：快速更改任务状态
- **拖拽移动**：可以拖拽到任意位置

#### 小组件管理
- **显示/隐藏**：双击系统托盘图标
- **关闭小组件**：右键小组件选择关闭
- **重启小组件**：在主界面重新启动

### 3. 文件管理功能

#### 文件书签
1. 在文件管理器中右键文件/文件夹
2. 选择"添加到造神计划书签"
3. 在软件中快速访问常用文件

#### 文件标签
- 为文件添加自定义标签
- 按标签分类和搜索文件
- 支持多标签组合

#### 文件历史
- 自动记录文件访问历史
- 快速找到最近使用的文件
- 支持历史记录搜索

### 4. 系统监控

#### 性能监控
- **CPU使用率**：实时显示CPU占用
- **内存使用**：显示内存使用情况
- **磁盘空间**：监控磁盘剩余空间
- **网络状态**：显示网络连接状态

#### 进程管理
- 查看运行中的进程
- 监控进程资源使用
- 结束异常进程

#### 系统告警
- 资源使用过高时自动告警
- 磁盘空间不足提醒
- 系统异常状态通知

### 5. 学习追踪

#### 学习计划
1. 创建学习科目
2. 设置学习目标
3. 制定学习计划

#### 时间记录
- **开始学习**：点击开始按钮
- **暂停/继续**：支持学习中断
- **结束学习**：记录学习时长
- **学习笔记**：添加学习内容记录

#### 学习统计
- 每日学习时长统计
- 学习进度可视化
- 学习效率分析
- 学习成果报告

## ⚙️ 高级设置

### 配置文件位置
- **Windows**: `%APPDATA%\造神计划\config\`
- **配置文件**: `config.json`
- **数据库**: `personal_manager.db`
- **日志文件**: `logs\` 目录

### 自定义配置

#### 修改主题
1. 打开配置文件 `config.json`
2. 修改 `task_manager.theme` 值：
   - `"lake_blue"` - 湖面蓝主题
   - `"default"` - 默认主题
   - `"dark"` - 深色主题

#### 修改默认视图
1. 修改 `task_manager.view` 值：
   - `"weekly"` - 周视图
   - `"daily"` - 日视图
   - `"list"` - 列表视图

#### 自动启动设置
1. 在安装时选择"开机自动启动"
2. 或在系统设置中手动添加启动项

### 数据备份
1. 定期备份配置目录
2. 导出任务数据：主界面 → 导出 → 选择格式
3. 重要数据建议云端同步

## 🔧 故障排除

### 常见问题

#### 1. 程序无法启动
**症状**：双击程序无反应或报错
**解决方案**：
1. 检查Python环境是否正确安装
2. 确认所有依赖包已安装
3. 查看错误日志：`logs\main.log`
4. 尝试使用启动器：`python launcher.py`

#### 2. 界面显示异常
**症状**：界面布局错乱或显示不完整
**解决方案**：
1. 重启程序
2. 重置界面配置：删除配置文件中的界面设置
3. 更换主题尝试
4. 检查显示器分辨率和缩放设置

#### 3. 桌面小组件无法启动
**症状**：点击启动按钮无反应
**解决方案**：
1. 检查是否有防火墙阻止
2. 查看进程管理器是否有相关进程
3. 重启主程序
4. 查看小组件日志文件

#### 4. 数据丢失
**症状**：任务或配置数据消失
**解决方案**：
1. 检查数据库文件是否存在
2. 从备份恢复数据
3. 查看日志文件确定问题原因
4. 重新创建数据库

#### 5. 性能问题
**症状**：程序运行缓慢或卡顿
**解决方案**：
1. 关闭不必要的桌面小组件
2. 清理日志文件
3. 重启程序释放内存
4. 检查系统资源使用情况

### 日志文件位置
- **主程序日志**：`logs\main.log`
- **桌面小组件日志**：`logs\desktop_widget.log`
- **系统监控日志**：`logs\system_monitor.log`

### 获取技术支持
1. 查看日志文件确定错误信息
2. 记录操作步骤和错误现象
3. 提供系统环境信息
4. 联系技术支持团队

## 🎨 个性化定制

### 自定义主题
1. 复制现有主题文件
2. 修改颜色和样式配置
3. 在主题选择中添加新主题

### 自定义快捷键
1. 编辑配置文件中的快捷键设置
2. 重启程序生效

### 插件扩展
- 支持自定义插件开发
- 插件接口文档：`docs\plugin_api.md`
- 示例插件：`plugins\examples\`

## 📚 使用技巧

### 高效任务管理
1. **使用标签分类**：为任务添加项目、优先级等标签
2. **合理安排时间**：利用周视图规划时间
3. **定期回顾**：每周回顾任务完成情况
4. **设置提醒**：为重要任务设置截止日期提醒

### 桌面小组件技巧
1. **位置固定**：将小组件放在屏幕固定位置
2. **快速操作**：利用小组件快速添加临时任务
3. **状态监控**：通过小组件随时了解任务状态

### 学习追踪技巧
1. **番茄工作法**：25分钟专注学习，5分钟休息
2. **目标设定**：设置每日/每周学习目标
3. **进度可视化**：利用统计图表了解学习进度

## 🔄 更新和维护

### 软件更新
1. 定期检查更新通知
2. 备份数据后进行更新
3. 查看更新日志了解新功能

### 数据维护
1. 定期清理过期任务
2. 备份重要数据
3. 优化数据库性能

### 系统优化
1. 定期重启程序释放内存
2. 清理临时文件和日志
3. 检查磁盘空间

## 📞 联系我们

### 技术支持
- **邮箱**：<EMAIL>
- **QQ群**：123456789
- **微信群**：扫描二维码加入

### 反馈建议
- **功能建议**：<EMAIL>
- **Bug报告**：<EMAIL>
- **用户论坛**：https://forum.zaoshenjihua.com

### 开源项目
- **GitHub**：https://github.com/zaoshenjihua/personal-manager
- **贡献指南**：CONTRIBUTING.md
- **开发文档**：docs/development.md

---

**造神计划 - 让每一天都更高效！** 🚀

*版本：1.0.0*  
*更新日期：2025年1月*  
*文档版本：v1.0*