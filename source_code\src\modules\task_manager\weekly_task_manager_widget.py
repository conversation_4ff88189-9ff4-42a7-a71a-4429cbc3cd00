"""
Weekly Task Manager Widget - New Main Interface

This is the new main task manager interface that completely replaces
the old task management components. It features:
- Week and date display
- Weekly task table with inline editing
- Enhanced user experience
"""

from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QSplitter, QMessageBox, QComboBox, QLineEdit, QDateEdit, QTextEdit,
    QDialog, QDialogButtonBox, QFormLayout, QSpinBox, QCheckBox,
    QGroupBox, QToolBar, QStatusBar, QTimeEdit
)
from PyQt6.QtCore import Qt, pyqtSignal, QDate, QTimer, QTime
from PyQt6.QtGui import QFont, QIcon, QAction

from core.logger import LoggerMixin
from models.task_models import Task, TaskStatus, TaskPriority, TaskCategory
from .task_service import TaskService
from .weekly_task_table import WeeklyTaskTable
from .date_week_display import DateWeekDisplay
from .desktop_widget_controller import DesktopWidgetController


class LakeBlueTheme:
    """湖面蓝主题色彩系统"""

    # 主色调 - 湖蓝渐变色系
    DEEP_BLUE = "#023047"      # 深蓝
    MEDIUM_BLUE = "#0096C7"    # 中蓝
    LIGHT_BLUE = "#CAF0F8"     # 浅蓝

    # 辅助色彩
    WAVE_WHITE = "#CAF0F8"     # 浪花白
    SEAWEED_GREEN = "#4CC9F0"  # 水藻绿
    MINT_GREEN = "#80FFDB"     # 薄荷绿（已完成）
    CORAL_RED = "#FF9E90"      # 珊瑚红（未完成）

    # 透明度变体
    GLASS_WHITE = "rgba(255,255,255,0.2)"  # 磨砂玻璃
    SEMI_TRANSPARENT = "rgba(202,240,248,0.6)"  # 半透明浅蓝

    @staticmethod
    def get_gradient_style(start_color: str, end_color: str) -> str:
        """获取渐变样式"""
        return f"background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {start_color}, stop:1 {end_color});"

    @staticmethod
    def get_main_widget_style() -> str:
        """获取主界面样式"""
        return f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {LakeBlueTheme.WAVE_WHITE},
                    stop:0.5 #F0F8FF,
                    stop:1 {LakeBlueTheme.LIGHT_BLUE});
                font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
            }}
        """

    @staticmethod
    def get_header_style() -> str:
        """获取头部样式"""
        return f"""
            QFrame {{
                {LakeBlueTheme.get_gradient_style(LakeBlueTheme.SEAWEED_GREEN, LakeBlueTheme.MEDIUM_BLUE)}
                border-radius: 10px;
                border: 2px solid {LakeBlueTheme.LIGHT_BLUE};
            }}
            QLabel {{
                color: white;
                font-weight: bold;
            }}
        """

    @staticmethod
    def get_toolbar_style() -> str:
        """获取工具栏样式"""
        return f"""
            QFrame {{
                background-color: white;
                border-radius: 8px;
                border: 1px solid {LakeBlueTheme.LIGHT_BLUE};
                padding: 5px;
            }}
            QPushButton {{
                background-color: {LakeBlueTheme.LIGHT_BLUE};
                color: {LakeBlueTheme.DEEP_BLUE};
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
                margin: 3px;
                min-width: 80px;
                min-height: 30px;
            }}
            QPushButton:hover {{
                background-color: {LakeBlueTheme.SEAWEED_GREEN};
                color: white;
            }}
            QPushButton:pressed {{
                background-color: {LakeBlueTheme.MEDIUM_BLUE};
            }}
        """

    @staticmethod
    def get_content_style() -> str:
        """获取内容区域样式"""
        return f"""
            QFrame {{
                background-color: white;
                border-radius: 10px;
                border: 1px solid {LakeBlueTheme.LIGHT_BLUE};
            }}
        """

    @staticmethod
    def get_status_bar_style() -> str:
        """获取状态栏样式"""
        return f"""
            QFrame {{
                background-color: {LakeBlueTheme.LIGHT_BLUE};
                border-radius: 6px;
                border: 1px solid {LakeBlueTheme.MEDIUM_BLUE};
                padding: 5px;
            }}
            QLabel {{
                color: {LakeBlueTheme.DEEP_BLUE};
                font-weight: bold;
            }}
        """


class SimpleCellTaskDialog(QDialog):
    """Simplified dialog for adding tasks in cells (no date/time options)"""

    def __init__(self, task_data=None, categories=None, parent=None):
        super().__init__(parent)
        self.task_data = task_data or {}
        self.categories = categories or []
        self.init_ui()
        self.populate_form()

    def init_ui(self):
        """Initialize simplified dialog UI"""
        self.setWindowTitle("添加任务")
        self.setModal(True)
        self.resize(350, 300)

        layout = QVBoxLayout(self)

        # Form layout
        form_layout = QFormLayout()

        # Title
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("输入任务标题...")
        form_layout.addRow("标题:", self.title_edit)

        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("输入任务描述...")
        form_layout.addRow("描述:", self.description_edit)

        # Status
        self.status_combo = QComboBox()
        self.status_combo.addItems(["待处理", "进行中", "已完成", "已取消"])
        form_layout.addRow("状态:", self.status_combo)

        # Priority
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["低", "普通", "高", "紧急"])
        self.priority_combo.setCurrentText("普通")
        form_layout.addRow("优先级:", self.priority_combo)



        layout.addLayout(form_layout)

        # Buttons
        button_layout = QHBoxLayout()

        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_button)

        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)

    def populate_form(self):
        """Populate form with task data"""
        if not self.task_data:
            return

        self.title_edit.setText(self.task_data.get('title', ''))
        self.description_edit.setPlainText(self.task_data.get('description', ''))

        # Map English status to Chinese
        status_map = {
            'pending': '待处理',
            'in_progress': '进行中',
            'completed': '已完成',
            'cancelled': '已取消'
        }
        status = self.task_data.get('status', '待处理')
        chinese_status = status_map.get(status, status)
        index = self.status_combo.findText(chinese_status)
        if index >= 0:
            self.status_combo.setCurrentIndex(index)

        # Map English priority to Chinese
        priority_map = {
            'low': '低',
            'normal': '普通',
            'high': '高',
            'urgent': '紧急'
        }
        priority = self.task_data.get('priority', '普通')
        chinese_priority = priority_map.get(priority, priority)
        index = self.priority_combo.findText(chinese_priority)
        if index >= 0:
            self.priority_combo.setCurrentIndex(index)



    def get_task_data(self):
        """Get task data from form"""
        # Map Chinese status back to English
        status_reverse_map = {
            '待处理': 'pending',
            '进行中': 'in_progress',
            '已完成': 'completed',
            '已取消': 'cancelled'
        }

        # Map Chinese priority back to English
        priority_reverse_map = {
            '低': 'low',
            '普通': 'normal',
            '高': 'high',
            '紧急': 'urgent'
        }

        # Get category from task_data and set appropriate tags
        category = self.task_data.get('category', '其他')
        tags = category  # Use category as tag for proper task categorization

        return {
            'title': self.title_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'status': status_reverse_map.get(self.status_combo.currentText(), 'pending'),
            'priority': priority_reverse_map.get(self.priority_combo.currentText(), 'normal'),
            'date': self.task_data.get('date', date.today()),  # Use date from task_data
            'due_date': self.task_data.get('date', date.today()),  # Use same date as due date
            'category': category,  # Preserve original category
            'tags': tags,  # Add tags for proper categorization
        }


class TaskEditDialog(QDialog):
    """Dialog for editing task details"""

    def __init__(self, task_data=None, categories=None, parent=None):
        super().__init__(parent)
        self.task_data = task_data or {}
        self.categories = categories or []
        self.init_ui()
        self.populate_form()
    
    def init_ui(self):
        """Initialize dialog UI"""
        self.setWindowTitle("任务详情")
        self.setModal(True)
        self.resize(400, 500)
        
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Title
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("输入任务标题...")
        form_layout.addRow("标题:", self.title_edit)
        
        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("输入任务描述...")
        form_layout.addRow("描述:", self.description_edit)
        
        # Status
        self.status_combo = QComboBox()
        self.status_combo.addItems(["待处理", "进行中", "已完成", "已取消"])
        form_layout.addRow("状态:", self.status_combo)

        # Priority
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["低", "普通", "高", "紧急"])
        form_layout.addRow("优先级:", self.priority_combo)
        
        # Category
        self.category_combo = QComboBox()
        self.category_combo.addItem("无分类", None)
        for category in self.categories:
            self.category_combo.addItem(category.name, category.id)
        form_layout.addRow("分类:", self.category_combo)

        # Task date (when to execute the task)
        self.task_date_edit = QDateEdit()
        self.task_date_edit.setDate(QDate.currentDate())
        self.task_date_edit.setCalendarPopup(True)
        form_layout.addRow("任务日期:", self.task_date_edit)

        # Time period
        self.time_period_combo = QComboBox()
        self.time_period_combo.addItems(["全天", "上午", "下午", "晚上"])
        form_layout.addRow("时间段:", self.time_period_combo)

        # Due date
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setDate(QDate.currentDate())
        self.due_date_edit.setCalendarPopup(True)
        form_layout.addRow("截止日期:", self.due_date_edit)
        

        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox()
        button_box.addButton("确定", QDialogButtonBox.ButtonRole.AcceptRole)
        button_box.addButton("取消", QDialogButtonBox.ButtonRole.RejectRole)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def populate_form(self):
        """Populate form with task data"""
        if not self.task_data:
            return
        
        self.title_edit.setText(self.task_data.get('title', ''))
        self.description_edit.setPlainText(self.task_data.get('description', ''))

        # Map English status to Chinese
        status_map = {
            'pending': '待处理',
            'in_progress': '进行中',
            'completed': '已完成',
            'cancelled': '已取消'
        }
        status = self.task_data.get('status', '待处理')
        chinese_status = status_map.get(status, status)
        index = self.status_combo.findText(chinese_status)
        if index >= 0:
            self.status_combo.setCurrentIndex(index)

        # Map English priority to Chinese
        priority_map = {
            'low': '低',
            'normal': '普通',
            'high': '高',
            'urgent': '紧急'
        }
        priority = self.task_data.get('priority', '普通')
        chinese_priority = priority_map.get(priority, priority)
        index = self.priority_combo.findText(chinese_priority)
        if index >= 0:
            self.priority_combo.setCurrentIndex(index)
        
        category_id = self.task_data.get('category_id')
        if category_id:
            index = self.category_combo.findData(category_id)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)

        # Set task date
        if 'date' in self.task_data and self.task_data['date']:
            if isinstance(self.task_data['date'], datetime):
                qdate = QDate(self.task_data['date'].date())
            elif isinstance(self.task_data['date'], date):
                qdate = QDate(self.task_data['date'])
            else:
                qdate = QDate.currentDate()
            self.task_date_edit.setDate(qdate)

        # Set time period from tags
        tags = self.task_data.get('tags', '')
        if isinstance(tags, list):
            tags_str = ', '.join(tags)
        else:
            tags_str = str(tags)

        if '上午' in tags_str:
            self.time_period_combo.setCurrentText("上午")
        elif '下午' in tags_str:
            self.time_period_combo.setCurrentText("下午")
        elif '晚上' in tags_str:
            self.time_period_combo.setCurrentText("晚上")
        elif '全天' in tags_str:
            self.time_period_combo.setCurrentText("全天")
        else:
            self.time_period_combo.setCurrentText("全天")  # Default

        if 'due_date' in self.task_data and self.task_data['due_date']:
            if isinstance(self.task_data['due_date'], datetime):
                qdate = QDate(self.task_data['due_date'].date())
            elif isinstance(self.task_data['due_date'], date):
                qdate = QDate(self.task_data['due_date'])
            else:
                qdate = QDate.currentDate()
            self.due_date_edit.setDate(qdate)
        

    
    def get_task_data(self):
        """Get task data from form"""
        # Map Chinese status back to English for database
        status_reverse_map = {
            '待处理': 'pending',
            '进行中': 'in_progress',
            '已完成': 'completed',
            '已取消': 'cancelled'
        }
        # Map Chinese priority back to English for database
        priority_reverse_map = {
            '低': 'low',
            '普通': 'normal',
            '高': 'high',
            '紧急': 'urgent'
        }

        # Add time period as tag
        time_period = self.time_period_combo.currentText()
        user_tags = [time_period] if time_period else []

        return {
            'title': self.title_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'status': status_reverse_map.get(self.status_combo.currentText(), 'pending'),
            'priority': priority_reverse_map.get(self.priority_combo.currentText(), 'normal'),
            'date': self.task_date_edit.date().toPyDate(),  # Task execution date
            'due_date': self.due_date_edit.date().toPyDate(),
            'tags': user_tags
        }


class WeeklyTaskManagerWidget(QWidget, LoggerMixin):
    """New main task manager widget with weekly view"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.task_service = TaskService()
        self.current_tasks = []
        self.categories = []

        # Initialize desktop widget controller
        self.desktop_widget_controller = DesktopWidgetController(self)
        self.desktop_widget_controller.status_changed.connect(self.on_widget_status_changed)
        self.desktop_widget_controller.error_occurred.connect(self.on_widget_error)

        self.init_ui()
        self.connect_signals()
        self.load_data()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Create header section with date/week display
        self.create_header_section()
        layout.addWidget(self.header_frame)
        
        # Create toolbar
        self.create_toolbar()
        layout.addWidget(self.toolbar_frame)
        
        # Create main content with weekly table
        self.create_main_content()
        layout.addWidget(self.main_content)
        
        # Create status bar
        self.create_status_bar()
        layout.addWidget(self.status_bar)
        
        # Set window properties
        self.setWindowTitle("任务管理 - 湖面蓝主题")
        self.setMinimumSize(1000, 700)

        # Apply Lake Blue theme styles
        self.apply_lake_blue_theme()
    
    def create_header_section(self):
        """Create header section with date and week display"""
        self.header_frame = QFrame()
        self.header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        self.header_frame.setMaximumHeight(150)  # 增加高度以避免遮挡
        
        header_layout = QHBoxLayout(self.header_frame)
        header_layout.setContentsMargins(10, 10, 10, 10)
        
        # Date and week display
        self.date_week_display = DateWeekDisplay()
        header_layout.addWidget(self.date_week_display)

        # Right side: Desktop widget toggle and week navigation
        right_layout = QVBoxLayout()
        right_layout.setSpacing(5)

        # Desktop widget toggle switch (at the top right)
        self.create_desktop_widget_toggle()
        right_layout.addWidget(self.widget_toggle_frame)

        # Week navigation
        nav_layout = QVBoxLayout()
        nav_layout.setSpacing(5)
        
        # Week navigation buttons
        nav_buttons_layout = QHBoxLayout()
        
        self.prev_week_btn = QPushButton("◀ 上周")
        self.prev_week_btn.clicked.connect(lambda: self.navigate_week(-1))
        nav_buttons_layout.addWidget(self.prev_week_btn)
        
        self.current_week_btn = QPushButton("本周")
        self.current_week_btn.clicked.connect(lambda: self.navigate_week(0))
        nav_buttons_layout.addWidget(self.current_week_btn)
        
        self.next_week_btn = QPushButton("下周 ▶")
        self.next_week_btn.clicked.connect(lambda: self.navigate_week(1))
        nav_buttons_layout.addWidget(self.next_week_btn)
        
        nav_layout.addLayout(nav_buttons_layout)
        
        # Week info label
        self.week_info_label = QLabel()
        self.week_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font = QFont()
        font.setPointSize(10)
        font.setBold(True)
        self.week_info_label.setFont(font)
        nav_layout.addWidget(self.week_info_label)

        # Add navigation to right layout
        right_layout.addLayout(nav_layout)

        # Add right layout to header
        header_layout.addLayout(right_layout)
        header_layout.setStretch(0, 2)
        header_layout.setStretch(1, 1)

    def create_desktop_widget_toggle(self):
        """Create desktop widget toggle switch"""
        self.widget_toggle_frame = QFrame()
        self.widget_toggle_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        self.widget_toggle_frame.setMaximumHeight(50)
        self.widget_toggle_frame.setMaximumWidth(300)

        toggle_layout = QHBoxLayout(self.widget_toggle_frame)
        toggle_layout.setContentsMargins(10, 5, 10, 5)

        # Toggle label
        toggle_label = QLabel("桌面插件:")
        toggle_label.setFont(QFont("Microsoft YaHei", 9))
        toggle_layout.addWidget(toggle_label)

        # Toggle switch (checkbox)
        self.widget_toggle_checkbox = QCheckBox()
        self.widget_toggle_checkbox.setText("启用")
        self.widget_toggle_checkbox.setFont(QFont("Microsoft YaHei", 9))
        self.widget_toggle_checkbox.toggled.connect(self.on_widget_toggle_changed)
        toggle_layout.addWidget(self.widget_toggle_checkbox)

        # Status label
        self.widget_status_label = QLabel("状态: 检查中...")
        self.widget_status_label.setFont(QFont("Microsoft YaHei", 8))
        self.widget_status_label.setStyleSheet("color: #666666;")
        toggle_layout.addWidget(self.widget_status_label)

        toggle_layout.addStretch()

        # Load initial state
        self.load_widget_toggle_state()

        # Apply styling
        self.widget_toggle_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
            }
            QCheckBox {
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #6c757d;
                border-radius: 3px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #007bff;
                border-radius: 3px;
                background-color: #007bff;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QCheckBox::indicator:checked:hover {
                background-color: #0056b3;
            }
        """)
    
    def create_toolbar(self):
        """Create toolbar with action buttons"""
        self.toolbar_frame = QFrame()
        self.toolbar_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        self.toolbar_frame.setMinimumHeight(60)  # 增加最小高度确保按钮完整显示
        self.toolbar_frame.setMaximumHeight(80)  # 增加最大高度

        toolbar_layout = QHBoxLayout(self.toolbar_frame)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)  # 增加边距
        toolbar_layout.setSpacing(10)  # 增加按钮间距

        # Add task button - 增加按钮尺寸
        self.add_task_btn = QPushButton("➕ 添加任务")
        self.add_task_btn.setMinimumSize(120, 35)  # 设置最小尺寸
        self.add_task_btn.clicked.connect(self.create_new_task)
        toolbar_layout.addWidget(self.add_task_btn)

        # Refresh button - 增加按钮尺寸
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.setMinimumSize(100, 35)  # 设置最小尺寸
        self.refresh_btn.clicked.connect(self.load_data)
        toolbar_layout.addWidget(self.refresh_btn)
        
        toolbar_layout.addStretch()
        
        # View options
        self.view_options_combo = QComboBox()
        self.view_options_combo.addItems(["显示所有任务", "仅显示未完成", "仅显示本周"])
        self.view_options_combo.currentTextChanged.connect(self.filter_tasks)
        toolbar_layout.addWidget(self.view_options_combo)
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索任务...")
        self.search_edit.textChanged.connect(self.search_tasks)
        toolbar_layout.addWidget(self.search_edit)
    
    def create_main_content(self):
        """Create main content area with weekly task table"""
        self.main_content = QFrame()
        self.main_content.setFrameStyle(QFrame.Shape.StyledPanel)
        
        content_layout = QVBoxLayout(self.main_content)
        content_layout.setContentsMargins(5, 5, 5, 5)
        
        # Weekly task table
        self.weekly_table = WeeklyTaskTable()
        content_layout.addWidget(self.weekly_table)
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = QFrame()
        self.status_bar.setFrameStyle(QFrame.Shape.StyledPanel)
        self.status_bar.setMinimumHeight(40)  # 增加最小高度
        self.status_bar.setMaximumHeight(50)  # 增加最大高度

        status_layout = QHBoxLayout(self.status_bar)
        status_layout.setContentsMargins(20, 8, 20, 8)  # 增加边距确保文字显示

        # 左侧状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("font-size: 12px; font-weight: bold;")
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()
        
        self.task_count_label = QLabel()
        status_layout.addWidget(self.task_count_label)
    
    def connect_signals(self):
        """Connect signals and slots"""
        # Weekly table signals
        self.weekly_table.task_updated.connect(self.on_task_updated)
        self.weekly_table.task_deleted.connect(self.on_task_deleted)
        self.weekly_table.new_task_requested.connect(self.on_new_task_requested)
        self.weekly_table.week_changed.connect(self.on_week_changed)
    
    def load_data(self):
        """Load task data and categories"""
        try:
            # Load categories
            self.categories = self.task_service.get_categories()

            # Load all tasks
            all_tasks = self.task_service.get_tasks(limit=1000)

            # Filter out weekly plan tasks and load current week's weekly plan tasks separately
            regular_tasks = []
            for task in all_tasks:
                if task.tags and '周计划' in task.tags:
                    # Check if this weekly plan task belongs to current week
                    week_start = self.weekly_table.current_week_start
                    if week_start:
                        week_tag = f"周计划-{week_start.strftime('%Y-%m-%d')}"
                        if week_tag in task.tags:
                            regular_tasks.append(task)
                    # Skip weekly plan tasks that don't belong to current week
                else:
                    regular_tasks.append(task)

            self.current_tasks = regular_tasks

            # Update table
            self.weekly_table.update_tasks_data(self.current_tasks)

            # Update status
            self.update_status()

            self.logger.info("Task data loaded successfully")

        except Exception as e:
            self.logger.error(f"Error loading task data: {e}")
            QMessageBox.warning(self, "错误", f"加载数据失败: {e}")
    
    def update_status(self):
        """Update status bar information"""
        total_tasks = len(self.current_tasks)
        completed_tasks = len([t for t in self.current_tasks if t.status == TaskStatus.COMPLETED])
        
        self.task_count_label.setText(f"总任务: {total_tasks} | 已完成: {completed_tasks}")
        
        # Update week info
        week_info = self.weekly_table.get_current_week_info()
        if week_info['start_date'] and week_info['end_date']:
            start_str = week_info['start_date'].strftime("%m/%d")
            end_str = week_info['end_date'].strftime("%m/%d")
            week_num = week_info['week_number']
            self.week_info_label.setText(f"第 {week_num} 周 ({start_str} - {end_str})")

    def load_widget_toggle_state(self):
        """Load desktop widget toggle state"""
        try:
            # Load saved setting
            enabled = self.desktop_widget_controller.get_widget_enabled_setting()
            self.widget_toggle_checkbox.setChecked(enabled)

            # Update status display
            self.update_widget_status_display()

        except Exception as e:
            self.logger.error(f"加载桌面插件开关状态失败: {e}")

    def on_widget_toggle_changed(self, checked: bool):
        """Handle desktop widget toggle change"""
        try:
            self.widget_status_label.setText("状态: 处理中...")
            self.widget_toggle_checkbox.setEnabled(False)

            # Save setting
            self.desktop_widget_controller.set_widget_enabled_setting(checked)

            if checked:
                # Enable widget and auto-start
                success = self.desktop_widget_controller.start_widget()
                if success:
                    self.desktop_widget_controller.set_auto_start(True)
                    self.widget_status_label.setText("状态: 启动中...")
                else:
                    self.widget_toggle_checkbox.setChecked(False)
                    self.widget_status_label.setText("状态: 启动失败")
            else:
                # Disable widget and auto-start
                self.desktop_widget_controller.set_auto_start(False)
                success = self.desktop_widget_controller.stop_widget()
                if success:
                    self.widget_status_label.setText("状态: 已停止")
                else:
                    self.widget_status_label.setText("状态: 停止失败")

            self.widget_toggle_checkbox.setEnabled(True)

        except Exception as e:
            self.logger.error(f"处理桌面插件开关变化失败: {e}")
            self.widget_status_label.setText("状态: 操作失败")
            self.widget_toggle_checkbox.setEnabled(True)

    def on_widget_status_changed(self, is_running: bool):
        """Handle desktop widget status change"""
        self.update_widget_status_display()

    def on_widget_error(self, error_message: str):
        """Handle desktop widget error"""
        self.logger.error(f"桌面插件错误: {error_message}")
        QMessageBox.warning(self, "桌面插件错误", f"桌面插件操作失败:\n{error_message}")
        self.update_widget_status_display()

    def update_widget_status_display(self):
        """Update widget status display"""
        try:
            # 使用控制器的内部状态而不是 is_running() 方法，以确保状态同步的准确性
            is_running = self.desktop_widget_controller._is_running
            auto_start_enabled = self.desktop_widget_controller.is_auto_start_enabled()

            # 同步复选框状态 - 临时断开信号连接以避免触发事件
            self.widget_toggle_checkbox.toggled.disconnect()
            self.widget_toggle_checkbox.setChecked(is_running)
            self.widget_toggle_checkbox.toggled.connect(self.on_widget_toggle_changed)

            if is_running:
                status_text = "状态: 运行中"
                if auto_start_enabled:
                    status_text += " (自启动已启用)"
                self.widget_status_label.setStyleSheet("color: #28a745;")
            else:
                status_text = "状态: 已停止"
                if auto_start_enabled:
                    status_text += " (自启动已启用)"
                self.widget_status_label.setStyleSheet("color: #dc3545;")

            self.widget_status_label.setText(status_text)

        except Exception as e:
            self.logger.error(f"更新桌面插件状态显示失败: {e}")
            self.widget_status_label.setText("状态: 检查失败")
            self.widget_status_label.setStyleSheet("color: #ffc107;")
    
    def navigate_week(self, offset: int):
        """Navigate to different week"""
        if offset == 0:
            # Go to current week
            self.weekly_table.set_current_week(0)
        else:
            # Navigate relative to current displayed week
            self.weekly_table.navigate_week(offset)
        
        self.load_data()
    
    def on_week_changed(self, start_date: date, end_date: date):
        """Handle week change"""
        # Reload data to get the correct weekly plan for the new week
        self.load_data()
        self.update_status()
    
    def create_new_task(self):
        """Create a new task"""
        dialog = TaskEditDialog(categories=self.categories, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            task_data = dialog.get_task_data()
            
            if not task_data['title']:
                QMessageBox.warning(self, "错误", "任务标题不能为空")
                return
            
            try:
                # Create task with task date
                task = self.task_service.create_task(
                    title=task_data['title'],
                    description=task_data['description'],
                    category_id=None,  # Add missing category_id parameter
                    priority=task_data['priority'],
                    due_date=datetime.combine(task_data['due_date'], datetime.min.time()),
                    start_date=datetime.combine(task_data['date'], datetime.min.time()),
                    tags=task_data.get('tags', [])  # Use get() to handle missing tags
                )

                if task:
                    self.load_data()
                    self.status_label.setText("任务创建成功")
                    self.logger.info(f"Task created successfully: {task_data['title']}")
                else:
                    QMessageBox.warning(self, "错误", "任务创建失败")
                    self.logger.error("Task creation failed")
            except Exception as e:
                self.logger.error(f"Error creating task: {e}")
                QMessageBox.critical(self, "错误", f"创建任务时发生错误: {str(e)}")
    
    def on_new_task_requested(self, task_data: Dict):
        """Handle new task request from table"""
        # Special handling for weekly plan tasks
        if task_data.get('category') == '周计划':
            self.create_weekly_plan_task(task_data)
            return

        # Use simplified dialog for cell tasks (no date/time options)
        dialog = SimpleCellTaskDialog(task_data, self.categories, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            updated_data = dialog.get_task_data()

            if not updated_data['title']:
                QMessageBox.warning(self, "错误", "任务标题不能为空")
                return

            try:
                # Create task with task date and tags for proper categorization
                task = self.task_service.create_task(
                    title=updated_data['title'],
                    description=updated_data['description'],
                    category_id=None,  # Add missing category_id parameter
                    priority=updated_data['priority'],
                    due_date=datetime.combine(updated_data['due_date'], datetime.min.time()),
                    start_date=datetime.combine(updated_data['date'], datetime.min.time()),
                    tags=[updated_data['tags']] if updated_data.get('tags') else None
                )

                if task:
                    # Convert task to dict format for table display
                    status_map = {
                        'pending': '待处理',
                        'in_progress': '进行中',
                        'completed': '已完成',
                        'cancelled': '已取消'
                    }
                    priority_map = {
                        'low': '低',
                        'normal': '普通',
                        'high': '高',
                        'urgent': '紧急'
                    }

                    task_dict = {
                        'id': task.id,
                        'title': task.title,
                        'description': task.description,
                        'status': status_map.get(task.status.value if task.status else 'pending', '待处理'),
                        'priority': priority_map.get(task.priority.value if task.priority else 'normal', '普通'),
                        'due_date': task.due_date,
                        'start_date': task.start_date,
                        'date': updated_data['date'],
                        'category': updated_data['category']  # Use category from updated data
                    }

                    # Add task to specific cell instead of reloading all data
                    self.weekly_table.add_task_to_cell(
                        updated_data['date'],
                        updated_data['category'],  # Use category from updated data
                        task_dict
                    )

                    self.status_label.setText("任务创建成功")
                    self.logger.info(f"Task created successfully: {updated_data['title']}")
                else:
                    QMessageBox.warning(self, "错误", "任务创建失败")
                    self.logger.error("Task creation failed")
            except Exception as e:
                self.logger.error(f"Error creating task: {e}")
                QMessageBox.critical(self, "错误", f"创建任务时发生错误: {str(e)}")
    
    def on_task_updated(self, task_id: str, updated_data: Dict):
        """Handle task update"""
        # Get task info before update to determine which cell to refresh
        task = self.task_service.get_task(task_id)
        task_date = None
        task_category = None

        if task:
            # Determine task date and category
            if task.start_date:
                task_date = task.start_date.date()

            # Determine category from tags
            if task.tags:
                task_category = self.weekly_table.determine_task_category(task)

        success = self.task_service.update_task(task_id, **updated_data)
        if success:
            # Check if this is a weekly plan task
            is_weekly_plan_task = False
            if hasattr(self.weekly_table, 'weekly_plan_tasks_by_week'):
                # Check all weeks for this task
                for week_key, tasks in self.weekly_table.weekly_plan_tasks_by_week.items():
                    for task_data in tasks:
                        if task_data.get('id') == task_id:
                            # Update weekly plan task
                            self.weekly_table.update_weekly_plan_task(task_id, updated_data)
                            self.update_status()
                            self.status_label.setText("周计划任务更新成功")
                            is_weekly_plan_task = True
                            break
                    if is_weekly_plan_task:
                        break

            if not is_weekly_plan_task:
                # Find the task in current_tasks and update it
                for task_obj in self.current_tasks:
                    if task_obj.id == task_id:
                        # Update the task object with new data
                        for key, value in updated_data.items():
                            if hasattr(task_obj, key):
                                # Convert status string to enum if needed
                                if key == 'status' and isinstance(value, str):
                                    from models.task_models import TaskStatus
                                    status_map = {
                                        'PENDING': TaskStatus.PENDING,
                                        'IN_PROGRESS': TaskStatus.IN_PROGRESS,
                                        'COMPLETED': TaskStatus.COMPLETED,
                                        'CANCELLED': TaskStatus.CANCELLED,
                                        'PAUSED': TaskStatus.PAUSED
                                    }
                                    value = status_map.get(value, TaskStatus.PENDING)
                                setattr(task_obj, key, value)
                        break

                # Use precise cell refresh if we have the task info
                if task_date and task_category and task_category != "周计划":
                    self.weekly_table.refresh_cell(task_date, task_category)
                    self.status_label.setText("任务更新成功")
                else:
                    # Fallback to status-only update if we can't determine the cell
                    self.weekly_table.update_task_status_only(task_id, updated_data)
                    self.status_label.setText("任务更新成功")

            self.update_status()
        else:
            QMessageBox.warning(self, "错误", "任务更新失败")
    
    def on_task_deleted(self, task_id: str):
        """Handle task deletion"""
        # Get task info before deletion to determine which cell to refresh
        task = self.task_service.get_task(task_id)
        task_date = None
        task_category = None

        if task:
            # Determine task date and category
            if task.start_date:
                task_date = task.start_date.date()

            # Determine category from tags
            if task.tags:
                task_category = self.weekly_table.determine_task_category(task)

        success = self.task_service.delete_task(task_id)
        if success:
            # Check if this is a weekly plan task
            is_weekly_plan_task = False
            if hasattr(self.weekly_table, 'weekly_plan_tasks_by_week'):
                # Check all weeks for this task
                for week_key, tasks in self.weekly_table.weekly_plan_tasks_by_week.items():
                    for task_data in tasks:
                        if task_data.get('id') == task_id:
                            # Delete weekly plan task
                            self.weekly_table.delete_weekly_plan_task(task_id)
                            self.update_status()
                            self.status_label.setText("周计划任务删除成功")
                            is_weekly_plan_task = True
                            break
                    if is_weekly_plan_task:
                        break

            if not is_weekly_plan_task:
                # Use precise cell refresh if we have the task info
                if task_date and task_category and task_category != "周计划":
                    self.weekly_table.refresh_cell(task_date, task_category)
                    self.status_label.setText("任务删除成功")
                else:
                    # Fallback to full reload if we can't determine the cell
                    self.load_data()
                    self.status_label.setText("任务删除成功")

            # 恢复到删除前选择的单元格位置
            if hasattr(self.weekly_table, 'restore_previous_selection'):
                QTimer.singleShot(150, self.weekly_table.restore_previous_selection)
        else:
            QMessageBox.warning(self, "错误", "任务删除失败")
    
    def filter_tasks(self, filter_text: str):
        """Filter tasks based on selection"""
        # This could be enhanced to actually filter the displayed tasks
        self.load_data()

    def create_weekly_plan_task(self, task_data: Dict):
        """Create a new weekly plan task"""
        # Use simplified dialog for weekly plan tasks
        dialog = SimpleCellTaskDialog(task_data, self.categories, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            updated_data = dialog.get_task_data()

            if not updated_data['title']:
                QMessageBox.warning(self, "错误", "任务标题不能为空")
                return

            try:
                # Create task for weekly plan with week-specific tag
                week_start = self.weekly_table.current_week_start
                week_tag = f"周计划-{week_start.strftime('%Y-%m-%d')}" if week_start else "周计划"

                task = self.task_service.create_task(
                    title=updated_data['title'],
                    description=updated_data['description'],
                    category_id=None,
                    priority=updated_data['priority'],
                    due_date=datetime.combine(updated_data['due_date'], datetime.min.time()),
                    start_date=datetime.combine(updated_data['date'], datetime.min.time()),
                    tags=['周计划', week_tag]  # Add both general and week-specific tags
                )

                if task:
                    # Convert task to dict format for weekly plan display
                    status_map = {
                        'pending': '待处理',
                        'in_progress': '进行中',
                        'completed': '已完成',
                        'cancelled': '已取消'
                    }
                    priority_map = {
                        'low': '低',
                        'normal': '普通',
                        'high': '高',
                        'urgent': '紧急'
                    }

                    task_dict = {
                        'id': task.id,
                        'title': task.title,
                        'description': task.description or '',
                        'status': status_map.get(task.status, task.status),
                        'priority': priority_map.get(task.priority, task.priority),
                        'due_date': task.due_date.date() if task.due_date else None,
                        'start_date': task.start_date.date() if task.start_date else None,
                        'created_at': task.created_at,
                        'updated_at': task.updated_at,
                        'category': '周计划'  # 添加category字段
                    }

                    # Add to weekly plan
                    self.weekly_table.add_weekly_plan_task(task_dict)

                    self.logger.info(f"Weekly plan task created: {task.title}")
                    self.status_label.setText("周计划任务创建成功")
                else:
                    self.logger.error("Weekly plan task creation failed")
            except Exception as e:
                self.logger.error(f"Error creating weekly plan task: {e}")
                QMessageBox.critical(self, "错误", f"创建周计划任务时发生错误: {str(e)}")

    def search_tasks(self, search_text: str):
        """Search tasks"""
        # This could be enhanced to implement actual search functionality
        pass

    def apply_lake_blue_theme(self):
        """应用湖面蓝主题样式"""
        try:
            # 主界面背景
            self.setStyleSheet(LakeBlueTheme.get_main_widget_style())

            # 头部区域样式
            if hasattr(self, 'header_frame'):
                self.header_frame.setStyleSheet(LakeBlueTheme.get_header_style())

            # 工具栏样式
            if hasattr(self, 'toolbar_frame'):
                self.toolbar_frame.setStyleSheet(LakeBlueTheme.get_toolbar_style())

            # 主内容区域样式
            if hasattr(self, 'main_content'):
                self.main_content.setStyleSheet(LakeBlueTheme.get_content_style())

            # 状态栏样式
            if hasattr(self, 'status_bar'):
                self.status_bar.setStyleSheet(LakeBlueTheme.get_status_bar_style())

            # 桌面小组件切换按钮样式
            if hasattr(self, 'widget_toggle_btn'):
                self.widget_toggle_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {LakeBlueTheme.MINT_GREEN};
                        color: {LakeBlueTheme.DEEP_BLUE};
                        border: none;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-weight: bold;
                        font-size: 12px;
                    }}
                    QPushButton:hover {{
                        background-color: {LakeBlueTheme.SEAWEED_GREEN};
                        color: white;
                    }}
                    QPushButton:pressed {{
                        background-color: {LakeBlueTheme.MEDIUM_BLUE};
                    }}
                """)

            # 周导航按钮样式
            for btn_name in ['prev_week_btn', 'next_week_btn', 'today_btn']:
                if hasattr(self, btn_name):
                    btn = getattr(self, btn_name)
                    btn.setStyleSheet(f"""
                        QPushButton {{
                            background-color: {LakeBlueTheme.LIGHT_BLUE};
                            color: {LakeBlueTheme.DEEP_BLUE};
                            border: 1px solid {LakeBlueTheme.MEDIUM_BLUE};
                            border-radius: 6px;
                            padding: 6px 12px;
                            font-weight: bold;
                        }}
                        QPushButton:hover {{
                            background-color: {LakeBlueTheme.SEAWEED_GREEN};
                            color: white;
                        }}
                        QPushButton:pressed {{
                            background-color: {LakeBlueTheme.MEDIUM_BLUE};
                        }}
                    """)

            self.logger.info("Lake Blue theme applied successfully")

        except Exception as e:
            self.logger.error(f"Failed to apply Lake Blue theme: {e}")
