#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
桌面小部件数据同步调试脚本
用于检查数据库中的任务数据和分类逻辑
"""

import sys
import os
from datetime import date, datetime
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# 直接导入
import core.database as db
import core.config as config
from modules.task_manager.task_service import TaskService

def debug_task_data():
    """调试任务数据"""
    print("=== 桌面小部件数据同步调试 ===")
    
    try:
        # 初始化配置和数据库
        config_manager = config.Config()
        db_manager = db.init_db_manager(config_manager)
        
        # 创建任务服务
        task_service = TaskService()
        
        # 获取所有任务
        all_tasks = task_service.get_tasks(limit=1000)
        print(f"\n数据库中总任务数: {len(all_tasks)}")
        
        # 显示今天的日期
        today = date.today()
        print(f"今天日期: {today}")
        
        # 分析每个任务
        print("\n=== 任务详细信息 ===")
        today_tasks = []
        
        for i, task in enumerate(all_tasks):
            print(f"\n任务 {i+1}:")
            print(f"  ID: {task.id}")
            print(f"  标题: {task.title}")
            print(f"  描述: {task.description}")
            print(f"  标签: {task.tags}")
            print(f"  状态: {task.status}")
            print(f"  优先级: {task.priority}")
            print(f"  开始日期: {task.start_date}")
            print(f"  截止日期: {task.due_date}")
            print(f"  创建时间: {task.created_at}")
            
            # 检查是否是今天的任务
            task_date = None
            if task.start_date:
                task_date = task.start_date.date()
            elif task.due_date:
                task_date = task.due_date.date()
            
            is_today = task_date == today
            print(f"  是否今天任务: {is_today}")
            
            if is_today:
                today_tasks.append(task)
                
                # 测试分类逻辑
                category = determine_task_category(task)
                print(f"  分类结果: {category}")
        
        print(f"\n=== 今天的任务总数: {len(today_tasks)} ===")
        
        # 按分类统计
        categories = {
            '上午': [],
            '下午': [],
            '晚上': [],
            '全天': [],
            '其他': [],
            '日总结': []
        }
        
        for task in today_tasks:
            category = determine_task_category(task)
            categories[category].append(task)
        
        print("\n=== 按分类统计 ===")
        for category, tasks in categories.items():
            print(f"{category}: {len(tasks)} 个任务")
            for task in tasks:
                print(f"  - {task.title}")
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def determine_task_category(task) -> str:
    """确定任务分类 - 与桌面小部件中的逻辑保持一致"""
    if task.tags:
        tags = task.tags.lower()
        if '日总结' in tags or '总结' in tags:
            return '日总结'
        elif '上午' in tags:
            return '上午'
        elif '下午' in tags:
            return '下午'
        elif '晚上' in tags:
            return '晚上'
        elif '全天' in tags:
            return '全天'

    return '其他'

if __name__ == "__main__":
    debug_task_data()
