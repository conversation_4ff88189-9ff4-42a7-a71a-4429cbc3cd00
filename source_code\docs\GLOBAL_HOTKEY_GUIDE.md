# Personal Manager 全局快捷键功能指南

## 概述

Personal Manager 现已支持全局快捷键功能，允许您在任何应用程序中使用快捷键来控制学习时间追踪悬浮小组件的显示和隐藏。

## 功能特性

### 🔥 全局快捷键支持
- **Ctrl+Shift+S**: 显示/隐藏学习时间追踪悬浮小组件
- 在任何应用程序中都可以使用
- 支持 Windows 平台（使用 Windows API）
- 自动处理快捷键冲突和异常情况

### 🎯 智能悬浮小组件控制
- 首次按快捷键时自动创建悬浮小组件
- 再次按快捷键切换显示/隐藏状态
- 悬浮小组件保持完整的学习计时功能
- 支持拖拽移动和窗口折叠

### 📋 用户界面集成
- 菜单栏中的快捷键选项（视图 → 学习追踪悬浮窗）
- F1 快捷键说明对话框
- 状态栏消息提示
- 系统托盘通知（计划中）

## 使用方法

### 1. 启动应用程序
```bash
cd personal_manager_project/source_code
python main.py
```

### 2. 使用全局快捷键
1. **显示悬浮小组件**: 在任何应用程序中按 `Ctrl+Shift+S`
2. **隐藏悬浮小组件**: 再次按 `Ctrl+Shift+S`
3. **返回主界面**: 双击悬浮小组件或点击其中的主界面按钮

### 3. 查看快捷键说明
- 在主界面按 `F1` 键
- 或通过菜单：帮助 → 快捷键说明

## 技术实现

### 核心组件

#### 1. GlobalHotkeyManager (`src/core/global_hotkey.py`)
- 基于 Windows API 的全局快捷键注册
- 支持多个快捷键同时注册
- 线程安全的消息循环处理
- 自动资源清理和错误处理

#### 2. MainWindow 集成 (`src/gui/main_window.py`)
- 在主窗口初始化时注册快捷键
- 悬浮小组件的创建和管理
- 快捷键触发事件处理
- 应用程序退出时的资源清理

#### 3. 配置管理 (`src/core/config.py`)
- 快捷键组合的配置化管理
- 支持用户自定义快捷键（计划中）
- 快捷键启用/禁用开关

### 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MainWindow    │    │ GlobalHotkeyMgr  │    │ StudyFloating   │
│                 │    │                  │    │     Widget      │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │                 │
│ │setup_global_│ │───▶│ │register_     │ │    │ ┌─────────────┐ │
│ │hotkeys()    │ │    │ │hotkey()      │ │    │ │show/hide    │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ │toggle       │ │
│                 │    │                  │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │                 │
│ │toggle_study_│ │◀───│ │callback      │ │    │ ┌─────────────┐ │
│ │floating()   │ │    │ │execution     │ │    │ │timer        │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ │functions    │ │
└─────────────────┘    └──────────────────┘    │ └─────────────┘ │
                                               └─────────────────┘
```

## 配置选项

### 默认配置 (`config.yaml`)
```yaml
hotkeys:
  study_floating_widget: "Ctrl+Shift+S"
  enabled: true
```

### 自定义快捷键（计划中）
用户将能够通过设置界面自定义快捷键组合。

## 故障排除

### 常见问题

#### 1. 快捷键不响应
**可能原因**:
- 快捷键被其他程序占用
- Windows API 权限不足
- 应用程序未正确启动

**解决方案**:
- 检查其他程序是否使用相同快捷键
- 以管理员身份运行应用程序
- 查看应用程序日志文件

#### 2. 悬浮小组件不显示
**可能原因**:
- 悬浮小组件被其他窗口遮挡
- 显示器分辨率或多显示器配置问题

**解决方案**:
- 检查任务栏是否有悬浮小组件
- 尝试再次按快捷键切换状态
- 重启应用程序

#### 3. 应用程序崩溃
**可能原因**:
- 全局快捷键冲突
- 内存不足
- 系统兼容性问题

**解决方案**:
- 查看错误日志：`%APPDATA%\PersonalManager\logs\app.log`
- 重启应用程序
- 联系技术支持

### 调试工具

#### 测试全局快捷键功能
```bash
cd personal_manager_project/source_code
python test_global_hotkey.py
```

这个测试程序可以帮助您：
- 验证全局快捷键是否正常工作
- 测试不同的快捷键组合
- 调试快捷键冲突问题

## 日志和监控

### 日志位置
- Windows: `%APPDATA%\PersonalManager\logs\app.log`
- 包含全局快捷键注册、触发和错误信息

### 关键日志信息
```
INFO - Global hotkey manager initialized
INFO - Registered global hotkey: Ctrl+Shift+S - 显示/隐藏学习时间追踪悬浮小组件
INFO - Started global hotkey message loop
INFO - Global hotkey triggered: Ctrl+Shift+S
```

## 安全考虑

### 权限要求
- 全局快捷键需要系统级权限
- 某些安全软件可能阻止全局快捷键注册
- 建议将应用程序添加到安全软件白名单

### 隐私保护
- 全局快捷键仅监听注册的组合键
- 不记录其他键盘输入
- 所有数据保存在本地

## 未来计划

### 即将推出的功能
1. **自定义快捷键设置界面**
   - 图形化快捷键配置
   - 快捷键冲突检测
   - 快捷键组合验证

2. **更多全局快捷键**
   - 快速添加任务
   - 系统监控快捷访问
   - 文件管理器快捷打开

3. **系统托盘集成**
   - 托盘图标右键菜单
   - 快捷键状态指示
   - 托盘通知消息

4. **跨平台支持**
   - macOS 全局快捷键支持
   - Linux 全局快捷键支持
   - 统一的快捷键 API

## 技术支持

如果您遇到问题或有功能建议，请：

1. 查看日志文件获取详细错误信息
2. 运行测试程序验证功能
3. 检查系统兼容性和权限设置
4. 提供详细的错误描述和系统信息

---

**注意**: 全局快捷键功能目前仅支持 Windows 平台。其他平台的支持正在开发中。
