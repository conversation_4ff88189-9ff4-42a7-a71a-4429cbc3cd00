"""
DEPRECATED: Old Task Manager Widget for Personal Manager System

This widget has been COMPLETELY REPLACED by WeeklyTaskManagerWidget.
This file is kept for reference only and should not be used in new code.

Use WeeklyTaskManagerWidget instead for the new weekly task management interface.
"""

from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QTableWidget, QTableWidgetItem, QHeaderView,
    QPushButton, QLineEdit, QComboBox, QLabel,
    QTextEdit, QDateTimeEdit, QCheckBox, QTabWidget,
    QGroupBox, QListWidget, QListWidgetItem,
    QMessageBox, QInputDialog, QDialog, QDialogButtonBox,
    QFormLayout, QSplitter, QFrame
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QDateTime
from PyQt6.QtGui import QFont, QColor

from core.logger import LoggerMixin
from .task_service import TaskService


class TaskDialog(QDialog):
    """Dialog for creating/editing tasks"""
    
    def __init__(self, task=None, categories=None, parent=None):
        super().__init__(parent)
        self.task = task
        self.categories = categories or []
        self.setWindowTitle("Edit Task" if task else "New Task")
        self.setModal(True)
        self.resize(500, 400)
        self.init_ui()
        
        if task:
            self.load_task_data()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Title
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("Enter task title...")
        form_layout.addRow("Title:", self.title_edit)
        
        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("Enter task description...")
        form_layout.addRow("Description:", self.description_edit)
        

        
        # Priority
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["low", "medium", "high", "urgent"])
        self.priority_combo.setCurrentText("medium")
        form_layout.addRow("Priority:", self.priority_combo)
        
        # Due date
        self.due_date_edit = QDateTimeEdit()
        self.due_date_edit.setDateTime(QDateTime.currentDateTime().addDays(1))
        self.due_date_edit.setCalendarPopup(True)
        form_layout.addRow("Due Date:", self.due_date_edit)
        
        # Reminder
        self.reminder_checkbox = QCheckBox("Set Reminder")
        form_layout.addRow("", self.reminder_checkbox)
        
        self.reminder_edit = QDateTimeEdit()
        self.reminder_edit.setDateTime(QDateTime.currentDateTime().addSecs(3600))  # Add 1 hour
        self.reminder_edit.setCalendarPopup(True)
        self.reminder_edit.setEnabled(False)
        form_layout.addRow("Reminder Time:", self.reminder_edit)
        
        # Connect reminder checkbox
        self.reminder_checkbox.toggled.connect(self.reminder_edit.setEnabled)
        

        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def load_task_data(self):
        """Load task data into form"""
        if not self.task:
            return
        
        self.title_edit.setText(self.task.title or "")
        self.description_edit.setPlainText(self.task.description or "")
        self.priority_combo.setCurrentText(self.task.priority or "medium")
        
        if self.task.due_date:
            self.due_date_edit.setDateTime(QDateTime.fromSecsSinceEpoch(int(self.task.due_date.timestamp())))
        

    
    def get_task_data(self):
        """Get task data from form"""
        return {
            'title': self.title_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'priority': self.priority_combo.currentText(),
            'due_date': self.due_date_edit.dateTime().toPython(),
            'reminder_time': self.reminder_edit.dateTime().toPython() if self.reminder_checkbox.isChecked() else None,
            'tags': []  # Add tags field (empty for now, can be extended later)
        }


class TaskTableWidget(QTableWidget):
    """Custom table widget for tasks"""
    
    task_double_clicked = pyqtSignal(str)  # task_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()
    
    def setup_table(self):
        """Setup table properties"""
        self.setColumnCount(6)
        self.setHorizontalHeaderLabels([
            'Title', 'Category', 'Priority', 'Status', 'Due Date', 'Created'
        ])
        
        # Configure table
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # Connect double click
        self.itemDoubleClicked.connect(self.on_item_double_clicked)
    
    def on_item_double_clicked(self, item):
        """Handle item double click"""
        row = item.row()
        task_id_item = self.item(row, 0)
        if task_id_item and hasattr(task_id_item, 'task_id'):
            self.task_double_clicked.emit(task_id_item.task_id)
    
    def update_tasks(self, tasks):
        """Update table with tasks"""
        self.setRowCount(len(tasks))
        
        for row, task in enumerate(tasks):
            # Title
            title_item = QTableWidgetItem(task.title or "")
            title_item.task_id = task.id
            self.setItem(row, 0, title_item)
            
            # Category
            category_name = task.category.name if task.category else "No Category"
            self.setItem(row, 1, QTableWidgetItem(category_name))
            
            # Priority
            priority_item = QTableWidgetItem(task.priority or "medium")
            # Color code priority
            if task.priority == 'urgent':
                priority_item.setBackground(QColor(255, 200, 200))
            elif task.priority == 'high':
                priority_item.setBackground(QColor(255, 230, 200))
            elif task.priority == 'medium':
                priority_item.setBackground(QColor(255, 255, 200))
            else:
                priority_item.setBackground(QColor(200, 255, 200))
            self.setItem(row, 2, priority_item)
            
            # Status
            status_item = QTableWidgetItem(task.status or "pending")
            if task.status == 'completed':
                status_item.setBackground(QColor(200, 255, 200))
            elif task.status == 'in_progress':
                status_item.setBackground(QColor(200, 230, 255))
            self.setItem(row, 3, status_item)
            
            # Due date
            due_date = task.due_date.strftime('%Y-%m-%d %H:%M') if task.due_date else ""
            due_item = QTableWidgetItem(due_date)
            # Highlight overdue tasks
            if task.due_date and task.due_date < datetime.utcnow() and task.status != 'completed':
                due_item.setBackground(QColor(255, 180, 180))
            self.setItem(row, 4, due_item)
            
            # Created
            created = task.created_at.strftime('%Y-%m-%d') if task.created_at else ""
            self.setItem(row, 5, QTableWidgetItem(created))


class TaskManagerWidget(QWidget, LoggerMixin):
    """Main task manager widget"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.task_service = TaskService()
        self.current_tasks = []
        self.categories = []
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Create toolbar
        self.create_toolbar()
        layout.addWidget(self.toolbar_frame)
        
        # Create main content
        self.create_main_content()
        layout.addWidget(self.main_splitter)
    
    def create_toolbar(self):
        """Create the toolbar"""
        self.toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(self.toolbar_frame)
        
        # New task button
        self.new_task_button = QPushButton("New Task")
        self.new_task_button.clicked.connect(self.create_new_task)
        toolbar_layout.addWidget(self.new_task_button)
        
        # Edit task button
        self.edit_task_button = QPushButton("Edit Task")
        self.edit_task_button.clicked.connect(self.edit_selected_task)
        self.edit_task_button.setEnabled(False)
        toolbar_layout.addWidget(self.edit_task_button)
        
        # Complete task button
        self.complete_task_button = QPushButton("Complete Task")
        self.complete_task_button.clicked.connect(self.complete_selected_task)
        self.complete_task_button.setEnabled(False)
        toolbar_layout.addWidget(self.complete_task_button)
        
        # Delete task button
        self.delete_task_button = QPushButton("Delete Task")
        self.delete_task_button.clicked.connect(self.delete_selected_task)
        self.delete_task_button.setEnabled(False)
        toolbar_layout.addWidget(self.delete_task_button)
        
        toolbar_layout.addStretch()
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search tasks...")
        self.search_edit.textChanged.connect(self.search_tasks)
        toolbar_layout.addWidget(QLabel("Search:"))
        toolbar_layout.addWidget(self.search_edit)
        
        # Filter by status
        self.status_filter = QComboBox()
        self.status_filter.addItems(["All", "pending", "in_progress", "completed"])
        self.status_filter.currentTextChanged.connect(self.filter_tasks)
        toolbar_layout.addWidget(QLabel("Status:"))
        toolbar_layout.addWidget(self.status_filter)
        
        # Refresh button
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.load_data)
        toolbar_layout.addWidget(self.refresh_button)
    
    def create_main_content(self):
        """Create the main content area"""
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Create sidebar
        self.create_sidebar()
        self.main_splitter.addWidget(self.sidebar_widget)
        
        # Create task table
        self.task_table = TaskTableWidget()
        self.task_table.task_double_clicked.connect(self.edit_task_by_id)
        self.task_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.main_splitter.addWidget(self.task_table)
        
        # Set splitter proportions
        self.main_splitter.setSizes([200, 600])
    
    def create_sidebar(self):
        """Create the sidebar"""
        self.sidebar_widget = QWidget()
        sidebar_layout = QVBoxLayout(self.sidebar_widget)
        
        # Quick filters
        quick_filters_group = QGroupBox("Quick Filters")
        quick_filters_layout = QVBoxLayout(quick_filters_group)
        
        self.overdue_button = QPushButton("Overdue Tasks")
        self.overdue_button.clicked.connect(self.show_overdue_tasks)
        quick_filters_layout.addWidget(self.overdue_button)
        
        self.upcoming_button = QPushButton("Upcoming Tasks")
        self.upcoming_button.clicked.connect(self.show_upcoming_tasks)
        quick_filters_layout.addWidget(self.upcoming_button)
        
        self.today_button = QPushButton("Due Today")
        self.today_button.clicked.connect(self.show_today_tasks)
        quick_filters_layout.addWidget(self.today_button)
        
        sidebar_layout.addWidget(quick_filters_group)
        
        # Categories
        categories_group = QGroupBox("Categories")
        categories_layout = QVBoxLayout(categories_group)
        
        self.categories_list = QListWidget()
        self.categories_list.itemClicked.connect(self.filter_by_category)
        categories_layout.addWidget(self.categories_list)
        
        # Category management buttons
        cat_buttons_layout = QHBoxLayout()
        self.new_category_button = QPushButton("New")
        self.new_category_button.clicked.connect(self.create_new_category)
        cat_buttons_layout.addWidget(self.new_category_button)
        
        self.delete_category_button = QPushButton("Delete")
        self.delete_category_button.clicked.connect(self.delete_selected_category)
        cat_buttons_layout.addWidget(self.delete_category_button)
        
        categories_layout.addLayout(cat_buttons_layout)
        sidebar_layout.addWidget(categories_group)
        
        sidebar_layout.addStretch()
    
    def load_data(self):
        """Load tasks and categories"""
        try:
            # Load categories
            self.categories = self.task_service.get_categories()
            self.update_categories_list()
            
            # Load tasks
            self.current_tasks = self.task_service.get_tasks()
            self.task_table.update_tasks(self.current_tasks)
            
        except Exception as e:
            self.logger.error(f"Error loading data: {e}")
            QMessageBox.warning(self, "Error", f"Failed to load data: {e}")
    
    def update_categories_list(self):
        """Update the categories list"""
        self.categories_list.clear()
        
        # Add "All Categories" item
        all_item = QListWidgetItem("All Categories")
        all_item.setData(Qt.ItemDataRole.UserRole, None)
        self.categories_list.addItem(all_item)
        
        # Add categories
        for category in self.categories:
            item = QListWidgetItem(category.name)
            item.setData(Qt.ItemDataRole.UserRole, category.id)
            self.categories_list.addItem(item)

    def on_selection_changed(self):
        """Handle task selection change"""
        has_selection = len(self.task_table.selectedItems()) > 0
        self.edit_task_button.setEnabled(has_selection)
        self.complete_task_button.setEnabled(has_selection)
        self.delete_task_button.setEnabled(has_selection)

    def create_new_task(self):
        """Create a new task"""
        dialog = TaskDialog(categories=self.categories, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            task_data = dialog.get_task_data()

            if not task_data['title']:
                QMessageBox.warning(self, "Error", "Task title is required")
                return

            task = self.task_service.create_task(
                title=task_data['title'],
                description=task_data['description'],
                category_id=None,  # Add missing category_id parameter
                priority=task_data['priority'],
                due_date=task_data['due_date'],
                reminder_time=task_data['reminder_time'],
                tags=task_data.get('tags', [])  # Add tags parameter
            )

            if task:
                self.load_data()
                QMessageBox.information(self, "Success", "Task created successfully")
            else:
                QMessageBox.warning(self, "Error", "Failed to create task")

    def edit_selected_task(self):
        """Edit the selected task"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            return

        row = selected_items[0].row()
        title_item = self.task_table.item(row, 0)
        if hasattr(title_item, 'task_id'):
            self.edit_task_by_id(title_item.task_id)

    def edit_task_by_id(self, task_id: str):
        """Edit a task by ID"""
        task = self.task_service.get_task(task_id)
        if not task:
            QMessageBox.warning(self, "Error", "Task not found")
            return

        dialog = TaskDialog(task=task, categories=self.categories, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            task_data = dialog.get_task_data()

            if not task_data['title']:
                QMessageBox.warning(self, "Error", "Task title is required")
                return

            success = self.task_service.update_task(task_id, **task_data)

            if success:
                self.load_data()
                QMessageBox.information(self, "Success", "Task updated successfully")
            else:
                QMessageBox.warning(self, "Error", "Failed to update task")

    def complete_selected_task(self):
        """Complete the selected task"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            return

        row = selected_items[0].row()
        title_item = self.task_table.item(row, 0)
        if hasattr(title_item, 'task_id'):
            success = self.task_service.complete_task(title_item.task_id)

            if success:
                self.load_data()
                QMessageBox.information(self, "Success", "Task completed successfully")
            else:
                QMessageBox.warning(self, "Error", "Failed to complete task")

    def delete_selected_task(self):
        """Delete the selected task"""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            return

        row = selected_items[0].row()
        title_item = self.task_table.item(row, 0)
        task_title = title_item.text()

        reply = QMessageBox.question(
            self, 'Delete Task',
            f'Are you sure you want to delete "{task_title}"?',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes and hasattr(title_item, 'task_id'):
            success = self.task_service.delete_task(title_item.task_id)

            if success:
                self.load_data()
                QMessageBox.information(self, "Success", "Task deleted successfully")
            else:
                QMessageBox.warning(self, "Error", "Failed to delete task")

    def search_tasks(self, query: str):
        """Search tasks"""
        if not query.strip():
            self.current_tasks = self.task_service.get_tasks()
        else:
            self.current_tasks = self.task_service.search_tasks(query)

        self.task_table.update_tasks(self.current_tasks)

    def filter_tasks(self, status: str):
        """Filter tasks by status"""
        if status == "All":
            self.current_tasks = self.task_service.get_tasks()
        else:
            self.current_tasks = self.task_service.get_tasks(status=status)

        self.task_table.update_tasks(self.current_tasks)

    def filter_by_category(self, item):
        """Filter tasks by category"""
        category_id = item.data(Qt.ItemDataRole.UserRole)

        if category_id is None:
            self.current_tasks = self.task_service.get_tasks()
        else:
            self.current_tasks = self.task_service.get_tasks(category_id=category_id)

        self.task_table.update_tasks(self.current_tasks)

    def show_overdue_tasks(self):
        """Show overdue tasks"""
        self.current_tasks = self.task_service.get_overdue_tasks()
        self.task_table.update_tasks(self.current_tasks)

    def show_upcoming_tasks(self):
        """Show upcoming tasks"""
        self.current_tasks = self.task_service.get_upcoming_tasks()
        self.task_table.update_tasks(self.current_tasks)

    def show_today_tasks(self):
        """Show tasks due today"""
        today = datetime.now().date()
        today_tasks = [
            task for task in self.task_service.get_tasks()
            if task.due_date and task.due_date.date() == today
        ]
        self.current_tasks = today_tasks
        self.task_table.update_tasks(self.current_tasks)

    def create_new_category(self):
        """Create a new category"""
        name, ok = QInputDialog.getText(self, 'New Category', 'Category name:')
        if ok and name.strip():
            category = self.task_service.create_category(name.strip())
            if category:
                self.load_data()
                QMessageBox.information(self, "Success", "Category created successfully")
            else:
                QMessageBox.warning(self, "Error", "Failed to create category")

    def delete_selected_category(self):
        """Delete the selected category"""
        current_item = self.categories_list.currentItem()
        if not current_item:
            return

        category_id = current_item.data(Qt.ItemDataRole.UserRole)
        if category_id is None:
            return  # Can't delete "All Categories"

        category_name = current_item.text()
        reply = QMessageBox.question(
            self, 'Delete Category',
            f'Are you sure you want to delete category "{category_name}"?',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            success = self.task_service.delete_category(category_id)

            if success:
                self.load_data()
                QMessageBox.information(self, "Success", "Category deleted successfully")
            else:
                QMessageBox.warning(self, "Error", "Failed to delete category")
