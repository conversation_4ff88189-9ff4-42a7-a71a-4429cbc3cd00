"""
Widget Process Manager - Manages widget processes and lifecycle
"""

import os
import sys
import psutil
import subprocess
from pathlib import Path
from typing import Optional, List
from PyQt6.QtCore import QObject, pyqtSignal

from core.logger import LoggerMixin


class WidgetProcessManager(QObject, LoggerMixin):
    """小组件进程管理器"""
    
    process_started = pyqtSignal(int)  # 进程启动信号 (PID)
    process_stopped = pyqtSignal(int)  # 进程停止信号 (PID)
    
    def __init__(self, widget_type: str, parent=None):
        super().__init__(parent)
        self.widget_type = widget_type
        self.project_root = self._get_project_root()
        
        # 进程相关文件
        self.pid_file = self.project_root / f"{widget_type}_daemon.pid"
        self.daemon_script = self.project_root / f"{widget_type}_daemon.py"
        
        # 当前进程引用
        self.daemon_process: Optional[subprocess.Popen] = None
    
    def _get_project_root(self) -> Path:
        """获取项目根目录"""
        current_file = Path(__file__)
        return current_file.parent.parent.parent.parent
    
    def is_running(self) -> bool:
        """检查进程是否正在运行"""
        try:
            if not self.pid_file.exists():
                return False
            
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            return psutil.pid_exists(pid)
        
        except Exception as e:
            self.logger.debug(f"Error checking if process is running: {e}")
            return False
    
    def get_running_pid(self) -> Optional[int]:
        """获取正在运行的进程PID"""
        try:
            if not self.pid_file.exists():
                return None
            
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            if psutil.pid_exists(pid):
                return pid
            else:
                # PID文件存在但进程不存在，清理文件
                self.pid_file.unlink()
                return None
        
        except Exception as e:
            self.logger.debug(f"Error getting running PID: {e}")
            return None
    
    def start_daemon(self, manual_mode: bool = True) -> bool:
        """启动守护进程"""
        try:
            if self.is_running():
                self.logger.info(f"{self.widget_type} daemon already running")
                return True
            
            if not self.daemon_script.exists():
                self.logger.error(f"Daemon script not found: {self.daemon_script}")
                return False
            
            # 准备启动参数
            cmd = [sys.executable, str(self.daemon_script)]
            if manual_mode:
                cmd.append("--manual")
            
            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSFSENCODING'] = '0'
            if os.name == 'nt':
                env['PYTHONUTF8'] = '1'
            
            # 启动进程
            self.daemon_process = subprocess.Popen(
                cmd,
                cwd=str(self.project_root),
                env=env,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            
            self.logger.info(f"{self.widget_type} daemon started, PID: {self.daemon_process.pid}")
            self.process_started.emit(self.daemon_process.pid)
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to start {self.widget_type} daemon: {e}")
            return False
    
    def stop_daemon(self) -> bool:
        """停止守护进程"""
        try:
            pid = self.get_running_pid()
            if not pid:
                self.logger.info(f"{self.widget_type} daemon not running")
                return True
            
            # 终止进程
            process = psutil.Process(pid)
            process.terminate()
            
            # 等待进程结束
            try:
                process.wait(timeout=5)
            except psutil.TimeoutExpired:
                self.logger.warning(f"Process {pid} did not terminate, killing...")
                process.kill()
            
            # 清理PID文件
            if self.pid_file.exists():
                self.pid_file.unlink()
            
            self.logger.info(f"{self.widget_type} daemon stopped")
            self.process_stopped.emit(pid)
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to stop {self.widget_type} daemon: {e}")
            return False
    
    def restart_daemon(self) -> bool:
        """重启守护进程"""
        self.stop_daemon()
        return self.start_daemon()
    
    def force_kill_all_related_processes(self):
        """强制终止所有相关进程"""
        try:
            killed_count = 0
            
            # 查找所有相关进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info.get('cmdline', [])
                    if not cmdline:
                        continue
                    
                    # 检查是否是相关的Python进程
                    cmdline_str = ' '.join(cmdline)
                    if (self.widget_type in cmdline_str and 
                        ('python' in cmdline_str.lower() or 'python.exe' in cmdline_str.lower())):
                        
                        proc.kill()
                        killed_count += 1
                        self.logger.info(f"Killed process {proc.info['pid']}: {cmdline_str}")
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if killed_count > 0:
                self.logger.info(f"Force killed {killed_count} related processes")
            
            # 清理PID文件
            if self.pid_file.exists():
                self.pid_file.unlink()
        
        except Exception as e:
            self.logger.error(f"Error force killing processes: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.daemon_process and self.daemon_process.poll() is None:
                self.daemon_process.terminate()
            
            if self.pid_file.exists():
                self.pid_file.unlink()
        
        except Exception as e:
            self.logger.warning(f"Error during cleanup: {e}")