@echo off
REM Personal Manager Shortcut Installer
REM This script creates desktop and start menu shortcuts for Personal Manager

echo ========================================
echo Personal Manager Shortcut Installer
echo ========================================
echo.

REM Set the script directory as the working directory
cd /d "%~dp0"

echo Current directory: %CD%
echo.

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo Error: PowerShell is not available
    echo This installer requires PowerShell to create shortcuts
    pause
    exit /b 1
)

echo Creating application icon...
python create_icon.py
if errorlevel 1 (
    echo Warning: Failed to create application icon
    echo The shortcut will be created without a custom icon
)

echo.
echo Creating desktop shortcut...
echo This may require administrator privileges or user confirmation.
echo.

REM Run PowerShell script to create shortcuts
powershell -ExecutionPolicy Bypass -File "create_desktop_shortcut.ps1"

echo.
echo Installation complete!
echo.
echo You can now start Personal Manager by:
echo 1. Double-clicking the desktop shortcut
echo 2. Running start_personal_manager.bat
echo 3. Using the Start Menu shortcut (if created)
echo.
pause
