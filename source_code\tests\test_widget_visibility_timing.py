#!/usr/bin/env python3
"""
测试桌面小部件可见性时间
测量从启动命令到用户可以看到并操作小部件的时间
"""

import sys
import os
import time
import subprocess
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from src.modules.task_manager.desktop_widget_controller import DesktopWidgetController

def test_widget_visibility_timing():
    """测试桌面小部件可见性时间"""
    print("=" * 60)
    print("桌面小部件可见性时间测试")
    print("=" * 60)
    
    controller = DesktopWidgetController()
    
    # 确保先停止任何运行中的实例
    if controller.is_running():
        print("停止现有实例...")
        stop_start = time.time()
        controller.stop_widget()
        stop_end = time.time()
        print(f"停止时间: {stop_end - stop_start:.3f}秒")
        time.sleep(1)  # 等待完全停止

    print("\n开始测试启动到可见的时间...")

    # 记录启动开始时间
    start_time = time.time()
    print(f"启动命令发送时间: {start_time}")

    # 启动桌面小部件
    success = controller.start_widget()
    
    if success:
        # 记录启动完成时间
        startup_complete_time = time.time()
        startup_duration = startup_complete_time - start_time
        
        print(f"启动完成时间: {startup_complete_time}")
        print(f"启动耗时: {startup_duration:.3f}秒")
        
        # 等待一段时间让用户观察
        print("\n请观察桌面右上角是否出现了任务小部件")
        print("如果看到了小部件，请在小部件上点击任何任务卡片来测试响应时间")
        print("按 Enter 键继续测试停止功能...")
        input()
        
        # 测试停止时间
        print("\n测试停止时间...")
        stop_start = time.time()
        controller.stop_widget()
        stop_end = time.time()
        stop_duration = stop_end - stop_start
        
        print(f"停止耗时: {stop_duration:.3f}秒")
        
        # 总结
        print("\n" + "=" * 60)
        print("测试结果总结:")
        print(f"启动时间: {startup_duration:.3f}秒")
        print(f"停止时间: {stop_duration:.3f}秒")
        print("=" * 60)
        
    else:
        print("启动失败！")
        return False
    
    return True

def test_direct_widget_startup():
    """直接测试桌面小部件启动"""
    print("\n" + "=" * 60)
    print("直接启动桌面小部件测试")
    print("=" * 60)
    
    script_path = project_root / "start_desktop_widget.py"
    
    print("直接运行桌面小部件脚本...")
    start_time = time.time()
    
    # 直接运行脚本并捕获输出
    try:
        process = subprocess.Popen(
            [sys.executable, str(script_path)],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            cwd=str(project_root)
        )
        
        print("脚本已启动，等待输出...")
        
        # 读取前几行输出来判断启动状态
        output_lines = []
        for i in range(20):  # 读取前20行输出
            try:
                line = process.stdout.readline()
                if line:
                    output_lines.append(line.strip())
                    print(f"输出: {line.strip()}")
                    
                    # 检查是否包含完成初始化的标志
                    if "桌面小部件完全初始化完成" in line:
                        end_time = time.time()
                        total_time = end_time - start_time
                        print(f"\n✅ 桌面小部件完全可见！总耗时: {total_time:.3f}秒")
                        break
                else:
                    time.sleep(0.1)
            except:
                break
        
        # 终止进程
        process.terminate()
        process.wait()
        
    except Exception as e:
        print(f"直接启动测试失败: {e}")

if __name__ == "__main__":
    print("桌面小部件可见性时间测试工具")
    print("此工具将测量从启动命令到用户可以看到并操作小部件的实际时间")
    
    # 测试1: 通过控制器启动
    test_widget_visibility_timing()
    
    # 测试2: 直接启动脚本
    test_direct_widget_startup()
    
    print("\n测试完成！")
