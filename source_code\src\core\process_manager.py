"""
Application Process Manager - 造神计划
管理应用程序进程，确保单一实例运行和优雅关闭
"""

import os
import sys
import psutil
import signal
import atexit
from pathlib import Path
from typing import List, Optional
from PyQt6.QtCore import QObject, pyqtSignal, QTimer
from PyQt6.QtWidgets import QApplication

from .logger import LoggerMixin


class ProcessManager(QObject, LoggerMixin):
    """进程管理器 - 造神计划"""
    
    # 信号
    shutdown_requested = pyqtSignal()
    all_processes_stopped = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.app_name = "造神计划"
        self.main_process_pid = os.getpid()
        self.child_processes: List[int] = []
        self.is_shutting_down = False
        
        # 锁文件路径
        self.lock_file = Path.home() / f".{self.app_name.lower()}_lock"
        
        # 注册退出处理
        atexit.register(self.cleanup_on_exit)
        
        # 设置信号处理器
        self.setup_signal_handlers()
        
        self.logger.info("Process manager initialized")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        if os.name != 'nt':  # Unix系统
            signal.signal(signal.SIGTERM, self.signal_handler)
            signal.signal(signal.SIGINT, self.signal_handler)
        else:  # Windows系统
            signal.signal(signal.SIGTERM, self.signal_handler)
            signal.signal(signal.SIGINT, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"Received signal {signum}, initiating shutdown...")
        self.shutdown_application()
    
    def check_single_instance(self) -> bool:
        """检查是否已有实例在运行"""
        try:
            if self.lock_file.exists():
                # 读取PID
                with open(self.lock_file, 'r') as f:
                    existing_pid = int(f.read().strip())
                
                # 检查进程是否存在
                if psutil.pid_exists(existing_pid):
                    try:
                        existing_process = psutil.Process(existing_pid)
                        # 检查是否是同一个应用
                        if self.app_name.lower() in existing_process.name().lower():
                            self.logger.warning(f"Application already running with PID: {existing_pid}")
                            return False
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                
                # 如果进程不存在，删除锁文件
                self.lock_file.unlink()
            
            # 创建新的锁文件
            with open(self.lock_file, 'w') as f:
                f.write(str(self.main_process_pid))
            
            self.logger.info(f"Single instance check passed, PID: {self.main_process_pid}")
            return True
        
        except Exception as e:
            self.logger.error(f"Error checking single instance: {e}")
            return True  # 出错时允许启动
    
    def register_child_process(self, pid: int):
        """注册子进程"""
        if pid not in self.child_processes:
            self.child_processes.append(pid)
            self.logger.info(f"Registered child process: {pid}")
    
    def unregister_child_process(self, pid: int):
        """注销子进程"""
        if pid in self.child_processes:
            self.child_processes.remove(pid)
            self.logger.info(f"Unregistered child process: {pid}")
    
    def get_all_related_processes(self) -> List[psutil.Process]:
        """获取所有相关进程"""
        related_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    cmdline = proc_info.get('cmdline', [])
                    
                    if not cmdline:
                        continue
                    
                    cmdline_str = ' '.join(cmdline).lower()
                    
                    # 检查是否是相关进程
                    if (self.app_name.lower() in cmdline_str or
                        'personal_manager' in cmdline_str or
                        'universal_widget_daemon' in cmdline_str or
                        any('造神计划' in arg for arg in cmdline)):
                        
                        related_processes.append(proc)
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        
        except Exception as e:
            self.logger.error(f"Error getting related processes: {e}")
        
        return related_processes
    
    def terminate_child_processes(self):
        """终止所有子进程"""
        self.logger.info("Terminating child processes...")
        
        # 终止注册的子进程
        for pid in self.child_processes.copy():
            try:
                if psutil.pid_exists(pid):
                    process = psutil.Process(pid)
                    process.terminate()
                    
                    # 等待进程结束
                    try:
                        process.wait(timeout=3)
                        self.logger.info(f"Child process {pid} terminated gracefully")
                    except psutil.TimeoutExpired:
                        process.kill()
                        self.logger.warning(f"Child process {pid} killed forcefully")
                
                self.unregister_child_process(pid)
            
            except Exception as e:
                self.logger.error(f"Error terminating child process {pid}: {e}")
        
        # 查找并终止所有相关进程
        related_processes = self.get_all_related_processes()
        for proc in related_processes:
            try:
                if proc.pid != self.main_process_pid:  # 不终止主进程
                    proc.terminate()
                    
                    try:
                        proc.wait(timeout=3)
                        self.logger.info(f"Related process {proc.pid} terminated")
                    except psutil.TimeoutExpired:
                        proc.kill()
                        self.logger.warning(f"Related process {proc.pid} killed")
            
            except Exception as e:
                self.logger.error(f"Error terminating related process {proc.pid}: {e}")
    
    def shutdown_application(self):
        """关闭应用程序"""
        if self.is_shutting_down:
            return
        
        self.is_shutting_down = True
        self.logger.info("Initiating application shutdown...")
        
        # 发送关闭信号
        self.shutdown_requested.emit()
        
        # 终止子进程
        self.terminate_child_processes()
        
        # 清理资源
        self.cleanup_on_exit()
        
        # 发送完成信号
        self.all_processes_stopped.emit()
        
        # 退出应用
        QApplication.quit()
    
    def cleanup_on_exit(self):
        """退出时清理"""
        try:
            # 删除锁文件
            if self.lock_file.exists():
                self.lock_file.unlink()
                self.logger.info("Lock file removed")
            
            # 最后一次尝试清理进程
            if not self.is_shutting_down:
                self.terminate_child_processes()
        
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def get_process_info(self) -> dict:
        """获取进程信息"""
        try:
            main_process = psutil.Process(self.main_process_pid)
            related_processes = self.get_all_related_processes()
            
            return {
                'main_process': {
                    'pid': self.main_process_pid,
                    'name': main_process.name(),
                    'memory_mb': round(main_process.memory_info().rss / 1024 / 1024, 2),
                    'cpu_percent': main_process.cpu_percent(),
                    'status': main_process.status()
                },
                'child_processes': len(self.child_processes),
                'related_processes': len(related_processes),
                'total_memory_mb': sum(
                    proc.memory_info().rss / 1024 / 1024 
                    for proc in related_processes
                ),
                'is_shutting_down': self.is_shutting_down
            }
        
        except Exception as e:
            self.logger.error(f"Error getting process info: {e}")
            return {'error': str(e)}


# 全局进程管理器实例
_process_manager: Optional[ProcessManager] = None


def get_process_manager() -> ProcessManager:
    """获取全局进程管理器实例"""
    global _process_manager
    if _process_manager is None:
        _process_manager = ProcessManager()
    return _process_manager


def ensure_single_instance() -> bool:
    """确保单一实例运行"""
    return get_process_manager().check_single_instance()


def register_child_process(pid: int):
    """注册子进程"""
    get_process_manager().register_child_process(pid)


def shutdown_application():
    """关闭应用程序"""
    get_process_manager().shutdown_application()