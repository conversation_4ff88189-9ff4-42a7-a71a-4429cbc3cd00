"""
Theme Strategy Pattern for Task Manager

Provides different visual themes for the task management interface.
"""

from abc import ABC, abstractmethod
from PyQt6.QtWidgets import QWidget
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QColor


class ThemeStrategy(ABC):
    """抽象主题策略基类"""
    
    @abstractmethod
    def apply_styles(self, widget: QWidget):
        """应用主题样式到组件"""
        pass
    
    @abstractmethod
    def get_colors(self) -> dict:
        """获取主题颜色配置"""
        pass
    
    @abstractmethod
    def get_fonts(self) -> dict:
        """获取主题字体配置"""
        pass


class DefaultTheme(ThemeStrategy):
    """默认主题"""
    
    def apply_styles(self, widget: QWidget):
        """应用默认主题样式"""
        style = """
        QWidget {
            background-color: #f5f5f5;
            color: #333333;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        QPushButton {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #005a9e;
        }
        
        QPushButton:pressed {
            background-color: #004578;
        }
        
        QTableWidget {
            background-color: white;
            border: 1px solid #ddd;
            gridline-color: #e0e0e0;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        QTableWidget::item:selected {
            background-color: #e3f2fd;
        }
        
        QHeaderView::section {
            background-color: #f0f0f0;
            padding: 8px;
            border: none;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        
        QLabel {
            color: #333333;
        }
        
        QFrame {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        """
        widget.setStyleSheet(style)
    
    def get_colors(self) -> dict:
        """获取默认主题颜色"""
        return {
            'primary': '#007acc',
            'secondary': '#f5f5f5',
            'background': '#ffffff',
            'text': '#333333',
            'border': '#dddddd',
            'hover': '#005a9e',
            'selected': '#e3f2fd'
        }
    
    def get_fonts(self) -> dict:
        """获取默认主题字体"""
        return {
            'default': QFont('Microsoft YaHei', 9),
            'header': QFont('Microsoft YaHei', 10, QFont.Weight.Bold),
            'title': QFont('Microsoft YaHei', 12, QFont.Weight.Bold)
        }


class LakeBlueTheme(ThemeStrategy):
    """湖面蓝主题"""
    
    def apply_styles(self, widget: QWidget):
        """应用湖面蓝主题样式"""
        style = """
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e8f4f8, stop:1 #d1e7dd);
            color: #2c3e50;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #5dade2, stop:1 #3498db);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 10px;
        }
        
        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
        }
        
        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2980b9, stop:1 #1f618d);
        }
        
        QTableWidget {
            background-color: rgba(255, 255, 255, 0.9);
            border: 2px solid #85c1e9;
            border-radius: 8px;
            gridline-color: #aed6f1;
        }
        
        QTableWidget::item {
            padding: 10px;
            border-bottom: 1px solid #d6eaf8;
        }
        
        QTableWidget::item:selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #d6eaf8, stop:1 #aed6f1);
        }
        
        QHeaderView::section {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #85c1e9, stop:1 #5dade2);
            color: white;
            padding: 12px;
            border: none;
            font-weight: bold;
            font-size: 10px;
        }
        
        QLabel {
            color: #2c3e50;
            font-weight: 500;
        }
        
        QFrame {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid #85c1e9;
            border-radius: 8px;
        }
        """
        widget.setStyleSheet(style)
    
    def get_colors(self) -> dict:
        """获取湖面蓝主题颜色"""
        return {
            'primary': '#3498db',
            'secondary': '#85c1e9',
            'background': '#e8f4f8',
            'text': '#2c3e50',
            'border': '#85c1e9',
            'hover': '#2980b9',
            'selected': '#d6eaf8'
        }
    
    def get_fonts(self) -> dict:
        """获取湖面蓝主题字体"""
        return {
            'default': QFont('Microsoft YaHei', 9),
            'header': QFont('Microsoft YaHei', 10, QFont.Weight.Bold),
            'title': QFont('Microsoft YaHei', 12, QFont.Weight.Bold)
        }


class DarkTheme(ThemeStrategy):
    """深色主题"""
    
    def apply_styles(self, widget: QWidget):
        """应用深色主题样式"""
        style = """
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        QPushButton {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #505050;
            border-color: #777777;
        }
        
        QPushButton:pressed {
            background-color: #353535;
        }
        
        QTableWidget {
            background-color: #353535;
            border: 1px solid #555555;
            gridline-color: #555555;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #555555;
            color: #ffffff;
        }
        
        QTableWidget::item:selected {
            background-color: #404040;
        }
        
        QHeaderView::section {
            background-color: #404040;
            color: #ffffff;
            padding: 8px;
            border: none;
            border-bottom: 1px solid #555555;
            font-weight: bold;
        }
        
        QLabel {
            color: #ffffff;
        }
        
        QFrame {
            border: 1px solid #555555;
            border-radius: 4px;
            background-color: #353535;
        }
        """
        widget.setStyleSheet(style)
    
    def get_colors(self) -> dict:
        """获取深色主题颜色"""
        return {
            'primary': '#404040',
            'secondary': '#2b2b2b',
            'background': '#353535',
            'text': '#ffffff',
            'border': '#555555',
            'hover': '#505050',
            'selected': '#404040'
        }
    
    def get_fonts(self) -> dict:
        """获取深色主题字体"""
        return {
            'default': QFont('Microsoft YaHei', 9),
            'header': QFont('Microsoft YaHei', 10, QFont.Weight.Bold),
            'title': QFont('Microsoft YaHei', 12, QFont.Weight.Bold)
        }