# 桌面任务小部件 - 自动启动使用说明

## 🎯 概述

桌面任务小部件现在支持完全自动化运行，一次安装后无需任何手动操作，系统启动时自动运行并持续保持运行状态。

## 🚀 快速开始

### 一键安装（推荐）

1. **双击运行** `一键安装自启动.bat`
2. **选择安装方式**：
   - 选项1：注册表自启动（推荐）
   - 选项2：启动文件夹
   - 选项3：任务计划程序
   - 选项4：全部安装（最稳定）
3. **完成安装**，选择是否立即测试
4. **重启电脑**，桌面小部件将自动启动

### 手动管理

- **启动守护进程**：双击 `start_daemon.bat`
- **停止守护进程**：双击 `stop_daemon.bat`
- **卸载自启动**：双击 `卸载自启动.bat`

## 🔧 系统架构

### 守护进程机制

```
系统启动 → 自启动脚本 → 守护进程 → 桌面小部件
                ↓
            持续监控 ← 自动重启 ← 异常检测
```

### 核心组件

1. **守护进程** (`desktop_widget_daemon.py`)
   - 负责启动和监控桌面小部件
   - 检测崩溃并自动重启
   - 内存使用监控
   - 配置管理和日志记录

2. **桌面小部件** (`desktop_widget.py`)
   - 主要的用户界面
   - 任务显示和管理功能
   - 与守护进程协同工作

3. **自启动配置**
   - 注册表项：`HKCU\Software\Microsoft\Windows\CurrentVersion\Run`
   - 启动文件夹：`%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup`
   - 任务计划程序：系统级任务调度

## ⚙️ 配置选项

### 守护进程配置 (`daemon_config.json`)

```json
{
  "restart_delay": 5,           // 重启延迟（秒）
  "max_restart_per_hour": 10,   // 每小时最大重启次数
  "check_interval": 10,         // 检查间隔（秒）
  "auto_start_delay": 30,       // 开机启动延迟（秒）
  "enable_crash_detection": true,    // 启用崩溃检测
  "enable_memory_monitor": true,     // 启用内存监控
  "max_memory_mb": 500          // 最大内存使用（MB）
}
```

### 自定义配置

1. **修改启动延迟**：调整 `auto_start_delay` 参数
2. **调整监控频率**：修改 `check_interval` 参数
3. **内存限制**：设置 `max_memory_mb` 参数
4. **重启策略**：配置 `max_restart_per_hour` 和 `restart_delay`

## 📊 监控和日志

### 日志文件

- **守护进程日志**：`daemon.log`
- **应用程序日志**：`config/logs/app.log`
- **PID文件**：`daemon.pid`

### 状态检查

```bash
# 检查守护进程是否运行
if exist "daemon.pid" echo "守护进程正在运行"

# 查看日志
type daemon.log | more
```

## 🛠️ 故障排除

### 常见问题

1. **自启动不生效**
   - 检查是否有管理员权限
   - 确认Python环境正确
   - 查看系统事件日志

2. **小部件频繁重启**
   - 检查内存使用情况
   - 调整重启策略配置
   - 查看错误日志

3. **守护进程无法启动**
   - 确认依赖包已安装
   - 检查文件权限
   - 查看启动日志

### 诊断命令

```bash
# 检查Python环境
python --version

# 检查依赖包
python -c "import PyQt6, psutil"

# 查看进程状态
tasklist | findstr python

# 检查自启动项
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "桌面任务小部件"
```

## 🔒 安全考虑

### 权限要求

- **用户级权限**：注册表和启动文件夹
- **无需管理员权限**：完全在用户空间运行
- **文件访问**：仅访问项目目录和配置文件

### 数据安全

- **本地运行**：无网络连接需求
- **数据隔离**：独立的数据库和配置
- **日志轮转**：自动清理旧日志文件

## 📈 性能优化

### 资源使用

- **内存占用**：通常 < 100MB
- **CPU使用**：空闲时 < 1%
- **磁盘IO**：最小化数据库访问

### 优化建议

1. **调整检查间隔**：根据需要增加 `check_interval`
2. **内存限制**：设置合理的 `max_memory_mb`
3. **日志管理**：定期清理日志文件
4. **启动延迟**：增加 `auto_start_delay` 减少系统启动负载

## 🔄 更新和维护

### 版本更新

1. **停止守护进程**：运行 `stop_daemon.bat`
2. **更新文件**：替换新版本文件
3. **重启守护进程**：运行 `start_daemon.bat`

### 定期维护

- **清理日志**：定期删除旧的日志文件
- **检查配置**：确认配置文件正确
- **性能监控**：观察内存和CPU使用情况

## 📞 技术支持

### 联系方式

- **问题反馈**：查看日志文件并提供错误信息
- **功能建议**：描述具体需求和使用场景
- **技术讨论**：提供系统环境和配置信息

### 常用命令

```bash
# 完全重置
stop_daemon.bat
del daemon.pid
del daemon.log
start_daemon.bat

# 查看状态
type daemon.log | findstr "ERROR\|WARNING"

# 手动测试
python desktop_widget_daemon.py
```

---

## 🎉 总结

桌面任务小部件的自动启动功能提供了：

- ✅ **一键安装**：简单的安装流程
- ✅ **自动运行**：开机自动启动
- ✅ **持续监控**：守护进程保护
- ✅ **故障恢复**：自动重启机制
- ✅ **配置灵活**：可自定义参数
- ✅ **日志完整**：详细的运行记录

安装一次，终身受益！桌面小部件将成为您高效工作的得力助手。
