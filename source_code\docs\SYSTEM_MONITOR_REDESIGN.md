# 系统监控模块重新设计文档

## 概述

系统监控模块已完全重新设计，现在提供类似Windows任务管理器的界面和功能。新设计直接在软件内部显示系统性能数据和进程信息，无需调用外部工具。

## 主要功能

### 1. 性能监控 (Performance Tab)

#### 实时性能图表
- **CPU使用率图表**: 实时显示CPU使用百分比，蓝色曲线图
- **内存使用率图表**: 实时显示内存使用百分比，绿色曲线图  
- **磁盘使用率图表**: 实时显示磁盘活动百分比，橙色曲线图
- **网络使用率图表**: 实时显示网络传输速度，红色曲线图

#### 图表特性
- 深色主题，类似Windows任务管理器风格
- 60个数据点的历史记录
- 实时时间轴显示
- 自动缩放和网格线
- 每2秒更新一次数据

#### 性能摘要
- 实时显示当前CPU、内存、磁盘、网络使用率
- 大字体显示，便于快速查看

### 2. 进程监控 (Processes Tab)

#### 详细进程列表
- **进程名称**: 应用程序或服务名称
- **PID**: 进程标识符
- **状态**: 运行状态 (running, sleeping, etc.)
- **CPU使用率**: 实时CPU占用百分比
- **内存使用**: 物理内存使用量 (MB)
- **内存百分比**: 内存使用百分比
- **磁盘读取**: 磁盘读取量 (MB)
- **磁盘写入**: 磁盘写入量 (MB)
- **网络**: 网络使用情况 (暂时显示N/A)
- **用户**: 进程所属用户

#### 交互功能
- **搜索过滤**: 实时搜索进程名称
- **列排序**: 点击列标题进行排序
- **多选**: 支持选择多个进程行
- **显示所有用户进程**: 复选框控制显示范围

### 3. 系统工具集成

#### 工具栏快捷按钮
- **Task Manager**: 打开Windows任务管理器
- **Performance Monitor**: 打开Windows性能监视器
- **Resource Monitor**: 打开Windows资源监视器
- **System Info**: 打开Windows系统信息
- **Refresh All**: 手动刷新所有数据

#### SystemToolsManager功能
- 支持12种常用Windows系统工具
- 统一的错误处理和用户提示
- 自动检测工具可用性
- 支持MSC文件和EXE文件启动

## 技术实现

### 核心组件

#### PerformanceChart类
```python
class PerformanceChart(FigureCanvas):
    """实时性能图表组件"""
    - 基于matplotlib的实时图表
    - 支持数据点队列管理
    - 深色主题配置
    - 自动时间轴更新
```

#### ProcessTableModel类
```python
class ProcessTableModel(QAbstractTableModel):
    """进程列表数据模型"""
    - 支持PyQt6的MVC架构
    - 实时数据更新
    - 列排序和过滤支持
```

#### PerformanceWidget类
```python
class PerformanceWidget(QWidget):
    """性能监控标签页"""
    - 4个实时图表的网格布局
    - 性能摘要信息显示
    - 数据更新接口
```

#### ProcessesWidget类
```python
class ProcessesWidget(QWidget):
    """进程监控标签页"""
    - 搜索和过滤控件
    - 进程表格视图
    - 排序代理模型
```

### 数据获取

#### 使用psutil库
- **CPU信息**: `psutil.cpu_percent()`
- **内存信息**: `psutil.virtual_memory()`
- **磁盘信息**: `psutil.disk_usage()` 和 `shutil.disk_usage()`
- **网络信息**: `psutil.net_io_counters()`
- **进程信息**: `psutil.process_iter()`

#### 数据处理
- 磁盘使用率计算多个磁盘的平均值
- 网络速度转换为KB/s单位
- 进程信息包含详细的资源使用统计
- 异常处理确保数据获取的稳定性

### 界面设计

#### Windows任务管理器风格
- 深色主题的性能图表
- 标准的表格布局和列配置
- 工具栏按钮样式
- 标签页组织结构

#### 响应式布局
- 自适应窗口大小
- 列宽自动调整
- 图表自动缩放

## 更新和维护

### 实时更新机制
- QTimer每2秒触发数据刷新
- 性能图表实时绘制新数据点
- 进程列表动态更新
- 错误处理和日志记录

### 性能优化
- 数据点队列限制为60个
- 进程列表按需更新
- 图表重绘优化
- 内存使用控制

## 使用说明

### 启动系统监控
1. 在主界面点击"系统监控"模块
2. 默认显示Performance标签页
3. 切换到Processes标签页查看进程列表

### 性能监控使用
1. 观察实时性能图表
2. 查看底部的性能摘要
3. 图表显示最近60个数据点的历史

### 进程监控使用
1. 在搜索框输入进程名称进行过滤
2. 点击列标题进行排序
3. 勾选"显示所有用户进程"查看系统进程
4. 点击"Open Task Manager"打开Windows任务管理器

### 系统工具使用
1. 点击工具栏按钮快速打开系统工具
2. 使用"Refresh All"手动刷新数据
3. 所有工具都有错误处理和用户提示

## 依赖项

### 新增依赖
- `matplotlib==3.8.2`: 用于实时图表绘制

### 现有依赖
- `psutil==5.9.6`: 系统信息获取
- `PyQt6`: GUI框架
- `SQLAlchemy`: 数据库操作

## 文件结构

```
src/modules/system_monitor/
├── system_monitor_widget.py      # 主界面组件 (重新设计)
├── system_service.py             # 数据服务 (保持不变)
├── system_tools.py               # 系统工具管理 (新增)
└── system_monitor_widget_backup.py # 原版本备份
```

## 测试

### 测试脚本
- `test_system_monitor.py`: 独立测试脚本
- 测试所有主要功能
- 验证实时数据更新
- 检查界面响应性

### 测试要点
1. 性能图表实时更新
2. 进程列表数据准确性
3. 搜索和排序功能
4. 系统工具启动
5. 错误处理机制

## 未来改进

### 计划功能
1. 网络使用率按进程统计
2. GPU使用率监控
3. 系统温度监控
4. 历史数据存储和分析
5. 性能警报和通知
6. 自定义图表配置

### 性能优化
1. 数据缓存机制
2. 图表渲染优化
3. 内存使用优化
4. 多线程数据获取
