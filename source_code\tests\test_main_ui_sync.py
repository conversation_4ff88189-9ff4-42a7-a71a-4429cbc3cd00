"""
测试主界面UI状态同步功能
"""

import sys
from pathlib import Path
import time

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪主界面
from modules.study_tracker.study_tracker_widget import StudyTrackerWidget


class MainUISyncTestWindow(QMainWindow):
    """主界面UI状态同步测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("主界面UI状态同步测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("测试主界面UI状态同步")
        layout.addWidget(self.status_label)
        
        # 信息标签
        info_label = QLabel("""
🎯 测试目标：

验证主界面StudyTrackerWidget中的悬浮小组件状态是否能正确同步

🧪 测试步骤：

1. 创建StudyTrackerWidget（包含控制器和状态显示）
2. 通过主界面启用悬浮小组件
3. 观察主界面状态显示
4. 手动关闭悬浮小组件
5. 验证主界面状态是否自动更新为"已停止"

预期结果：
- 启用后主界面显示"状态: 运行中"
- 关闭后主界面自动显示"状态: 已停止"
- 无需手动刷新
        """)
        layout.addWidget(info_label)
        
        # 控制按钮
        create_widget_btn = QPushButton("1. 创建StudyTrackerWidget")
        create_widget_btn.clicked.connect(self.create_study_widget)
        layout.addWidget(create_widget_btn)
        
        enable_floating_btn = QPushButton("2. 启用悬浮小组件")
        enable_floating_btn.clicked.connect(self.enable_floating_widget)
        layout.addWidget(enable_floating_btn)
        
        check_status_btn = QPushButton("3. 检查主界面状态")
        check_status_btn.clicked.connect(self.check_main_ui_status)
        layout.addWidget(check_status_btn)
        
        # 状态显示
        self.main_ui_status_label = QLabel("主界面状态: 未创建")
        layout.addWidget(self.main_ui_status_label)
        
        self.test_result_label = QLabel("测试结果: 等待测试...")
        layout.addWidget(self.test_result_label)
        
        # StudyTrackerWidget实例
        self.study_widget = None
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_status_display)
        self.monitor_timer.start(1000)  # 每秒更新
    
    def create_study_widget(self):
        """创建StudyTrackerWidget"""
        try:
            if self.study_widget is None:
                self.status_label.setText("正在创建StudyTrackerWidget...")
                self.study_widget = StudyTrackerWidget()
                
                # 连接状态变化信号（如果有的话）
                if hasattr(self.study_widget, 'floating_widget_controller'):
                    controller = self.study_widget.floating_widget_controller
                    controller.status_changed.connect(self.on_controller_status_changed)
                
                # 显示StudyTrackerWidget
                self.study_widget.show()
                self.study_widget.raise_()
                self.study_widget.activateWindow()
                
                self.status_label.setText("✅ StudyTrackerWidget已创建")
                self.main_ui_status_label.setText("主界面状态: 已创建")
                print("✅ StudyTrackerWidget已创建")
            else:
                self.status_label.setText("StudyTrackerWidget已存在")
                
        except Exception as e:
            self.status_label.setText(f"创建StudyTrackerWidget失败: {str(e)}")
            print(f"Error creating StudyTrackerWidget: {e}")
            import traceback
            traceback.print_exc()
    
    def enable_floating_widget(self):
        """启用悬浮小组件"""
        if self.study_widget:
            try:
                # 模拟用户勾选启用复选框
                checkbox = self.study_widget.widget_toggle_checkbox
                if not checkbox.isChecked():
                    checkbox.setChecked(True)
                    self.status_label.setText("✅ 悬浮小组件已启用")
                    print("✅ 悬浮小组件已启用")
                else:
                    self.status_label.setText("悬浮小组件已经启用")
                    print("悬浮小组件已经启用")
                    
            except Exception as e:
                self.status_label.setText(f"启用悬浮小组件失败: {str(e)}")
                print(f"Error enabling floating widget: {e}")
        else:
            self.status_label.setText("请先创建StudyTrackerWidget")
    
    def check_main_ui_status(self):
        """检查主界面状态"""
        if self.study_widget:
            try:
                # 获取主界面状态标签的文本
                status_text = self.study_widget.widget_status_label.text()
                self.status_label.setText(f"主界面状态: {status_text}")
                print(f"主界面状态: {status_text}")
                
                # 检查控制器状态
                if hasattr(self.study_widget, 'floating_widget_controller'):
                    controller_status = self.study_widget.floating_widget_controller.is_running()
                    print(f"控制器状态: {'运行中' if controller_status else '已停止'}")
                
            except Exception as e:
                self.status_label.setText(f"检查状态失败: {str(e)}")
                print(f"Error checking status: {e}")
        else:
            self.status_label.setText("请先创建StudyTrackerWidget")
    
    def update_status_display(self):
        """更新状态显示"""
        if self.study_widget:
            try:
                # 获取主界面状态
                status_text = self.study_widget.widget_status_label.text()
                self.main_ui_status_label.setText(f"主界面状态: {status_text}")
                
                # 检查是否为停止状态
                if "已停止" in status_text:
                    self.test_result_label.setText("✅ 状态同步正常：主界面显示'已停止'")
                elif "运行中" in status_text:
                    self.test_result_label.setText("🔄 状态同步正常：主界面显示'运行中'")
                else:
                    self.test_result_label.setText(f"⏳ 当前状态：{status_text}")
                    
            except Exception as e:
                self.main_ui_status_label.setText("主界面状态: 错误")
                print(f"Update error: {e}")
    
    def on_controller_status_changed(self, is_running: bool):
        """控制器状态变化处理"""
        status_text = "运行中" if is_running else "已停止"
        print(f"🔄 控制器状态变化信号: {status_text}")
        
        if not is_running:
            self.test_result_label.setText("✅ 状态同步成功：收到控制器停止信号")
        else:
            self.test_result_label.setText("🔄 状态同步：收到控制器运行信号")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.study_widget:
            self.study_widget.close()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建测试窗口
        window = MainUISyncTestWindow()
        window.show()
        
        print("主界面UI状态同步测试程序已启动")
        print("请按照界面提示进行测试")
        print("重点观察主界面状态标签的变化")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
