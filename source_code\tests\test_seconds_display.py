"""
测试学习追踪悬浮小组件的秒数显示功能
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件
from modules.study_tracker.study_floating_widget import StudyFloatingWidget


class SecondsTestWindow(QMainWindow):
    """秒数显示测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("学习追踪悬浮小组件秒数显示测试")
        self.setGeometry(100, 100, 500, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("测试学习追踪悬浮小组件的秒数显示功能")
        layout.addWidget(self.status_label)
        
        # 信息标签
        info_label = QLabel("""
测试说明：
1. 点击"创建悬浮小组件"
2. 点击悬浮小组件的"开始"按钮开始学习
3. 观察时间显示是否精确到秒
4. 验证时间格式：
   - 小于1小时：MM:SS
   - 大于1小时：HH:MM:SS
5. 确认每秒都有更新
        """)
        layout.addWidget(info_label)
        
        # 控制按钮
        create_btn = QPushButton("创建悬浮小组件")
        create_btn.clicked.connect(self.create_floating_widget)
        layout.addWidget(create_btn)
        
        start_test_btn = QPushButton("开始学习测试")
        start_test_btn.clicked.connect(self.start_test_session)
        layout.addWidget(start_test_btn)
        
        stop_test_btn = QPushButton("停止学习测试")
        stop_test_btn.clicked.connect(self.stop_test_session)
        layout.addWidget(stop_test_btn)
        
        # 时间显示标签
        self.time_display_label = QLabel("当前时间: 00:00")
        layout.addWidget(self.time_display_label)
        
        # 悬浮小组件实例
        self.floating_widget = None
        
        # 测试定时器
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.update_time_display)
        self.test_timer.start(1000)  # 每秒更新
    
    def create_floating_widget(self):
        """创建悬浮小组件"""
        try:
            if self.floating_widget is None:
                self.status_label.setText("正在创建悬浮小组件...")
                self.floating_widget = StudyFloatingWidget()
                
                # 连接信号
                self.floating_widget.start_study.connect(self.on_start_signal)
                self.floating_widget.stop_study.connect(self.on_stop_signal)
            
            self.floating_widget.show()
            self.floating_widget.raise_()
            self.floating_widget.activateWindow()
            self.status_label.setText("悬浮小组件已创建并显示")
            print("悬浮小组件已创建，请观察时间显示是否精确到秒")
            
        except Exception as e:
            self.status_label.setText(f"创建失败: {str(e)}")
            print(f"Error creating floating widget: {e}")
            import traceback
            traceback.print_exc()
    
    def start_test_session(self):
        """开始测试学习会话"""
        if self.floating_widget:
            print("手动开始学习会话...")
            self.floating_widget.start_session()
            self.status_label.setText("学习会话已开始，观察秒数显示")
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def stop_test_session(self):
        """停止测试学习会话"""
        if self.floating_widget:
            print("手动停止学习会话...")
            self.floating_widget.stop_session()
            self.status_label.setText("学习会话已停止")
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def update_time_display(self):
        """更新时间显示"""
        if self.floating_widget and hasattr(self.floating_widget, 'study_service'):
            try:
                current_seconds = self.floating_widget.study_service.get_current_duration_seconds()
                hours = current_seconds // 3600
                minutes = (current_seconds % 3600) // 60
                seconds = current_seconds % 60
                
                if hours > 0:
                    time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                else:
                    time_str = f"{minutes:02d}:{seconds:02d}"
                
                self.time_display_label.setText(f"当前时间: {time_str} ({current_seconds}秒)")
            except:
                self.time_display_label.setText("当前时间: 00:00")
    
    def on_start_signal(self):
        """开始学习信号"""
        self.status_label.setText("✅ 学习已开始，观察秒数变化")
        print("✅ 学习已开始，观察悬浮小组件的秒数显示")
    
    def on_stop_signal(self):
        """停止学习信号"""
        self.status_label.setText("⏹️ 学习已停止")
        print("⏹️ 学习已停止")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.floating_widget:
            self.floating_widget.close()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建测试窗口
        window = SecondsTestWindow()
        window.show()
        
        print("秒数显示测试程序已启动")
        print("请按照界面提示进行测试")
        print("观察悬浮小组件的时间显示是否精确到秒")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
