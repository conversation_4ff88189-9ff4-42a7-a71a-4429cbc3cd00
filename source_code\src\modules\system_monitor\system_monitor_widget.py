"""
System Tools Widget for Personal Manager

This module provides quick access to Windows system tools like Task Manager,
Performance Monitor, and other system utilities.
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QFrame, QGroupBox,
    QMessageBox, QSizePolicy
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QIcon

from core.logger import LoggerMixin
from .system_tools import SystemToolsManager


class SystemMonitorWidget(QWidget, LoggerMixin):
    """Simplified system tools widget with quick access buttons"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.system_tools = SystemToolsManager()
        self.init_ui()
        self.logger.info("System Tools Widget initialized")

    def init_ui(self):
        """Initialize the simplified UI with system tool buttons"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Title
        title_label = QLabel("系统工具")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Description
        desc_label = QLabel("快速访问Windows系统工具")
        desc_label.setFont(QFont("Microsoft YaHei", 10))
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("color: #666666; margin-bottom: 20px;")
        layout.addWidget(desc_label)

        # Main tools group
        main_group = QGroupBox("主要工具")
        main_group.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        main_layout = QGridLayout(main_group)
        main_layout.setSpacing(15)

        # Task Manager button - most important
        self.task_manager_btn = self.create_tool_button(
            "任务管理器",
            "查看和管理系统进程",
            lambda: self.system_tools.launch_task_manager(self)
        )
        main_layout.addWidget(self.task_manager_btn, 0, 0)

        # Performance Monitor button
        self.performance_btn = self.create_tool_button(
            "性能监视器",
            "监控系统性能指标",
            lambda: self.system_tools.launch_performance_monitor(self)
        )
        main_layout.addWidget(self.performance_btn, 0, 1)

        # Resource Monitor button
        self.resource_btn = self.create_tool_button(
            "资源监视器",
            "详细的系统资源监控",
            lambda: self.system_tools.launch_resource_monitor(self)
        )
        main_layout.addWidget(self.resource_btn, 1, 0)

        # System Information button
        self.sysinfo_btn = self.create_tool_button(
            "系统信息",
            "查看系统配置信息",
            lambda: self.system_tools.launch_system_info(self)
        )
        main_layout.addWidget(self.sysinfo_btn, 1, 1)

        layout.addWidget(main_group)

        # Additional tools group
        additional_group = QGroupBox("其他工具")
        additional_group.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        additional_layout = QGridLayout(additional_group)
        additional_layout.setSpacing(15)

        # Event Viewer button
        self.event_viewer_btn = self.create_tool_button(
            "事件查看器",
            "查看系统事件日志",
            lambda: self.system_tools.launch_event_viewer(self)
        )
        additional_layout.addWidget(self.event_viewer_btn, 0, 0)

        # Services button
        self.services_btn = self.create_tool_button(
            "服务管理",
            "管理Windows服务",
            lambda: self.system_tools.launch_services(self)
        )
        additional_layout.addWidget(self.services_btn, 0, 1)

        # Device Manager button
        self.device_manager_btn = self.create_tool_button(
            "设备管理器",
            "管理硬件设备",
            lambda: self.system_tools.launch_device_manager(self)
        )
        additional_layout.addWidget(self.device_manager_btn, 1, 0)

        # Registry Editor button
        self.registry_btn = self.create_tool_button(
            "注册表编辑器",
            "编辑Windows注册表",
            lambda: self.system_tools.launch_registry_editor(self)
        )
        additional_layout.addWidget(self.registry_btn, 1, 1)

        layout.addWidget(additional_group)

        # Add stretch to push everything to top
        layout.addStretch()

    def create_tool_button(self, title: str, description: str, callback) -> QPushButton:
        """Create a styled tool button"""
        button = QPushButton()
        button.setFixedSize(200, 80)
        button.clicked.connect(callback)

        # Set button text (for accessibility)
        button.setText(f"{title}\n{description}")

        # Style the button
        button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 2px solid #d0d0d0;
                border-radius: 8px;
                text-align: center;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
                font-weight: bold;
                color: #333333;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
                border-color: #0078d4;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
                border-color: #005a9e;
            }
        """)

        return button