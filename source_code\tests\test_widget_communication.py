#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试桌面小组件进程间通信机制
用于验证手动启动时不会覆盖自启动命令的解决方案
"""

import os
import sys
import time
import psutil
import subprocess
from pathlib import Path


def check_daemon_running():
    """检查守护进程是否在运行"""
    project_root = Path(__file__).parent
    pid_file = project_root / "daemon.pid"
    
    if not pid_file.exists():
        return False, None
    
    try:
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())
        
        if psutil.pid_exists(pid):
            process = psutil.Process(pid)
            cmdline = ' '.join(process.cmdline())
            if 'desktop_widget_daemon.py' in cmdline:
                return True, pid
    except:
        pass
    
    return False, None


def send_show_command():
    """发送显示命令"""
    project_root = Path(__file__).parent
    command_file = project_root / "daemon_command.txt"
    
    try:
        with open(command_file, 'w', encoding='utf-8') as f:
            f.write("show_widget")
        print("✅ 显示命令已发送")
        return True
    except Exception as e:
        print(f"❌ 发送显示命令失败: {e}")
        return False


def start_daemon():
    """启动守护进程"""
    project_root = Path(__file__).parent
    daemon_script = project_root / "desktop_widget_daemon.py"
    
    try:
        print("🚀 启动守护进程...")
        process = subprocess.Popen(
            [sys.executable, str(daemon_script), "--manual"],
            cwd=str(project_root),
            creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        
        # 等待启动
        for i in range(10):
            time.sleep(0.5)
            is_running, pid = check_daemon_running()
            if is_running:
                print(f"✅ 守护进程启动成功，PID: {pid}")
                return True
        
        print("❌ 守护进程启动失败")
        return False
        
    except Exception as e:
        print(f"❌ 启动守护进程失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("桌面小组件进程间通信测试")
    print("=" * 50)
    
    # 检查守护进程状态
    is_running, pid = check_daemon_running()
    
    if is_running:
        print(f"🔍 检测到守护进程已在运行，PID: {pid}")
        print("📡 发送显示命令而不是重启进程...")
        
        if send_show_command():
            print("✅ 测试成功：已发送显示命令给现有守护进程")
            print("💡 这避免了覆盖自启动命令的问题")
        else:
            print("❌ 测试失败：无法发送显示命令")
    else:
        print("🔍 未检测到守护进程，启动新的守护进程...")
        
        if start_daemon():
            print("✅ 测试成功：守护进程启动完成")
            
            # 测试发送显示命令
            print("\n📡 测试发送显示命令...")
            time.sleep(2)
            if send_show_command():
                print("✅ 显示命令测试成功")
            else:
                print("❌ 显示命令测试失败")
        else:
            print("❌ 测试失败：无法启动守护进程")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)


if __name__ == "__main__":
    main()
