#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
桌面小部件测试版本
用于调试数据同步问题
"""

import sys
import os
from datetime import datetime, date
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QFont

# 直接导入
import core.database as db
import core.config as config
from modules.task_manager.task_service import TaskService

class TestDesktopWidget(QWidget):
    """测试桌面小部件"""
    
    def __init__(self):
        super().__init__()
        self.current_date = date.today()
        self.task_service = None
        self.init_db()
        self.init_ui()
        self.load_tasks()
        
        # 定时刷新
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_tasks)
        self.timer.start(5000)  # 5秒刷新
    
    def init_db(self):
        """初始化数据库"""
        try:
            config_manager = config.Config()
            db_manager = db.init_db_manager(config_manager)
            self.task_service = TaskService()
            print("数据库初始化成功")
        except Exception as e:
            print(f"数据库初始化失败: {e}")
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("测试桌面小部件")
        self.setGeometry(100, 100, 400, 600)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("桌面任务小部件测试")
        title.setFont(QFont('Microsoft YaHei', 12, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 日期
        self.date_label = QLabel()
        self.update_date()
        layout.addWidget(self.date_label)
        
        # 任务信息
        self.task_info = QLabel("正在加载任务...")
        layout.addWidget(self.task_info)
        
        # 详细任务列表
        self.task_details = QLabel()
        layout.addWidget(self.task_details)
        
        # 刷新按钮
        refresh_btn = QPushButton("手动刷新")
        refresh_btn.clicked.connect(self.load_tasks)
        layout.addWidget(refresh_btn)
    
    def update_date(self):
        """更新日期显示"""
        today = datetime.now()
        weekdays = ['一', '二', '三', '四', '五', '六', '日']
        weekday = weekdays[today.weekday()]
        date_text = f"{today.year}年{today.month:02d}月{today.day:02d}日 星期{weekday}"
        self.date_label.setText(date_text)
    
    def load_tasks(self):
        """加载任务"""
        try:
            print(f"\n=== 开始加载任务 {datetime.now()} ===")
            
            if not self.task_service:
                self.task_info.setText("数据库服务未初始化")
                return
            
            # 重新创建服务实例
            self.task_service = TaskService()
            
            # 获取所有任务
            all_tasks = self.task_service.get_tasks(limit=1000)
            print(f"数据库中总任务数: {len(all_tasks)}")
            
            # 过滤今天的任务
            today_tasks = []
            for task in all_tasks:
                task_date = None
                if task.start_date:
                    task_date = task.start_date.date()
                elif task.due_date:
                    task_date = task.due_date.date()
                
                print(f"任务: {task.title}, 日期: {task_date}, 今天: {self.current_date}, 匹配: {task_date == self.current_date}")
                
                if task_date == self.current_date:
                    today_tasks.append(task)
            
            print(f"今天的任务数: {len(today_tasks)}")
            
            # 按分类统计
            categories = {
                '上午': [],
                '下午': [],
                '晚上': [],
                '全天': [],
                '其他': [],
                '日总结': []
            }
            
            for task in today_tasks:
                category = self.determine_task_category(task)
                categories[category].append(task)
                print(f"任务 '{task.title}' 分类为: {category}")
            
            # 更新显示
            info_text = f"今天任务总数: {len(today_tasks)}\n"
            for cat, tasks in categories.items():
                info_text += f"{cat}: {len(tasks)}个\n"
            
            self.task_info.setText(info_text)
            
            # 详细任务列表
            details_text = ""
            for cat, tasks in categories.items():
                if tasks:
                    details_text += f"\n【{cat}】\n"
                    for task in tasks:
                        details_text += f"  • {task.title}\n"
            
            if not details_text:
                details_text = "今天暂无任务"
            
            self.task_details.setText(details_text)
            
        except Exception as e:
            error_msg = f"加载任务失败: {e}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.task_info.setText(error_msg)
    
    def determine_task_category(self, task) -> str:
        """确定任务分类"""
        if task.tags:
            tags = task.tags.lower()
            if '日总结' in tags or '总结' in tags:
                return '日总结'
            elif '上午' in tags:
                return '上午'
            elif '下午' in tags:
                return '下午'
            elif '晚上' in tags:
                return '晚上'
            elif '全天' in tags:
                return '全天'
        return '其他'

def main():
    app = QApplication(sys.argv)
    widget = TestDesktopWidget()
    widget.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
