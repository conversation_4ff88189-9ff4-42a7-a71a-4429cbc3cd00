"""
测试悬浮小组件关闭状态同步功能
"""

import sys
from pathlib import Path
import time

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件和控制器
from modules.study_tracker.study_floating_widget import StudyFloatingWidget
from modules.study_tracker.study_floating_widget_controller import StudyFloatingWidgetController


class CloseSyncTestWindow(QMainWindow):
    """关闭状态同步测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("关闭状态同步测试")
        self.setGeometry(100, 100, 600, 500)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("测试悬浮小组件关闭状态同步")
        layout.addWidget(self.status_label)
        
        # 信息标签
        info_label = QLabel("""
🎯 测试目标：

验证当悬浮小组件关闭时，桌面应用端状态能自动同步显示为停止状态

🧪 测试步骤：

1. 创建悬浮小组件控制器（模拟桌面应用端）
2. 创建悬浮小组件
3. 观察控制器状态变化
4. 关闭悬浮小组件
5. 验证控制器状态是否自动更新为"已停止"

预期结果：
- 创建悬浮小组件后，控制器状态应为"运行中"
- 关闭悬浮小组件后，控制器状态应自动变为"已停止"
- 不需要手动刷新或点击
        """)
        layout.addWidget(info_label)
        
        # 控制按钮
        create_controller_btn = QPushButton("1. 创建控制器（模拟桌面应用端）")
        create_controller_btn.clicked.connect(self.create_controller)
        layout.addWidget(create_controller_btn)
        
        create_widget_btn = QPushButton("2. 创建悬浮小组件")
        create_widget_btn.clicked.connect(self.create_floating_widget)
        layout.addWidget(create_widget_btn)
        
        close_widget_btn = QPushButton("3. 关闭悬浮小组件")
        close_widget_btn.clicked.connect(self.close_floating_widget)
        layout.addWidget(close_widget_btn)
        
        check_status_btn = QPushButton("4. 手动检查控制器状态")
        check_status_btn.clicked.connect(self.check_controller_status)
        layout.addWidget(check_status_btn)
        
        # 状态显示
        self.controller_status_label = QLabel("控制器状态: 未创建")
        layout.addWidget(self.controller_status_label)
        
        self.widget_status_label = QLabel("悬浮小组件状态: 未创建")
        layout.addWidget(self.widget_status_label)
        
        self.sync_result_label = QLabel("同步测试结果: 等待测试...")
        layout.addWidget(self.sync_result_label)
        
        # 组件实例
        self.controller = None
        self.floating_widget = None
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_status_display)
        self.monitor_timer.start(1000)  # 每秒更新
    
    def create_controller(self):
        """创建控制器"""
        try:
            if self.controller is None:
                self.status_label.setText("正在创建控制器...")
                self.controller = StudyFloatingWidgetController()
                
                # 连接状态变化信号
                self.controller.status_changed.connect(self.on_controller_status_changed)
                
                self.status_label.setText("✅ 控制器已创建")
                self.controller_status_label.setText("控制器状态: 已创建，监控中...")
                print("✅ 控制器已创建")
            else:
                self.status_label.setText("控制器已存在")
                
        except Exception as e:
            self.status_label.setText(f"创建控制器失败: {str(e)}")
            print(f"Error creating controller: {e}")
            import traceback
            traceback.print_exc()
    
    def create_floating_widget(self):
        """创建悬浮小组件"""
        try:
            if self.floating_widget is None:
                self.status_label.setText("正在创建悬浮小组件...")
                self.floating_widget = StudyFloatingWidget()
            
            self.floating_widget.show()
            self.floating_widget.raise_()
            self.floating_widget.activateWindow()
            
            self.status_label.setText("✅ 悬浮小组件已创建并显示")
            self.widget_status_label.setText("悬浮小组件状态: 已显示")
            print("✅ 悬浮小组件已创建并显示")
            
        except Exception as e:
            self.status_label.setText(f"创建悬浮小组件失败: {str(e)}")
            print(f"Error creating floating widget: {e}")
            import traceback
            traceback.print_exc()
    
    def close_floating_widget(self):
        """关闭悬浮小组件"""
        if self.floating_widget:
            self.status_label.setText("正在关闭悬浮小组件...")
            print("🔒 关闭悬浮小组件...")
            
            # 关闭悬浮小组件（这会触发closeEvent和状态文件创建）
            self.floating_widget.close()
            
            self.widget_status_label.setText("悬浮小组件状态: 已关闭")
            self.status_label.setText("✅ 悬浮小组件已关闭，等待控制器状态同步...")
            print("✅ 悬浮小组件已关闭，等待控制器状态同步...")
            
            # 等待几秒钟让控制器检测到状态变化
            QTimer.singleShot(3000, self.check_sync_result)
        else:
            self.status_label.setText("悬浮小组件未创建")
    
    def check_controller_status(self):
        """手动检查控制器状态"""
        if self.controller:
            is_running = self.controller.is_running()
            self.status_label.setText(f"手动检查：控制器检测到悬浮小组件状态为 {'运行中' if is_running else '已停止'}")
            print(f"手动检查：控制器状态 = {is_running}")
        else:
            self.status_label.setText("控制器未创建")
    
    def check_sync_result(self):
        """检查同步结果"""
        if self.controller:
            is_running = self.controller.is_running()
            if not is_running:
                self.sync_result_label.setText("✅ 同步测试通过：控制器状态已自动更新为'已停止'")
                print("✅ 同步测试通过：状态自动同步成功")
            else:
                self.sync_result_label.setText("❌ 同步测试失败：控制器状态仍为'运行中'")
                print("❌ 同步测试失败：状态未自动同步")
        else:
            self.sync_result_label.setText("❌ 无法测试：控制器未创建")
    
    def update_status_display(self):
        """更新状态显示"""
        if self.controller:
            is_running = self.controller.is_running()
            self.controller_status_label.setText(f"控制器状态: {'运行中' if is_running else '已停止'}")
    
    def on_controller_status_changed(self, is_running: bool):
        """控制器状态变化处理"""
        status_text = "运行中" if is_running else "已停止"
        print(f"🔄 控制器状态变化: {status_text}")
        self.controller_status_label.setText(f"控制器状态: {status_text}")
        
        if not is_running and self.floating_widget and not self.floating_widget.isVisible():
            self.sync_result_label.setText("✅ 状态同步成功：悬浮小组件关闭后控制器自动更新")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.floating_widget:
            self.floating_widget.close()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建测试窗口
        window = CloseSyncTestWindow()
        window.show()
        
        print("关闭状态同步测试程序已启动")
        print("请按照界面提示进行测试")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
