"""
System Service for System Monitor Module

This service provides system monitoring and hardware information functionality.
"""

import psutil
import platform
import json
import shutil
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session

from core.database import get_session
from core.logger import LoggerMixin
from models.system_models import SystemMetrics, ProcessMonitor, HardwareInfo, SystemAlert
from utils.system_utils import SystemUtils


class SystemService(LoggerMixin):
    """Service class for system monitoring operations"""
    
    def __init__(self):
        self.system_utils = SystemUtils()
        self._last_network_io = None
        self._last_disk_io = None
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get basic system information
        
        Returns:
            Dictionary containing system information
        """
        try:
            system_info = {
                'platform': platform.system(),
                'platform_release': platform.release(),
                'platform_version': platform.version(),
                'architecture': platform.machine(),
                'hostname': platform.node(),
                'processor': platform.processor(),
                'python_version': platform.python_version(),
                'boot_time': datetime.fromtimestamp(psutil.boot_time()),
                'uptime': datetime.now() - datetime.fromtimestamp(psutil.boot_time())
            }
            
            return system_info
            
        except Exception as e:
            self.logger.error(f"Error getting system info: {e}")
            return {}
    
    def get_cpu_info(self) -> Dict[str, Any]:
        """Get CPU information and usage
        
        Returns:
            Dictionary containing CPU information
        """
        try:
            cpu_info = {
                'physical_cores': psutil.cpu_count(logical=False),
                'total_cores': psutil.cpu_count(logical=True),
                'max_frequency': psutil.cpu_freq().max if psutil.cpu_freq() else None,
                'min_frequency': psutil.cpu_freq().min if psutil.cpu_freq() else None,
                'current_frequency': psutil.cpu_freq().current if psutil.cpu_freq() else None,
                'cpu_usage_total': psutil.cpu_percent(interval=1),
                'cpu_usage_per_core': psutil.cpu_percent(interval=1, percpu=True),
                'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
            }
            
            return cpu_info
            
        except Exception as e:
            self.logger.error(f"Error getting CPU info: {e}")
            return {}
    
    def get_memory_info(self) -> Dict[str, Any]:
        """Get memory information and usage
        
        Returns:
            Dictionary containing memory information
        """
        try:
            virtual_memory = psutil.virtual_memory()
            swap_memory = psutil.swap_memory()
            
            memory_info = {
                'total': virtual_memory.total,
                'available': virtual_memory.available,
                'used': virtual_memory.used,
                'free': virtual_memory.free,
                'percentage': virtual_memory.percent,
                'swap_total': swap_memory.total,
                'swap_used': swap_memory.used,
                'swap_free': swap_memory.free,
                'swap_percentage': swap_memory.percent
            }
            
            return memory_info
            
        except Exception as e:
            self.logger.error(f"Error getting memory info: {e}")
            return {}
    
    def get_disk_info(self) -> List[Dict[str, Any]]:
        """Get disk information and usage
        
        Returns:
            List of dictionaries containing disk information
        """
        try:
            disk_info = []
            
            # Get disk partitions
            partitions = psutil.disk_partitions()
            
            for partition in partitions:
                try:
                    # Skip system partitions that might cause issues
                    if partition.mountpoint in ['', None] or not partition.mountpoint.strip():
                        continue

                    # Skip certain file systems that might cause issues on Windows
                    if partition.fstype in ['', 'squashfs', 'tmpfs', 'devtmpfs']:
                        continue

                    # Use shutil.disk_usage for better Windows compatibility
                    try:
                        usage = shutil.disk_usage(partition.mountpoint)
                        partition_usage = type('DiskUsage', (), {
                            'total': usage.total,
                            'used': usage.total - usage.free,
                            'free': usage.free
                        })()
                    except Exception:
                        # Fallback to psutil if shutil fails
                        partition_usage = psutil.disk_usage(partition.mountpoint)

                    # Skip if total is 0 to avoid division by zero
                    if partition_usage.total == 0:
                        continue

                    disk_data = {
                        'device': str(partition.device) if partition.device else "unknown",
                        'mountpoint': str(partition.mountpoint) if partition.mountpoint else "unknown",
                        'file_system': str(partition.fstype) if partition.fstype else "unknown",
                        'total': partition_usage.total,
                        'used': partition_usage.used,
                        'free': partition_usage.free,
                        'percentage': (partition_usage.used / partition_usage.total) * 100
                    }

                    disk_info.append(disk_data)

                except (PermissionError, OSError, FileNotFoundError) as e:
                    # Skip partitions that can't be accessed
                    try:
                        device_name = str(partition.device) if partition.device else "unknown"
                        self.logger.debug(f"Skipping partition {device_name}: {str(e)}")
                    except:
                        self.logger.debug(f"Skipping partition: {str(e)}")
                    continue
            
            return disk_info
            
        except Exception as e:
            self.logger.error(f"Error getting disk info: {e}")
            return []
    
    def get_network_info(self) -> Dict[str, Any]:
        """Get network information and statistics
        
        Returns:
            Dictionary containing network information
        """
        try:
            network_io = psutil.net_io_counters()
            network_interfaces = psutil.net_if_addrs()
            network_stats = psutil.net_if_stats()
            
            # Calculate network speed if we have previous data
            bytes_sent_per_sec = 0
            bytes_recv_per_sec = 0
            
            if self._last_network_io:
                time_delta = 1  # Assume 1 second interval
                bytes_sent_per_sec = (network_io.bytes_sent - self._last_network_io.bytes_sent) / time_delta
                bytes_recv_per_sec = (network_io.bytes_recv - self._last_network_io.bytes_recv) / time_delta
            
            self._last_network_io = network_io
            
            network_info = {
                'bytes_sent': network_io.bytes_sent,
                'bytes_recv': network_io.bytes_recv,
                'packets_sent': network_io.packets_sent,
                'packets_recv': network_io.packets_recv,
                'bytes_sent_per_sec': bytes_sent_per_sec,
                'bytes_recv_per_sec': bytes_recv_per_sec,
                'interfaces': {}
            }
            
            # Add interface information
            for interface_name, addresses in network_interfaces.items():
                interface_info = {
                    'addresses': [],
                    'is_up': network_stats.get(interface_name, {}).isup if interface_name in network_stats else False,
                    'speed': network_stats.get(interface_name, {}).speed if interface_name in network_stats else None
                }
                
                for address in addresses:
                    interface_info['addresses'].append({
                        'family': str(address.family),
                        'address': address.address,
                        'netmask': address.netmask,
                        'broadcast': address.broadcast
                    })
                
                network_info['interfaces'][interface_name] = interface_info
            
            return network_info
            
        except Exception as e:
            self.logger.error(f"Error getting network info: {e}")
            return {}
    
    def get_process_list(self) -> List[Dict[str, Any]]:
        """Get list of running processes
        
        Returns:
            List of dictionaries containing process information
        """
        try:
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'status', 'create_time']):
                try:
                    process_info = proc.info
                    process_info['create_time'] = datetime.fromtimestamp(process_info['create_time'])
                    processes.append(process_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            # Sort by CPU usage
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
            
            return processes
            
        except Exception as e:
            self.logger.error(f"Error getting process list: {e}")
            return []
    
    def get_hardware_info(self) -> Dict[str, Any]:
        """Get detailed hardware information
        
        Returns:
            Dictionary containing hardware information
        """
        try:
            hardware_info = {
                'cpu': self.get_cpu_info(),
                'memory': self.get_memory_info(),
                'disks': self.get_disk_info(),
                'network': self.get_network_info(),
                'system': self.get_system_info()
            }
            
            # Add temperature information if available
            try:
                temperatures = psutil.sensors_temperatures()
                if temperatures:
                    hardware_info['temperatures'] = {}
                    for name, entries in temperatures.items():
                        hardware_info['temperatures'][name] = [
                            {
                                'label': entry.label or name,
                                'current': entry.current,
                                'high': entry.high,
                                'critical': entry.critical
                            }
                            for entry in entries
                        ]
            except AttributeError:
                # sensors_temperatures not available on this platform
                pass
            
            # Add battery information if available
            try:
                battery = psutil.sensors_battery()
                if battery:
                    hardware_info['battery'] = {
                        'percent': battery.percent,
                        'power_plugged': battery.power_plugged,
                        'time_left': battery.secsleft if battery.secsleft != psutil.POWER_TIME_UNLIMITED else None
                    }
            except AttributeError:
                # sensors_battery not available on this platform
                pass
            
            return hardware_info
            
        except Exception as e:
            self.logger.error(f"Error getting hardware info: {e}")
            return {}
    
    def record_system_metrics(self):
        """Record current system metrics to database"""
        try:
            cpu_info = self.get_cpu_info()
            memory_info = self.get_memory_info()
            disk_info = self.get_disk_info()
            network_info = self.get_network_info()
            
            # Get temperature data
            temperatures = {}
            try:
                temp_sensors = psutil.sensors_temperatures()
                if temp_sensors:
                    for name, entries in temp_sensors.items():
                        temperatures[name] = [entry.current for entry in entries]
            except AttributeError:
                pass
            
            with get_session() as session:
                metrics = SystemMetrics(
                    cpu_percent=cpu_info.get('cpu_usage_total', 0),
                    memory_percent=memory_info.get('percentage', 0),
                    disk_percent=sum(disk['percentage'] for disk in disk_info) / len(disk_info) if disk_info else 0,
                    network_bytes_sent=network_info.get('bytes_sent_per_sec', 0),
                    network_bytes_recv=network_info.get('bytes_recv_per_sec', 0),
                    temperatures=temperatures if temperatures else None
                )
                session.add(metrics)
                session.commit()
                
        except Exception as e:
            self.logger.error(f"Error recording system metrics: {e}")
    
    def get_metrics_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get system metrics history
        
        Args:
            hours: Number of hours of history to retrieve
            
        Returns:
            List of metrics dictionaries
        """
        try:
            with get_session() as session:
                start_time = datetime.utcnow() - timedelta(hours=hours)
                
                metrics = session.query(SystemMetrics).filter(
                    SystemMetrics.created_at >= start_time,
                    SystemMetrics.is_deleted == False
                ).order_by(SystemMetrics.created_at).all()
                
                return [metric.to_dict() for metric in metrics]
                
        except Exception as e:
            self.logger.error(f"Error getting metrics history: {e}")
            return []
