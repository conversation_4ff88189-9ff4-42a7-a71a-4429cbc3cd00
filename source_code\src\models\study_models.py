"""
学习时间记录模块数据模型

这个模块包含学习时间追踪系统的所有数据模型：
- StudySubject: 学习科目
- StudySession: 学习会话记录
- StudyGoal: 学习目标
- StudyBreak: 学习休息记录
"""

from datetime import datetime, timedelta
from enum import Enum
from typing import Optional, List

from sqlalchemy import (
    Column, String, Text, Integer, DateTime, Boolean, 
    ForeignKey, Enum as SQLEnum, Float, Index
)
from sqlalchemy.orm import relationship

from .base import BaseModel


class StudyStatus(Enum):
    """学习状态枚举"""
    ACTIVE = "active"           # 正在学习
    PAUSED = "paused"          # 暂停中
    COMPLETED = "completed"     # 已完成
    CANCELLED = "cancelled"     # 已取消


class GoalType(Enum):
    """目标类型枚举"""
    DAILY = "daily"            # 日目标
    WEEKLY = "weekly"          # 周目标
    MONTHLY = "monthly"        # 月目标
    CUSTOM = "custom"          # 自定义目标


class StudySubject(BaseModel):
    """学习科目模型"""
    
    __tablename__ = 'study_subjects'
    
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    color = Column(String(7), nullable=True)  # 十六进制颜色代码
    icon = Column(String(50), nullable=True)  # 图标名称
    
    # 统计字段
    total_sessions = Column(Integer, default=0)
    total_minutes = Column(Integer, default=0)
    average_session_minutes = Column(Float, default=0.0)
    
    # 设置
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    
    # 关联关系
    sessions = relationship("StudySession", back_populates="subject")
    goals = relationship("StudyGoal", back_populates="subject")
    
    # 索引
    __table_args__ = (
        Index('idx_study_subjects_name', 'name'),
        Index('idx_study_subjects_active', 'is_active'),
    )
    
    def update_statistics(self):
        """更新统计信息"""
        if self.sessions:
            completed_sessions = [s for s in self.sessions if s.status == StudyStatus.COMPLETED]
            self.total_sessions = len(completed_sessions)
            self.total_minutes = sum(s.duration_minutes for s in completed_sessions)
            self.average_session_minutes = self.total_minutes / self.total_sessions if self.total_sessions > 0 else 0
    
    def __repr__(self):
        return f"<StudySubject(name='{self.name}', total_minutes={self.total_minutes})>"


class StudySession(BaseModel):
    """学习会话记录模型"""
    
    __tablename__ = 'study_sessions'
    
    # 基本信息
    title = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    subject_id = Column(String(36), ForeignKey('study_subjects.id'), nullable=False)
    
    # 时间信息
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=True)
    planned_duration_minutes = Column(Integer, nullable=True)  # 计划学习时长
    duration_minutes = Column(Integer, default=0)  # 实际学习时长
    break_minutes = Column(Integer, default=0)     # 休息时长
    
    # 状态和质量
    status = Column(SQLEnum(StudyStatus), default=StudyStatus.ACTIVE, nullable=False)
    focus_rating = Column(Integer, nullable=True)  # 专注度评分 1-5
    difficulty_rating = Column(Integer, nullable=True)  # 难度评分 1-5
    satisfaction_rating = Column(Integer, nullable=True)  # 满意度评分 1-5
    
    # 学习内容和笔记
    topics_covered = Column(Text, nullable=True)  # 学习内容
    notes = Column(Text, nullable=True)           # 学习笔记
    achievements = Column(Text, nullable=True)    # 学习成果
    
    # 环境信息
    location = Column(String(100), nullable=True)  # 学习地点
    study_method = Column(String(100), nullable=True)  # 学习方法
    
    # 关联关系
    subject = relationship("StudySubject", back_populates="sessions")
    breaks = relationship("StudyBreak", back_populates="session")
    
    # 索引
    __table_args__ = (
        Index('idx_study_sessions_subject', 'subject_id'),
        Index('idx_study_sessions_start_time', 'start_time'),
        Index('idx_study_sessions_status', 'status'),
        Index('idx_study_sessions_date', 'start_time'),
    )
    
    def start_session(self):
        """开始学习会话"""
        self.start_time = datetime.utcnow()
        self.status = StudyStatus.ACTIVE
    
    def pause_session(self):
        """暂停学习会话"""
        if self.status == StudyStatus.ACTIVE:
            self.status = StudyStatus.PAUSED
            self._update_duration()
    
    def resume_session(self):
        """恢复学习会话"""
        if self.status == StudyStatus.PAUSED:
            self.status = StudyStatus.ACTIVE
    
    def complete_session(self):
        """完成学习会话"""
        self.end_time = datetime.now()  # 使用本地时间而不是UTC时间
        self.status = StudyStatus.COMPLETED
        self._update_duration()

    def cancel_session(self):
        """取消学习会话"""
        self.end_time = datetime.now()  # 使用本地时间而不是UTC时间
        self.status = StudyStatus.CANCELLED
        self._update_duration()
    
    def _update_duration(self):
        """更新学习时长"""
        if self.start_time and self.end_time:
            total_duration = (self.end_time - self.start_time).total_seconds() / 60
            self.duration_minutes = int(total_duration - self.break_minutes)
    
    def get_current_duration(self) -> int:
        """获取当前学习时长（分钟）"""
        if self.start_time:
            if self.end_time:
                total_duration = (self.end_time - self.start_time).total_seconds() / 60
            else:
                total_duration = (datetime.now() - self.start_time).total_seconds() / 60  # 使用本地时间
            return max(0, int(total_duration - self.break_minutes))
        return 0
    
    def __repr__(self):
        return f"<StudySession(subject='{self.subject.name if self.subject else 'Unknown'}', duration={self.duration_minutes}min, status='{self.status.value}')>"


class StudyGoal(BaseModel):
    """学习目标模型"""
    
    __tablename__ = 'study_goals'
    
    # 基本信息
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    subject_id = Column(String(36), ForeignKey('study_subjects.id'), nullable=True)
    
    # 目标设置
    goal_type = Column(SQLEnum(GoalType), nullable=False)
    target_minutes = Column(Integer, nullable=False)  # 目标学习时长（分钟）
    target_sessions = Column(Integer, nullable=True)  # 目标学习次数
    
    # 时间范围
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    
    # 进度跟踪
    current_minutes = Column(Integer, default=0)
    current_sessions = Column(Integer, default=0)
    completion_percentage = Column(Float, default=0.0)
    
    # 状态
    is_active = Column(Boolean, default=True)
    is_completed = Column(Boolean, default=False)
    completed_at = Column(DateTime, nullable=True)
    
    # 关联关系
    subject = relationship("StudySubject", back_populates="goals")
    
    # 索引
    __table_args__ = (
        Index('idx_study_goals_subject', 'subject_id'),
        Index('idx_study_goals_type', 'goal_type'),
        Index('idx_study_goals_date_range', 'start_date', 'end_date'),
        Index('idx_study_goals_active', 'is_active'),
    )
    
    def update_progress(self):
        """更新目标进度"""
        if self.subject:
            # 计算时间范围内的学习记录
            sessions_in_range = [
                s for s in self.subject.sessions 
                if (s.start_time >= self.start_date and 
                    s.start_time <= self.end_date and 
                    s.status == StudyStatus.COMPLETED)
            ]
            
            self.current_sessions = len(sessions_in_range)
            self.current_minutes = sum(s.duration_minutes for s in sessions_in_range)
            
            # 计算完成百分比
            if self.target_minutes > 0:
                self.completion_percentage = min(100.0, (self.current_minutes / self.target_minutes) * 100)
            
            # 检查是否完成
            if (self.current_minutes >= self.target_minutes and 
                (not self.target_sessions or self.current_sessions >= self.target_sessions)):
                self.is_completed = True
                if not self.completed_at:
                    self.completed_at = datetime.utcnow()
    
    def is_in_date_range(self, date: datetime = None) -> bool:
        """检查日期是否在目标范围内"""
        if date is None:
            date = datetime.utcnow()
        return self.start_date <= date <= self.end_date
    
    def __repr__(self):
        return f"<StudyGoal(title='{self.title}', target={self.target_minutes}min, progress={self.completion_percentage:.1f}%)>"


class StudyBreak(BaseModel):
    """学习休息记录模型"""
    
    __tablename__ = 'study_breaks'
    
    session_id = Column(String(36), ForeignKey('study_sessions.id'), nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=True)
    duration_minutes = Column(Integer, default=0)
    break_type = Column(String(50), nullable=True)  # 休息类型：short, long, meal等
    notes = Column(Text, nullable=True)
    
    # 关联关系
    session = relationship("StudySession", back_populates="breaks")
    
    # 索引
    __table_args__ = (
        Index('idx_study_breaks_session', 'session_id'),
        Index('idx_study_breaks_start_time', 'start_time'),
    )
    
    def start_break(self):
        """开始休息"""
        self.start_time = datetime.utcnow()
    
    def end_break(self):
        """结束休息"""
        self.end_time = datetime.utcnow()
        if self.start_time:
            self.duration_minutes = int((self.end_time - self.start_time).total_seconds() / 60)
    
    def __repr__(self):
        return f"<StudyBreak(duration={self.duration_minutes}min, type='{self.break_type}')>"
