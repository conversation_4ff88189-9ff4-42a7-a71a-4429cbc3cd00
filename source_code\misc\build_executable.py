#!/usr/bin/env python3
"""
Build executable for Personal Manager using PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """Check if PyInstaller is installed"""
    try:
        import PyInstaller
        return True
    except ImportError:
        return False

def install_pyinstaller():
    """Install PyInstaller"""
    print("Installing PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        return True
    except subprocess.CalledProcessError:
        return False

def build_executable():
    """Build executable using PyInstaller"""
    
    # Check if PyInstaller is available
    if not check_pyinstaller():
        print("PyInstaller not found. Installing...")
        if not install_pyinstaller():
            print("Failed to install PyInstaller")
            return False
    
    # Get paths
    script_dir = Path(__file__).parent
    main_py = script_dir / "main.py"
    icon_file = script_dir / "resources" / "icons" / "app.ico"
    
    # Check if main.py exists
    if not main_py.exists():
        print(f"Error: {main_py} not found")
        return False
    
    # Check if icon exists
    if not icon_file.exists():
        print(f"Warning: Icon file {icon_file} not found")
        icon_file = None
    
    # Build command
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",  # Create a single executable file
        "--windowed",  # Don't show console window
        "--name", "PersonalManager",
        "--distpath", str(script_dir / "dist"),
        "--workpath", str(script_dir / "build"),
        "--specpath", str(script_dir),
    ]
    
    # Add icon if available
    if icon_file:
        cmd.extend(["--icon", str(icon_file)])
    
    # Add hidden imports for PyQt6
    cmd.extend([
        "--hidden-import", "PyQt6.QtCore",
        "--hidden-import", "PyQt6.QtGui", 
        "--hidden-import", "PyQt6.QtWidgets",
        "--hidden-import", "PyQt6.QtSql",
        "--hidden-import", "sqlalchemy",
        "--hidden-import", "sqlalchemy.dialects.sqlite",
    ])
    
    # Add data files
    cmd.extend([
        "--add-data", f"{script_dir / 'src'};src",
        "--add-data", f"{script_dir / 'resources'};resources",
    ])
    
    # Add main script
    cmd.append(str(main_py))
    
    print("Building executable...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        # Run PyInstaller
        result = subprocess.run(cmd, cwd=script_dir, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("Executable built successfully!")
            exe_path = script_dir / "dist" / "PersonalManager.exe"
            if exe_path.exists():
                print(f"Executable location: {exe_path}")
                return True
            else:
                print("Executable file not found in expected location")
                return False
        else:
            print("PyInstaller failed:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"Error running PyInstaller: {e}")
        return False

def create_exe_shortcut():
    """Create shortcut for the executable"""
    script_dir = Path(__file__).parent
    exe_path = script_dir / "dist" / "PersonalManager.exe"
    
    if not exe_path.exists():
        print("Executable not found, cannot create shortcut")
        return False
    
    # Create PowerShell script for exe shortcut
    ps_script = f'''
# Create shortcut for PersonalManager.exe
$ExePath = "{exe_path}"
$ShortcutName = "私人经理"
$Description = "Personal Manager - 个人管理系统"
$DesktopPath = [Environment]::GetFolderPath("Desktop")
$ShortcutPath = Join-Path $DesktopPath "$ShortcutName.lnk"

$WshShell = New-Object -ComObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut($ShortcutPath)
$Shortcut.TargetPath = $ExePath
$Shortcut.WorkingDirectory = Split-Path $ExePath
$Shortcut.Description = $Description
$Shortcut.WindowStyle = 1
$Shortcut.Save()

Write-Host "Executable shortcut created: $ShortcutPath"
'''
    
    try:
        result = subprocess.run(
            ["powershell", "-Command", ps_script],
            capture_output=True, text=True
        )
        
        if result.returncode == 0:
            print("Executable shortcut created successfully!")
            return True
        else:
            print(f"Failed to create shortcut: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"Error creating shortcut: {e}")
        return False

def main():
    """Main function"""
    print("=" * 50)
    print("Personal Manager Executable Builder")
    print("=" * 50)
    
    # Build executable
    if build_executable():
        print("\nExecutable built successfully!")
        
        # Ask if user wants to create shortcut
        create_shortcut = input("\nCreate desktop shortcut for executable? (y/n): ")
        if create_shortcut.lower() in ['y', 'yes']:
            create_exe_shortcut()
        
        print("\nBuild complete!")
        print("You can find the executable in the 'dist' folder.")
        
    else:
        print("\nFailed to build executable.")
        print("You can still use the batch file launcher: start_personal_manager.bat")

if __name__ == "__main__":
    main()
