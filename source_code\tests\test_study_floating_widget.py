"""
测试学习追踪悬浮小组件功能
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.config import Config
from core.logger import setup_logging
from core.database import DatabaseManager

# 导入学习追踪悬浮小组件
from modules.study_tracker.study_floating_widget import StudyFloatingWidget


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("学习追踪悬浮小组件测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("测试学习追踪悬浮小组件功能")
        layout.addWidget(self.status_label)
        
        # 信息标签
        info_label = QLabel("""
测试说明：
1. 点击"显示悬浮小组件"按钮
2. 在悬浮小组件中测试以下功能：
   - 开始学习
   - 暂停学习
   - 继续学习
   - 停止学习
   - 主界面按钮
3. 观察状态变化和时间显示
        """)
        layout.addWidget(info_label)
        
        # 测试按钮
        show_btn = QPushButton("显示悬浮小组件")
        show_btn.clicked.connect(self.show_floating_widget)
        layout.addWidget(show_btn)
        
        hide_btn = QPushButton("隐藏悬浮小组件")
        hide_btn.clicked.connect(self.hide_floating_widget)
        layout.addWidget(hide_btn)
        
        # 悬浮小组件实例
        self.floating_widget = None
    
    def show_floating_widget(self):
        """显示悬浮小组件"""
        try:
            if self.floating_widget is None:
                self.floating_widget = StudyFloatingWidget()
                
                # 连接信号
                self.floating_widget.show_main_window.connect(self.on_show_main_window)
                self.floating_widget.start_study.connect(self.on_start_study)
                self.floating_widget.pause_study.connect(self.on_pause_study)
                self.floating_widget.resume_study.connect(self.on_resume_study)
                self.floating_widget.stop_study.connect(self.on_stop_study)
            
            self.floating_widget.show()
            self.floating_widget.raise_()
            self.floating_widget.activateWindow()
            self.status_label.setText("悬浮小组件已显示")
            
        except Exception as e:
            self.status_label.setText(f"显示失败: {str(e)}")
            print(f"Error showing floating widget: {e}")
    
    def hide_floating_widget(self):
        """隐藏悬浮小组件"""
        if self.floating_widget:
            self.floating_widget.hide()
            self.status_label.setText("悬浮小组件已隐藏")
    
    def on_show_main_window(self):
        """显示主窗口信号处理"""
        self.status_label.setText("收到显示主窗口信号")
        self.show()
        self.raise_()
        self.activateWindow()
        print("显示主窗口信号触发")
    
    def on_start_study(self):
        """开始学习信号处理"""
        self.status_label.setText("收到开始学习信号")
        print("开始学习信号触发")
    
    def on_pause_study(self):
        """暂停学习信号处理"""
        self.status_label.setText("收到暂停学习信号")
        print("暂停学习信号触发")
    
    def on_resume_study(self):
        """恢复学习信号处理"""
        self.status_label.setText("收到恢复学习信号")
        print("恢复学习信号触发")
    
    def on_stop_study(self):
        """停止学习信号处理"""
        self.status_label.setText("收到停止学习信号")
        print("停止学习信号触发")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.floating_widget:
            self.floating_widget.close()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 初始化配置和日志
    try:
        config = Config()
        setup_logging(config)

        # 初始化数据库
        db_manager = DatabaseManager(config)
        db_manager.init_database()
        print("数据库初始化成功")
    except Exception as e:
        print(f"初始化失败: {e}")
        # 使用基本日志配置
        import logging
        logging.basicConfig(level=logging.INFO)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("学习追踪悬浮小组件测试程序已启动")
    print("功能测试:")
    print("- 开始学习: 创建新的学习会话")
    print("- 暂停学习: 暂停当前学习会话")
    print("- 继续学习: 恢复暂停的学习会话")
    print("- 停止学习: 完成当前学习会话")
    print("- 主界面: 显示主窗口")
    print("- 拖拽: 可以拖拽悬浮小组件移动位置")
    print("- 折叠: 可以折叠/展开悬浮小组件")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
