"""
Base Models for Personal Manager System
"""

import uuid
from datetime import datetime
from typing import Any, Dict

from sqlalchemy import Column, String, DateTime, Boolean, Text, Integer
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import Session

from core.database import Base


class TimestampMixin:
    """Mixin for adding timestamp fields"""
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class UUIDMixin:
    """Mixin for adding UUID primary key"""
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))


class SoftDeleteMixin:
    """Mixin for soft delete functionality"""
    
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime, nullable=True)
    
    def soft_delete(self):
        """Mark record as deleted"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self):
        """Restore soft-deleted record"""
        self.is_deleted = False
        self.deleted_at = None


class BaseModel(Base, UUIDMixin, TimestampMixin, SoftDeleteMixin):
    """Base model class with common fields and methods"""
    
    __abstract__ = True
    
    @declared_attr
    def __tablename__(cls):
        """Generate table name from class name"""
        return cls.__name__.lower() + 's'
    
    def to_dict(self, exclude_fields: list = None) -> Dict[str, Any]:
        """Convert model instance to dictionary
        
        Args:
            exclude_fields: List of fields to exclude
            
        Returns:
            Dictionary representation of the model
        """
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[column.name] = value
        
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude_fields: list = None):
        """Update model instance from dictionary
        
        Args:
            data: Dictionary with field values
            exclude_fields: List of fields to exclude from update
        """
        exclude_fields = exclude_fields or ['id', 'created_at']
        
        for key, value in data.items():
            if key not in exclude_fields and hasattr(self, key):
                setattr(self, key, value)
        
        self.updated_at = datetime.utcnow()
    
    @classmethod
    def create(cls, session: Session, **kwargs):
        """Create new instance and save to database
        
        Args:
            session: Database session
            **kwargs: Field values
            
        Returns:
            Created instance
        """
        instance = cls(**kwargs)
        session.add(instance)
        session.flush()  # Get the ID without committing
        return instance
    
    @classmethod
    def get_by_id(cls, session: Session, id: str):
        """Get instance by ID
        
        Args:
            session: Database session
            id: Instance ID
            
        Returns:
            Instance or None
        """
        return session.query(cls).filter(
            cls.id == id,
            cls.is_deleted == False
        ).first()
    
    @classmethod
    def get_all(cls, session: Session, include_deleted: bool = False):
        """Get all instances
        
        Args:
            session: Database session
            include_deleted: Whether to include soft-deleted records
            
        Returns:
            List of instances
        """
        query = session.query(cls)
        if not include_deleted:
            query = query.filter(cls.is_deleted == False)
        return query.all()
    
    @classmethod
    def count(cls, session: Session, include_deleted: bool = False) -> int:
        """Count instances
        
        Args:
            session: Database session
            include_deleted: Whether to include soft-deleted records
            
        Returns:
            Number of instances
        """
        query = session.query(cls)
        if not include_deleted:
            query = query.filter(cls.is_deleted == False)
        return query.count()
    
    def save(self, session: Session):
        """Save instance to database
        
        Args:
            session: Database session
        """
        session.add(self)
        session.flush()
    
    def delete(self, session: Session, hard_delete: bool = False):
        """Delete instance
        
        Args:
            session: Database session
            hard_delete: Whether to permanently delete (default: soft delete)
        """
        if hard_delete:
            session.delete(self)
        else:
            self.soft_delete()
            session.add(self)
        session.flush()
    
    def __repr__(self):
        """String representation of the model"""
        return f"<{self.__class__.__name__}(id={self.id})>"


class Setting(BaseModel):
    """Application settings model"""
    
    __tablename__ = 'settings'
    
    key = Column(String(255), unique=True, nullable=False, index=True)
    value = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=True, index=True)
    is_system = Column(Boolean, default=False, nullable=False)
    
    @classmethod
    def get_setting(cls, session: Session, key: str, default: Any = None):
        """Get setting value by key
        
        Args:
            session: Database session
            key: Setting key
            default: Default value if not found
            
        Returns:
            Setting value or default
        """
        setting = session.query(cls).filter(
            cls.key == key,
            cls.is_deleted == False
        ).first()
        
        if setting:
            # Try to parse JSON values
            try:
                import json
                return json.loads(setting.value)
            except (json.JSONDecodeError, TypeError):
                return setting.value
        
        return default
    
    @classmethod
    def set_setting(cls, session: Session, key: str, value: Any, 
                   description: str = None, category: str = None):
        """Set setting value
        
        Args:
            session: Database session
            key: Setting key
            value: Setting value
            description: Setting description
            category: Setting category
        """
        # Convert value to JSON if needed
        if not isinstance(value, str):
            import json
            value = json.dumps(value)
        
        setting = session.query(cls).filter(
            cls.key == key,
            cls.is_deleted == False
        ).first()
        
        if setting:
            setting.value = value
            if description:
                setting.description = description
            if category:
                setting.category = category
            setting.updated_at = datetime.utcnow()
        else:
            setting = cls(
                key=key,
                value=value,
                description=description,
                category=category
            )
            session.add(setting)
        
        session.flush()
        return setting


class AuditLog(BaseModel):
    """Audit log for tracking user actions"""
    
    __tablename__ = 'audit_logs'
    
    action = Column(String(100), nullable=False, index=True)
    entity_type = Column(String(100), nullable=True, index=True)
    entity_id = Column(String(36), nullable=True, index=True)
    details = Column(Text, nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    @classmethod
    def log_action(cls, session: Session, action: str, entity_type: str = None,
                  entity_id: str = None, details: str = None):
        """Log user action
        
        Args:
            session: Database session
            action: Action performed
            entity_type: Type of entity affected
            entity_id: ID of entity affected
            details: Additional details
        """
        log_entry = cls(
            action=action,
            entity_type=entity_type,
            entity_id=entity_id,
            details=details
        )
        session.add(log_entry)
        session.flush()
        return log_entry
