"""
重置学习数据，清理所有异常数据
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 初始化核心组件
from core.database import init_db_manager, get_session
from core.config import Config
from models.study_models import StudySession, StudyBreak
from datetime import datetime, date


def reset_study_data():
    """重置学习数据"""
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        print("\n🔄 重置学习数据...")
        
        with get_session() as session:
            # 删除所有学习会话
            session_count = session.query(StudySession).count()
            session.query(StudySession).delete()
            print(f"删除了 {session_count} 个学习会话")
            
            # 删除所有学习休息记录
            break_count = session.query(StudyBreak).count()
            session.query(StudyBreak).delete()
            print(f"删除了 {break_count} 个休息记录")
            
            session.commit()
            print("✅ 数据重置完成")
            
            # 验证清理结果
            remaining_sessions = session.query(StudySession).count()
            remaining_breaks = session.query(StudyBreak).count()
            
            print(f"剩余学习会话: {remaining_sessions}")
            print(f"剩余休息记录: {remaining_breaks}")
            
    except Exception as e:
        print(f"重置失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("⚠️  警告：此操作将删除所有学习数据！")
    response = input("确定要重置所有学习数据吗? (yes/no): ")
    if response.lower() == 'yes':
        reset_study_data()
        print("\n✅ 数据重置完成，可以重新开始测试")
    else:
        print("操作已取消")
