"""
应用管理和笔记相关数据模型

包含笔记管理、密码管理等模型
"""

from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, ForeignKey, Index, JSON
from sqlalchemy.orm import relationship, Session
from typing import List, Optional, Dict, Any
from datetime import datetime

from .base import BaseModel


class Application(BaseModel):
    """应用程序模型"""
    
    __tablename__ = 'applications'
    
    name = Column(String(255), nullable=False)
    display_name = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    exe_path = Column(Text, nullable=False)
    working_directory = Column(Text, nullable=True)
    arguments = Column(Text, nullable=True)
    icon_path = Column(Text, nullable=True)
    
    # 分类和标签
    category = Column(String(100), nullable=True)
    tags = Column(String(500), nullable=True)  # 逗号分隔
    
    # 使用统计
    launch_count = Column(Integer, default=0)
    last_launched = Column(DateTime, nullable=True)
    
    # 设置
    is_favorite = Column(Boolean, default=False)
    is_pinned = Column(Boolean, default=False)
    sort_order = Column(Integer, default=0)
    
    # 启动选项
    run_as_admin = Column(Boolean, default=False)
    start_minimized = Column(Boolean, default=False)
    auto_start = Column(Boolean, default=False)
    
    # 索引
    __table_args__ = (
        Index('idx_applications_name', 'name'),
        Index('idx_applications_category', 'category'),
        Index('idx_applications_favorite', 'is_favorite'),
        Index('idx_applications_pinned', 'is_pinned'),
        Index('idx_applications_launch_count', 'launch_count'),
    )
    
    @property
    def tag_list(self) -> List[str]:
        """获取标签列表"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
    
    @tag_list.setter
    def tag_list(self, tags: List[str]):
        """设置标签列表"""
        self.tags = ','.join(tags) if tags else None
    
    def record_launch(self):
        """记录启动"""
        self.launch_count += 1
        self.last_launched = datetime.utcnow()
    
    @classmethod
    def get_favorites(cls, session: Session) -> List['Application']:
        """获取收藏的应用"""
        return session.query(cls).filter(
            cls.is_favorite == True,
            cls.is_deleted == False
        ).order_by(cls.sort_order, cls.name).all()
    
    @classmethod
    def get_pinned(cls, session: Session) -> List['Application']:
        """获取固定的应用"""
        return session.query(cls).filter(
            cls.is_pinned == True,
            cls.is_deleted == False
        ).order_by(cls.sort_order, cls.name).all()
    
    @classmethod
    def get_by_category(cls, session: Session, category: str) -> List['Application']:
        """根据分类获取应用"""
        return session.query(cls).filter(
            cls.category == category,
            cls.is_deleted == False
        ).order_by(cls.name).all()
    
    @classmethod
    def get_most_used(cls, session: Session, limit: int = 10) -> List['Application']:
        """获取最常用的应用"""
        return session.query(cls).filter(
            cls.is_deleted == False
        ).order_by(cls.launch_count.desc()).limit(limit).all()
    
    @classmethod
    def search_applications(cls, session: Session, query: str) -> List['Application']:
        """搜索应用"""
        search_pattern = f"%{query}%"
        return session.query(cls).filter(
            (cls.name.like(search_pattern) | 
             cls.display_name.like(search_pattern) |
             cls.description.like(search_pattern)),
            cls.is_deleted == False
        ).all()


class Note(BaseModel):
    """笔记模型"""
    
    __tablename__ = 'notes'
    
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=True)
    content_type = Column(String(20), default='text')  # text, markdown, html
    
    # 分类和标签
    category_id = Column(String(36), ForeignKey('note_categories.id'), nullable=True)
    tags = Column(String(500), nullable=True)  # 逗号分隔
    
    # 状态
    is_pinned = Column(Boolean, default=False)
    is_archived = Column(Boolean, default=False)
    is_encrypted = Column(Boolean, default=False)
    
    # 元数据
    word_count = Column(Integer, default=0)
    reading_time = Column(Integer, default=0)  # 预估阅读时间（分钟）
    
    # 关联关系
    category = relationship("NoteCategory", back_populates="notes")
    attachments = relationship("NoteAttachment", back_populates="note")
    
    # 索引
    __table_args__ = (
        Index('idx_notes_title', 'title'),
        Index('idx_notes_category', 'category_id'),
        Index('idx_notes_pinned', 'is_pinned'),
        Index('idx_notes_archived', 'is_archived'),
        Index('idx_notes_created', 'created_at'),
    )
    
    @property
    def tag_list(self) -> List[str]:
        """获取标签列表"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
    
    @tag_list.setter
    def tag_list(self, tags: List[str]):
        """设置标签列表"""
        self.tags = ','.join(tags) if tags else None
    
    def update_word_count(self):
        """更新字数统计"""
        if self.content:
            # 简单的字数统计
            self.word_count = len(self.content.split())
            # 预估阅读时间（按每分钟200字计算）
            self.reading_time = max(1, self.word_count // 200)
        else:
            self.word_count = 0
            self.reading_time = 0
    
    @classmethod
    def get_pinned_notes(cls, session: Session) -> List['Note']:
        """获取置顶笔记"""
        return session.query(cls).filter(
            cls.is_pinned == True,
            cls.is_archived == False,
            cls.is_deleted == False
        ).order_by(cls.updated_at.desc()).all()
    
    @classmethod
    def get_by_category(cls, session: Session, category_id: str) -> List['Note']:
        """根据分类获取笔记"""
        return session.query(cls).filter(
            cls.category_id == category_id,
            cls.is_archived == False,
            cls.is_deleted == False
        ).order_by(cls.updated_at.desc()).all()
    
    @classmethod
    def search_notes(cls, session: Session, query: str) -> List['Note']:
        """搜索笔记"""
        search_pattern = f"%{query}%"
        return session.query(cls).filter(
            (cls.title.like(search_pattern) | cls.content.like(search_pattern)),
            cls.is_archived == False,
            cls.is_deleted == False
        ).order_by(cls.updated_at.desc()).all()


class NoteCategory(BaseModel):
    """笔记分类模型"""
    
    __tablename__ = 'note_categories'
    
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    color = Column(String(7), nullable=True)  # 十六进制颜色值
    icon = Column(String(50), nullable=True)
    sort_order = Column(Integer, default=0)
    
    # 关联关系
    notes = relationship("Note", back_populates="category")
    
    @classmethod
    def get_by_name(cls, session: Session, name: str) -> Optional['NoteCategory']:
        """根据名称获取分类"""
        return session.query(cls).filter(
            cls.name == name,
            cls.is_deleted == False
        ).first()
    
    @classmethod
    def get_sorted_categories(cls, session: Session) -> List['NoteCategory']:
        """获取排序后的分类列表"""
        return session.query(cls).filter(
            cls.is_deleted == False
        ).order_by(cls.sort_order, cls.name).all()


class NoteAttachment(BaseModel):
    """笔记附件模型"""
    
    __tablename__ = 'note_attachments'
    
    note_id = Column(String(36), ForeignKey('notes.id'), nullable=False)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(Text, nullable=False)
    file_size = Column(Integer, nullable=True)
    mime_type = Column(String(100), nullable=True)
    
    # 关联关系
    note = relationship("Note", back_populates="attachments")
    
    # 索引
    __table_args__ = (
        Index('idx_note_attachments_note', 'note_id'),
        Index('idx_note_attachments_filename', 'filename'),
    )


class PasswordEntry(BaseModel):
    """密码条目模型"""
    
    __tablename__ = 'password_entries'
    
    title = Column(String(255), nullable=False)
    username = Column(String(255), nullable=True)
    email = Column(String(255), nullable=True)
    password_encrypted = Column(Text, nullable=False)  # 加密后的密码
    url = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # 分类和标签
    category = Column(String(100), nullable=True)
    tags = Column(String(500), nullable=True)  # 逗号分隔
    
    # 安全设置
    is_favorite = Column(Boolean, default=False)
    password_strength = Column(Integer, nullable=True)  # 密码强度评分 0-100
    last_password_change = Column(DateTime, nullable=True)
    password_expires_at = Column(DateTime, nullable=True)
    
    # 使用统计
    access_count = Column(Integer, default=0)
    last_accessed = Column(DateTime, nullable=True)
    
    # 索引
    __table_args__ = (
        Index('idx_password_entries_title', 'title'),
        Index('idx_password_entries_category', 'category'),
        Index('idx_password_entries_favorite', 'is_favorite'),
        Index('idx_password_entries_url', 'url'),
    )
    
    @property
    def tag_list(self) -> List[str]:
        """获取标签列表"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
    
    @tag_list.setter
    def tag_list(self, tags: List[str]):
        """设置标签列表"""
        self.tags = ','.join(tags) if tags else None
    
    @property
    def is_password_expired(self) -> bool:
        """检查密码是否过期"""
        if not self.password_expires_at:
            return False
        return datetime.utcnow() > self.password_expires_at
    
    def record_access(self):
        """记录访问"""
        self.access_count += 1
        self.last_accessed = datetime.utcnow()
    
    def update_password_change(self):
        """更新密码修改时间"""
        self.last_password_change = datetime.utcnow()
    
    @classmethod
    def get_favorites(cls, session: Session) -> List['PasswordEntry']:
        """获取收藏的密码条目"""
        return session.query(cls).filter(
            cls.is_favorite == True,
            cls.is_deleted == False
        ).order_by(cls.title).all()
    
    @classmethod
    def get_by_category(cls, session: Session, category: str) -> List['PasswordEntry']:
        """根据分类获取密码条目"""
        return session.query(cls).filter(
            cls.category == category,
            cls.is_deleted == False
        ).order_by(cls.title).all()
    
    @classmethod
    def get_expired_passwords(cls, session: Session) -> List['PasswordEntry']:
        """获取过期的密码条目"""
        return session.query(cls).filter(
            cls.password_expires_at < datetime.utcnow(),
            cls.is_deleted == False
        ).all()
    
    @classmethod
    def search_entries(cls, session: Session, query: str) -> List['PasswordEntry']:
        """搜索密码条目"""
        search_pattern = f"%{query}%"
        return session.query(cls).filter(
            (cls.title.like(search_pattern) | 
             cls.username.like(search_pattern) |
             cls.email.like(search_pattern) |
             cls.url.like(search_pattern)),
            cls.is_deleted == False
        ).order_by(cls.title).all()
