"""
带调试信息的关闭状态同步测试
"""

import sys
from pathlib import Path
import time

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件和控制器
from modules.study_tracker.study_floating_widget import StudyFloatingWidget
from modules.study_tracker.study_floating_widget_controller import StudyFloatingWidgetController


def test_debug_close_sync():
    """带调试信息的关闭状态同步测试"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        print("\n🔍 带调试信息的关闭状态同步测试")
        print("=" * 60)
        
        # 1. 创建控制器
        print("\n1. 创建控制器...")
        controller = StudyFloatingWidgetController()
        
        # 记录状态变化
        status_changes = []
        
        def on_status_changed(is_running):
            status_text = "运行中" if is_running else "已停止"
            status_changes.append((time.time(), status_text))
            print(f"   📡 [SIGNAL] 收到控制器状态变化信号: {status_text}")
        
        controller.status_changed.connect(on_status_changed)
        print("✅ 控制器已创建，信号已连接")
        
        # 2. 创建悬浮小组件
        print("\n2. 创建悬浮小组件...")
        widget = StudyFloatingWidget()
        widget.show()
        widget.raise_()
        widget.activateWindow()
        print("✅ 悬浮小组件已创建并显示")
        
        # 3. 等待一段时间让系统稳定
        print("\n3. 等待系统稳定...")
        for i in range(3):
            time.sleep(1)
            print(f"   等待第{i+1}秒...")
        
        # 4. 手动触发一次状态检查
        print("\n4. 手动触发状态检查...")
        controller.check_status()
        
        # 5. 关闭悬浮小组件
        print("\n5. 关闭悬浮小组件...")
        print("   即将调用 widget.close()...")
        widget.close()
        print("   widget.close() 调用完成")
        
        # 6. 等待状态同步
        print("\n6. 等待状态同步...")
        for i in range(10):
            time.sleep(1)
            print(f"\n   --- 第{i+1}秒检查 ---")
            
            # 手动触发状态检查
            print("   手动触发状态检查...")
            controller.check_status()
            
            # 检查当前状态
            current_status = controller.is_running()
            print(f"   控制器当前状态: {current_status}")
            
            if not current_status:
                print("   ✅ 检测到状态已变为停止")
                break
        else:
            print("   ❌ 10秒内状态未变为停止")
        
        # 7. 检查状态文件
        print("\n7. 检查状态文件...")
        status_file = controller.widget_status_file
        print(f"   状态文件路径: {status_file}")
        if status_file.exists():
            with open(status_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            print(f"   状态文件存在，内容: {content}")
        else:
            print("   状态文件不存在（正常，应该被删除了）")
        
        # 8. 测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果:")
        
        final_status = controller.is_running()
        if not final_status:
            print("✅ 关闭状态同步测试通过")
            print("   - 悬浮小组件关闭事件正确触发")
            print("   - 状态文件正确创建和删除")
            print("   - 控制器状态正确更新")
        else:
            print("❌ 关闭状态同步测试失败")
            print("   - 控制器状态未正确更新")
        
        print(f"\n状态变化记录: {len(status_changes)} 次")
        for timestamp, status in status_changes:
            print(f"   {time.strftime('%H:%M:%S', time.localtime(timestamp))}: {status}")
        
        if len(status_changes) == 0:
            print("❌ 没有收到任何状态变化信号")
            print("   可能的问题:")
            print("   1. 信号连接失败")
            print("   2. _update_status() 方法未被调用")
            print("   3. 状态文件机制未工作")
        
        app.quit()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        app.quit()


if __name__ == "__main__":
    print("🔍 开始带调试信息的关闭状态同步测试")
    print("请观察详细的调试输出...")
    test_debug_close_sync()
