"""
加密解密工具类

提供数据加密和解密功能，包括：
- AES对称加密
- 密钥派生
- 密码哈希
- 随机数生成
- 数字签名
"""

import os
import hashlib
import secrets
import base64
from typing import Optional, Tuple, Union
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend


class CryptoUtils:
    """加密解密工具类"""
    
    # 默认配置
    DEFAULT_KEY_LENGTH = 32  # 256位
    DEFAULT_IV_LENGTH = 16   # 128位
    DEFAULT_SALT_LENGTH = 16 # 128位
    DEFAULT_ITERATIONS = 100000
    
    @staticmethod
    def generate_random_bytes(length: int) -> bytes:
        """
        生成随机字节
        
        Args:
            length: 字节长度
            
        Returns:
            随机字节
        """
        return secrets.token_bytes(length)
    
    @staticmethod
    def generate_random_string(length: int) -> str:
        """
        生成随机字符串
        
        Args:
            length: 字符串长度
            
        Returns:
            随机字符串
        """
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def generate_salt(length: int = DEFAULT_SALT_LENGTH) -> bytes:
        """
        生成盐值
        
        Args:
            length: 盐值长度
            
        Returns:
            盐值
        """
        return CryptoUtils.generate_random_bytes(length)
    
    @staticmethod
    def derive_key(password: str, salt: bytes, 
                   length: int = DEFAULT_KEY_LENGTH,
                   iterations: int = DEFAULT_ITERATIONS) -> bytes:
        """
        从密码派生密钥
        
        Args:
            password: 密码
            salt: 盐值
            length: 密钥长度
            iterations: 迭代次数
            
        Returns:
            派生的密钥
        """
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=length,
            salt=salt,
            iterations=iterations,
            backend=default_backend()
        )
        return kdf.derive(password.encode('utf-8'))
    
    @staticmethod
    def hash_password(password: str, salt: Optional[bytes] = None) -> Tuple[str, str]:
        """
        哈希密码
        
        Args:
            password: 密码
            salt: 盐值（可选，如果不提供会自动生成）
            
        Returns:
            (哈希值, 盐值) 的元组
        """
        if salt is None:
            salt = CryptoUtils.generate_salt()
        
        # 使用PBKDF2进行密码哈希
        key = CryptoUtils.derive_key(password, salt)
        
        # 返回base64编码的哈希值和盐值
        hash_b64 = base64.b64encode(key).decode('utf-8')
        salt_b64 = base64.b64encode(salt).decode('utf-8')
        
        return hash_b64, salt_b64
    
    @staticmethod
    def verify_password(password: str, hash_b64: str, salt_b64: str) -> bool:
        """
        验证密码
        
        Args:
            password: 待验证的密码
            hash_b64: Base64编码的哈希值
            salt_b64: Base64编码的盐值
            
        Returns:
            密码是否正确
        """
        try:
            salt = base64.b64decode(salt_b64)
            expected_hash = base64.b64decode(hash_b64)
            
            # 使用相同的参数派生密钥
            derived_key = CryptoUtils.derive_key(password, salt, len(expected_hash))
            
            # 使用安全的比较方法
            return secrets.compare_digest(derived_key, expected_hash)
        except Exception:
            return False
    
    @staticmethod
    def encrypt_aes(data: Union[str, bytes], key: bytes, 
                    iv: Optional[bytes] = None) -> Tuple[bytes, bytes]:
        """
        AES加密
        
        Args:
            data: 待加密的数据
            key: 加密密钥
            iv: 初始化向量（可选）
            
        Returns:
            (加密后的数据, 初始化向量)
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        if iv is None:
            iv = CryptoUtils.generate_random_bytes(CryptoUtils.DEFAULT_IV_LENGTH)
        
        # 创建AES加密器
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        
        # PKCS7填充
        padding_length = 16 - (len(data) % 16)
        padded_data = data + bytes([padding_length] * padding_length)
        
        # 加密
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
        
        return encrypted_data, iv
    
    @staticmethod
    def decrypt_aes(encrypted_data: bytes, key: bytes, iv: bytes) -> Optional[bytes]:
        """
        AES解密
        
        Args:
            encrypted_data: 加密的数据
            key: 解密密钥
            iv: 初始化向量
            
        Returns:
            解密后的数据，解密失败返回None
        """
        try:
            # 创建AES解密器
            cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
            decryptor = cipher.decryptor()
            
            # 解密
            padded_data = decryptor.update(encrypted_data) + decryptor.finalize()
            
            # 移除PKCS7填充
            padding_length = padded_data[-1]
            data = padded_data[:-padding_length]
            
            return data
        except Exception:
            return None
    
    @staticmethod
    def encrypt_text(text: str, password: str) -> str:
        """
        加密文本（高级接口）
        
        Args:
            text: 待加密的文本
            password: 密码
            
        Returns:
            Base64编码的加密结果
        """
        # 生成盐值和IV
        salt = CryptoUtils.generate_salt()
        iv = CryptoUtils.generate_random_bytes(CryptoUtils.DEFAULT_IV_LENGTH)
        
        # 从密码派生密钥
        key = CryptoUtils.derive_key(password, salt)
        
        # 加密
        encrypted_data, _ = CryptoUtils.encrypt_aes(text, key, iv)
        
        # 组合盐值、IV和加密数据
        combined = salt + iv + encrypted_data
        
        # 返回Base64编码的结果
        return base64.b64encode(combined).decode('utf-8')
    
    @staticmethod
    def decrypt_text(encrypted_text: str, password: str) -> Optional[str]:
        """
        解密文本（高级接口）
        
        Args:
            encrypted_text: Base64编码的加密文本
            password: 密码
            
        Returns:
            解密后的文本，解密失败返回None
        """
        try:
            # 解码Base64
            combined = base64.b64decode(encrypted_text)
            
            # 分离盐值、IV和加密数据
            salt = combined[:CryptoUtils.DEFAULT_SALT_LENGTH]
            iv = combined[CryptoUtils.DEFAULT_SALT_LENGTH:CryptoUtils.DEFAULT_SALT_LENGTH + CryptoUtils.DEFAULT_IV_LENGTH]
            encrypted_data = combined[CryptoUtils.DEFAULT_SALT_LENGTH + CryptoUtils.DEFAULT_IV_LENGTH:]
            
            # 从密码派生密钥
            key = CryptoUtils.derive_key(password, salt)
            
            # 解密
            decrypted_data = CryptoUtils.decrypt_aes(encrypted_data, key, iv)
            
            if decrypted_data:
                return decrypted_data.decode('utf-8')
            else:
                return None
        except Exception:
            return None
    
    @staticmethod
    def generate_rsa_keypair(key_size: int = 2048) -> Tuple[bytes, bytes]:
        """
        生成RSA密钥对
        
        Args:
            key_size: 密钥长度
            
        Returns:
            (私钥, 公钥) 的元组
        """
        # 生成私钥
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=key_size,
            backend=default_backend()
        )
        
        # 获取公钥
        public_key = private_key.public_key()
        
        # 序列化密钥
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        return private_pem, public_pem
    
    @staticmethod
    def rsa_encrypt(data: Union[str, bytes], public_key_pem: bytes) -> Optional[bytes]:
        """
        RSA加密
        
        Args:
            data: 待加密的数据
            public_key_pem: PEM格式的公钥
            
        Returns:
            加密后的数据，加密失败返回None
        """
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # 加载公钥
            public_key = serialization.load_pem_public_key(public_key_pem, backend=default_backend())
            
            # 加密
            encrypted_data = public_key.encrypt(
                data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            return encrypted_data
        except Exception:
            return None
    
    @staticmethod
    def rsa_decrypt(encrypted_data: bytes, private_key_pem: bytes) -> Optional[bytes]:
        """
        RSA解密
        
        Args:
            encrypted_data: 加密的数据
            private_key_pem: PEM格式的私钥
            
        Returns:
            解密后的数据，解密失败返回None
        """
        try:
            # 加载私钥
            private_key = serialization.load_pem_private_key(
                private_key_pem, 
                password=None, 
                backend=default_backend()
            )
            
            # 解密
            decrypted_data = private_key.decrypt(
                encrypted_data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            return decrypted_data
        except Exception:
            return None
    
    @staticmethod
    def calculate_file_hash(file_path: str, algorithm: str = 'sha256') -> Optional[str]:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法
            
        Returns:
            文件哈希值，计算失败返回None
        """
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception:
            return None
    
    @staticmethod
    def secure_delete_file(file_path: str, passes: int = 3) -> bool:
        """
        安全删除文件（多次覆写）
        
        Args:
            file_path: 文件路径
            passes: 覆写次数
            
        Returns:
            删除是否成功
        """
        try:
            if not os.path.exists(file_path):
                return False
            
            file_size = os.path.getsize(file_path)
            
            with open(file_path, 'r+b') as f:
                for _ in range(passes):
                    # 随机数据覆写
                    f.seek(0)
                    f.write(os.urandom(file_size))
                    f.flush()
                    os.fsync(f.fileno())
            
            # 删除文件
            os.remove(file_path)
            return True
        except Exception:
            return False
