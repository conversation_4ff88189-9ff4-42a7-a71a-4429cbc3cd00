#!/usr/bin/env python3
"""
测试单元格选择功能的脚本
"""

import sys
import os
from datetime import date, timedelta

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt6.QtCore import QTimer
from src.modules.task_manager.weekly_task_table import WeeklyTaskTable

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试单元格选择功能")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        test_button = QPushButton("模拟点击完成按钮")
        test_button.clicked.connect(self.test_cell_selection)
        layout.addWidget(test_button)
        
        # 创建周任务表格
        self.weekly_table = WeeklyTaskTable()
        layout.addWidget(self.weekly_table)
        
        # 连接信号
        self.weekly_table.cell_selection_requested.connect(self.on_cell_selection_requested)
        
        # 添加一些测试数据
        self.add_test_data()
    
    def add_test_data(self):
        """添加测试数据"""
        today = date.today()
        days_since_monday = today.weekday()
        week_start = today - timedelta(days=days_since_monday)
        
        # 模拟一些任务数据
        test_tasks = [
            {
                'id': 'test_task_1',
                'title': '测试任务1',
                'category': '上午',
                'date': week_start,
                'status': '待处理',
                'priority': '高'
            },
            {
                'id': 'test_task_2', 
                'title': '测试任务2',
                'category': '周计划',
                'status': '待处理',
                'priority': '普通'
            }
        ]
        
        # 更新表格数据
        self.weekly_table.tasks_data = {
            week_start: {
                '上午': [test_tasks[0]]
            }
        }
        self.weekly_table.weekly_plan_tasks_by_week = {
            week_start.strftime('%Y-%m-%d'): [test_tasks[1]]
        }
        
        # 重新填充表格
        self.weekly_table.populate_table()
    
    def test_cell_selection(self):
        """测试单元格选择功能"""
        # 模拟点击完成按钮
        print("模拟点击完成按钮...")
        
        # 发射单元格选择请求信号
        self.weekly_table.on_cell_selection_requested('test_task_1', '上午')
        
        print("单元格选择请求已发送")
    
    def on_cell_selection_requested(self, task_id: str, category: str):
        """处理单元格选择请求"""
        print(f"收到单元格选择请求: 任务ID={task_id}, 类别={category}")

def main():
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
