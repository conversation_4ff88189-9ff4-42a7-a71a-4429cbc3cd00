2025-07-07 16:14:49,678 - src.core.logger - [32mINFO[0m - Logging initialized - Level: INFO
2025-07-07 16:14:49,678 - src.core.logger - [32mINFO[0m - Log file: C:\sxjm\personal_manager_project\source_code\autostart_manager.log
2025-07-07 16:14:49,679 - enhanced_autostart_manager - [32mINFO[0m - 启用自启动，方式: registry, 强制: True
2025-07-07 16:14:49,679 - enhanced_autostart_manager - [32mIN<PERSON>O[0m - 已清理注册表自启动项
2025-07-07 16:14:53,306 - enhanced_autostart_manager - [32mIN<PERSON>O[0m - 已清理任务计划程序项
2025-07-07 16:14:53,309 - enhanced_autostart_manager - [32mINFO[0m - 自启动已启用: registry
2025-07-07 16:14:53,309 - modules.task_manager.desktop_widget_controller.DesktopWidgetController - [32mIN<PERSON><PERSON>[0m - 自启动设置成功: 启用
2025-07-07 16:15:04,035 - __main__ - [32mINFO[0m - Cleaning up application resources...
2025-07-07 16:15:04,036 - core.database.DatabaseManager - [32mINFO[0m - Database connection closed
2025-07-07 16:15:04,036 - __main__ - [32mINFO[0m - Database connections closed
2025-07-07 16:15:04,037 - __main__ - [32mINFO[0m - Configuration saved
2025-07-07 16:15:04,037 - gui.main_window.MainWindow - [32mINFO[0m - Main window closed
2025-07-07 16:15:04,058 - __main__ - [32mINFO[0m - Application exited with code: 0
