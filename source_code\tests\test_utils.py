"""
工具类测试模块

测试各种工具类的基本功能
"""

import os
import sys
import tempfile
from datetime import datetime, timedelta

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils import FileUtils, SystemUtils, DateTimeUtils, StringUtils, CryptoUtils


def test_file_utils():
    """测试文件工具类"""
    print("=== 测试 FileUtils ===")
    
    # 测试文件大小格式化
    print(f"1024 bytes = {FileUtils.format_file_size(1024)}")
    print(f"1048576 bytes = {FileUtils.format_file_size(1048576)}")
    
    # 测试创建临时文件并获取信息
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write("Hello, World!")
        temp_file = f.name
    
    try:
        file_info = FileUtils.get_file_info(temp_file)
        if file_info:
            print(f"文件信息: {file_info['name']}, 大小: {file_info['size_formatted']}")
        
        # 测试文件哈希
        file_hash = FileUtils.get_file_hash(temp_file)
        print(f"文件MD5: {file_hash}")
        
        # 测试文件类型检测
        ext, mime_type = FileUtils.get_file_type(temp_file)
        print(f"文件类型: {ext}, MIME: {mime_type}")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)
    
    print()


def test_system_utils():
    """测试系统工具类"""
    print("=== 测试 SystemUtils ===")
    
    # 测试系统信息
    system_info = SystemUtils.get_system_info()
    print(f"操作系统: {system_info.get('system', 'Unknown')}")
    print(f"计算机名: {system_info.get('node_name', 'Unknown')}")
    
    # 测试CPU信息
    cpu_info = SystemUtils.get_cpu_info()
    print(f"CPU核心数: {cpu_info.get('physical_cores', 'Unknown')}")
    print(f"CPU使用率: {cpu_info.get('usage_percent', 'Unknown')}%")
    
    # 测试内存信息
    memory_info = SystemUtils.get_memory_info()
    if memory_info:
        total_gb = memory_info.get('total', 0) / (1024**3)
        used_percent = memory_info.get('percent', 0)
        print(f"内存总量: {total_gb:.1f} GB")
        print(f"内存使用率: {used_percent}%")
    
    print()


def test_datetime_utils():
    """测试日期时间工具类"""
    print("=== 测试 DateTimeUtils ===")
    
    # 测试当前时间
    now = DateTimeUtils.now()
    print(f"当前时间: {DateTimeUtils.format_datetime(now)}")
    print(f"可读格式: {DateTimeUtils.format_datetime(now, DateTimeUtils.FORMAT_DATETIME_READABLE)}")
    
    # 测试时间解析
    date_str = "2024-01-01 12:00:00"
    parsed_date = DateTimeUtils.parse_datetime(date_str)
    if parsed_date:
        print(f"解析日期: {parsed_date}")
        print(f"相对时间: {DateTimeUtils.time_ago(parsed_date)}")
    
    # 测试时间差计算
    yesterday = now - timedelta(days=1)
    diff = DateTimeUtils.time_diff(now, yesterday)
    print(f"时间差: {DateTimeUtils.format_time_diff(diff)}")
    
    # 测试周范围
    week_start, week_end = DateTimeUtils.get_week_range(now)
    print(f"本周范围: {DateTimeUtils.format_datetime(week_start, DateTimeUtils.FORMAT_DATE)} 到 {DateTimeUtils.format_datetime(week_end, DateTimeUtils.FORMAT_DATE)}")
    
    print()


def test_string_utils():
    """测试字符串工具类"""
    print("=== 测试 StringUtils ===")
    
    # 测试邮箱验证
    email = "<EMAIL>"
    print(f"邮箱 {email} 有效: {StringUtils.is_email(email)}")
    
    # 测试手机号验证
    phone = "13812345678"
    print(f"手机号 {phone} 有效: {StringUtils.is_phone(phone)}")
    
    # 测试文本清理
    messy_text = "  Hello   World  \n\n  "
    clean_text = StringUtils.clean_text(messy_text)
    print(f"清理前: '{messy_text}'")
    print(f"清理后: '{clean_text}'")
    
    # 测试字符串相似度
    text1 = "Hello World"
    text2 = "Hello Word"
    similarity = StringUtils.similarity(text1, text2)
    print(f"'{text1}' 和 '{text2}' 相似度: {similarity:.2f}")
    
    # 测试命名转换
    camel_case = "helloWorldTest"
    snake_case = StringUtils.camel_to_snake(camel_case)
    print(f"驼峰转下划线: {camel_case} -> {snake_case}")
    
    back_to_camel = StringUtils.snake_to_camel(snake_case)
    print(f"下划线转驼峰: {snake_case} -> {back_to_camel}")
    
    # 测试敏感信息遮蔽
    sensitive_text = "我的邮箱是 <EMAIL>，手机号是 13812345678"
    masked_text = StringUtils.mask_sensitive_info(sensitive_text)
    print(f"遮蔽前: {sensitive_text}")
    print(f"遮蔽后: {masked_text}")
    
    print()


def test_crypto_utils():
    """测试加密工具类"""
    print("=== 测试 CryptoUtils ===")
    
    # 测试密码哈希
    password = "my_secret_password"
    hash_value, salt = CryptoUtils.hash_password(password)
    print(f"密码哈希成功，哈希长度: {len(hash_value)}")
    
    # 测试密码验证
    is_valid = CryptoUtils.verify_password(password, hash_value, salt)
    print(f"密码验证: {is_valid}")
    
    wrong_password = "wrong_password"
    is_invalid = CryptoUtils.verify_password(wrong_password, hash_value, salt)
    print(f"错误密码验证: {is_invalid}")
    
    # 测试文本加密解密
    original_text = "这是一个秘密消息"
    encryption_password = "encryption_key"
    
    encrypted_text = CryptoUtils.encrypt_text(original_text, encryption_password)
    print(f"加密成功，密文长度: {len(encrypted_text)}")
    
    decrypted_text = CryptoUtils.decrypt_text(encrypted_text, encryption_password)
    print(f"解密结果: {decrypted_text}")
    print(f"解密成功: {original_text == decrypted_text}")
    
    # 测试随机字符串生成
    random_string = CryptoUtils.generate_random_string(16)
    print(f"随机字符串: {random_string}")
    
    print()


def main():
    """主测试函数"""
    print("Personal Manager System - 工具类测试")
    print("=" * 50)
    
    try:
        test_file_utils()
        test_system_utils()
        test_datetime_utils()
        test_string_utils()
        test_crypto_utils()
        
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
