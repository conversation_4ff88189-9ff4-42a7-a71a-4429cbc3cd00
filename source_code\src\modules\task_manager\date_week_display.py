"""
Date and Week Display Component for Task Manager

This component displays current date and week information.
"""

from datetime import datetime, date
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
)
from PyQt6.QtCore import Qt, QTimer, QDate
from PyQt6.QtGui import <PERSON>Font, QPalette

from core.logger import LoggerMixin


class DateWeekDisplay(QWidget, LoggerMixin):
    """Widget to display current date and week information"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.setup_timer()
        self.update_display()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # Create main frame
        self.main_frame = QFrame()
        self.main_frame.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Raised)
        self.main_frame.setLineWidth(1)
        layout.addWidget(self.main_frame)
        
        # Frame layout
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(15, 10, 15, 10)
        frame_layout.setSpacing(8)
        
        # Date display
        self.date_label = QLabel()
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        date_font = QFont()
        date_font.setPointSize(16)
        date_font.setBold(True)
        self.date_label.setFont(date_font)
        frame_layout.addWidget(self.date_label)
        
        # Week information layout
        week_layout = QHBoxLayout()
        week_layout.setSpacing(20)
        
        # Week number
        self.week_label = QLabel()
        self.week_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        week_font = QFont()
        week_font.setPointSize(12)
        week_font.setBold(True)
        self.week_label.setFont(week_font)
        week_layout.addWidget(self.week_label)
        
        # Day of week
        self.day_label = QLabel()
        self.day_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        day_font = QFont()
        day_font.setPointSize(12)
        self.day_label.setFont(day_font)
        week_layout.addWidget(self.day_label)
        
        frame_layout.addLayout(week_layout)
        
        # Additional info layout
        info_layout = QHBoxLayout()
        info_layout.setSpacing(15)
        
        # Day of year
        self.day_of_year_label = QLabel()
        self.day_of_year_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_font = QFont()
        info_font.setPointSize(10)
        self.day_of_year_label.setFont(info_font)
        info_layout.addWidget(self.day_of_year_label)
        
        # Days remaining in year
        self.days_remaining_label = QLabel()
        self.days_remaining_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.days_remaining_label.setFont(info_font)
        info_layout.addWidget(self.days_remaining_label)
        
        frame_layout.addLayout(info_layout)
        
        # Set style
        self.set_style()
    
    def set_style(self):
        """Set widget styling"""
        self.setStyleSheet("""
            QFrame {
                background-color: #f0f0f0;
                border: 2px solid #d0d0d0;
                border-radius: 8px;
            }
            QLabel {
                color: #333333;
                background-color: transparent;
                border: none;
            }
        """)
    
    def setup_timer(self):
        """Setup timer to update display every minute"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_display)
        self.timer.start(60000)  # Update every minute
    
    def update_display(self):
        """Update the date and week display"""
        try:
            current_date = datetime.now()
            qdate = QDate.currentDate()
            
            # Format date
            date_str = current_date.strftime("%Y年%m月%d日")
            self.date_label.setText(date_str)
            
            # Get week number (ISO week)
            year, week_num, weekday = current_date.isocalendar()
            week_str = f"第 {week_num} 周"
            self.week_label.setText(week_str)
            
            # Get day of week in Chinese
            weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
            day_str = weekdays[weekday - 1]
            self.day_label.setText(day_str)
            
            # Day of year
            day_of_year = current_date.timetuple().tm_yday
            self.day_of_year_label.setText(f"今年第 {day_of_year} 天")
            
            # Days remaining in year
            year_end = datetime(current_date.year, 12, 31)
            days_remaining = (year_end - current_date).days + 1
            self.days_remaining_label.setText(f"剩余 {days_remaining} 天")
            
            self.logger.debug("Date and week display updated")
            
        except Exception as e:
            self.logger.error(f"Error updating date display: {e}")
    
    def get_current_week_info(self):
        """Get current week information as dictionary"""
        current_date = datetime.now()
        year, week_num, weekday = current_date.isocalendar()
        
        return {
            'year': year,
            'week_number': week_num,
            'weekday': weekday,
            'date': current_date.date(),
            'day_of_year': current_date.timetuple().tm_yday
        }
    
    def get_week_date_range(self, week_offset=0):
        """Get date range for current week or offset week
        
        Args:
            week_offset: Number of weeks to offset (0 = current week)
            
        Returns:
            Tuple of (start_date, end_date) for the week
        """
        current_date = datetime.now()
        
        # Calculate the start of the current week (Monday)
        days_since_monday = current_date.weekday()
        week_start = current_date - timedelta(days=days_since_monday)
        
        # Apply week offset
        week_start += timedelta(weeks=week_offset)
        week_end = week_start + timedelta(days=6)
        
        return week_start.date(), week_end.date()
