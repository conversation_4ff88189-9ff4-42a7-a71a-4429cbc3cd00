# 学习时间追踪模块

## 概述

学习时间追踪模块提供完整的学习时间记录和统计功能，采用简化的界面设计，将计时器功能集中在悬浮桌面小组件中。

## 功能特性

### 主界面 (StudyTrackerWidget)

**简化设计**：
- 移除了复杂的左侧控制面板
- 专注于数据展示和分析功能
- 提供清晰的学习记录查看和统计分析

**主要功能**：
- 📊 **学习记录标签页**：查看历史学习记录，支持日期筛选和数据导出
- 📈 **统计分析标签页**：提供日/周/月学习统计，包含图表展示
- 🔍 **数据筛选**：按开始日期和结束日期筛选学习记录
- 📤 **数据导出**：支持CSV格式导出学习数据

### 悬浮桌面小组件 (StudyFloatingWidget)

**完整的计时器功能**：
- ⏱️ **实时计时**：显示当前学习会话的时间
- ▶️ **开始/停止**：快捷的学习会话控制
- 📱 **状态显示**：当前学习状态和今日累计时间
- 🎯 **悬浮显示**：可拖拽的桌面悬浮窗口
- 🔄 **系统托盘**：最小化到系统托盘，不干扰工作

**交互功能**：
- 双击悬浮窗打开主界面
- 拖拽移动窗口位置
- 折叠/展开窗口内容
- 系统托盘右键菜单

### 统计图表组件 (StudyStatsWidget)

**多维度统计**：
- 📅 **今日统计**：当日学习概览和科目分布
- 📊 **本周统计**：周学习趋势和每日对比
- 📈 **本月统计**：月度学习进度和目标达成
- 🔍 **趋势分析**：长期学习习惯分析

**图表支持**：
- 饼图：科目学习时间分布
- 趋势图：每日学习时长变化
- 兼容模式：无matplotlib时使用文本显示

## 使用方法

### 1. 启动学习追踪

1. 在主应用中点击左侧导航的"学习时间追踪"
2. 查看学习记录和统计数据
3. 可选择显示悬浮小组件进行计时

### 2. 使用悬浮小组件

1. 从主界面或系统托盘启动悬浮小组件
2. 点击"开始"按钮开始学习计时
3. 点击"停止"按钮结束学习会话
4. 双击悬浮窗返回主界面查看详细数据

### 3. 查看学习数据

1. **学习记录**：查看所有学习会话的详细记录
2. **统计分析**：查看各种维度的学习统计
3. **数据导出**：将学习记录导出为CSV文件

## 技术架构

### 数据模型
- `StudySubject`：学习科目
- `StudySession`：学习会话记录
- `StudyGoal`：学习目标
- `StudyBreak`：学习休息记录

### 服务层
- `StudyService`：核心业务逻辑，提供完整的学习时间管理功能

### 界面组件
- `StudyTrackerWidget`：简化的主界面，专注数据展示
- `StudyFloatingWidget`：功能完整的悬浮计时器
- `StudyStatsWidget`：统计图表展示组件
- `SubjectDialog`：学习科目管理对话框

## 设计理念

### 简化主界面
- 移除复杂的控制组件，专注于数据查看和分析
- 提供清晰的学习记录浏览体验
- 保持界面整洁和功能聚焦

### 悬浮小组件
- 将计时器功能集中在悬浮小组件中
- 提供便捷的学习会话控制
- 不干扰主要工作流程
- 支持系统托盘集成

### 数据驱动
- 强调学习数据的记录和分析
- 提供多维度的统计视图
- 支持数据导出和备份

## 文件结构

```
src/modules/study_tracker/
├── __init__.py                 # 模块初始化
├── study_service.py           # 核心服务层
├── study_tracker_widget.py   # 简化的主界面
├── study_floating_widget.py  # 悬浮计时器组件
├── study_stats_widget.py     # 统计图表组件
├── subject_dialog.py          # 科目管理对话框
└── README.md                  # 本文档

src/models/study_models.py     # 数据模型定义
```

## 注意事项

1. **数据持久化**：所有学习记录都保存在SQLite数据库中
2. **图表依赖**：统计图表需要matplotlib库，无此库时使用文本显示
3. **系统兼容**：悬浮小组件支持Windows系统托盘功能
4. **性能优化**：定时器更新频率已优化，避免过度消耗系统资源
