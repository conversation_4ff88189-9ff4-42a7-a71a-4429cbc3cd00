#!/usr/bin/env python3
"""
Build Installer Script - 造神计划
使用PyInstaller + Inno Setup创建专业的Windows安装程序
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from typing import List, Dict


class InstallerBuilder:
    """安装程序构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.app_name = "造神计划"
        self.app_version = "1.0.0"
        self.app_description = "个人管理系统 - 造神计划"
        self.app_author = "造神计划开发团队"
        
        # 构建目录
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.installer_dir = self.project_root / "installer"
        
        print(f"初始化安装程序构建器 - {self.app_name}")
    
    def clean_build_dirs(self):
        """清理构建目录"""
        print("清理构建目录...")
        
        dirs_to_clean = [self.build_dir, self.dist_dir, self.installer_dir]
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"  删除: {dir_path.name}/")
        
        # 重新创建目录
        for dir_path in dirs_to_clean:
            dir_path.mkdir(exist_ok=True)
            print(f"  创建: {dir_path.name}/")
    
    def create_pyinstaller_spec(self):
        """创建PyInstaller规格文件"""
        print("创建PyInstaller规格文件...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 项目路径
project_root = Path(r"{self.project_root}")
src_path = project_root / "src"

# 添加源代码路径
sys.path.insert(0, str(src_path))

# 数据文件
datas = [
    (str(project_root / "src"), "src"),
    (str(project_root / "resources"), "resources"),
    (str(project_root / "config"), "config"),
    (str(project_root / "docs"), "docs"),
    (str(project_root / "scripts"), "scripts"),
    (str(project_root / "requirements.txt"), "."),
    (str(project_root / "universal_widget_daemon.py"), "."),
]

# 隐藏导入
hiddenimports = [
    'PyQt6',
    'PyQt6.QtCore',
    'PyQt6.QtWidgets',
    'PyQt6.QtGui',
    'sqlalchemy',
    'psutil',
    'cryptography',
    'keyring',
    'requests',
    'yaml',
    'watchdog',
    'send2trash',
    'apscheduler',
    'pandas',
    'openpyxl',
    'pillow',
    'tqdm',
    'click',
]

# 排除模块
excludes = [
    'matplotlib',
    'numpy',
    'scipy',
    'tkinter',
    'unittest',
    'test',
    'tests',
]

a = Analysis(
    [str(project_root / "main.py")],
    pathex=[str(project_root), str(src_path)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name="{self.app_name}",
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / "resources" / "icon.ico") if (project_root / "resources" / "icon.ico").exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name="{self.app_name}",
)
'''
        
        spec_file = self.project_root / f"{self.app_name}.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"  创建规格文件: {spec_file.name}")
        return spec_file
    
    def build_executable(self, spec_file: Path):
        """使用PyInstaller构建可执行文件"""
        print("使用PyInstaller构建可执行文件...")
        
        try:
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                str(spec_file)
            ]
            
            print(f"  执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("  PyInstaller构建成功")
                return True
            else:
                print(f"  PyInstaller构建失败: {result.stderr}")
                return False
        
        except Exception as e:
            print(f"  PyInstaller构建异常: {e}")
            return False
    
    def create_inno_setup_script(self):
        """创建Inno Setup安装脚本"""
        print("创建Inno Setup安装脚本...")
        
        app_exe_path = self.dist_dir / self.app_name / f"{self.app_name}.exe"
        
        iss_content = f'''[Setup]
AppId={{{{12345678-1234-1234-1234-123456789ABC}}}}
AppName={self.app_name}
AppVersion={self.app_version}
AppVerName={self.app_name} {self.app_version}
AppPublisher={self.app_author}
AppPublisherURL=https://github.com/your-repo
AppSupportURL=https://github.com/your-repo/issues
AppUpdatesURL=https://github.com/your-repo/releases
DefaultDirName={{autopf}}\\{self.app_name}
DefaultGroupName={self.app_name}
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir={self.installer_dir}
OutputBaseFilename={self.app_name}_Setup_v{self.app_version}
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Languages\\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{{cm:CreateDesktopIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{{cm:CreateQuickLaunchIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "autostart"; Description: "开机自动启动"; GroupDescription: "启动选项"; Flags: unchecked

[Files]
Source: "{app_exe_path}"; DestDir: "{{app}}"; Flags: ignoreversion
Source: "{self.dist_dir / self.app_name}\\*"; DestDir: "{{app}}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{{group}}\\{self.app_name}"; Filename: "{{app}}\\{self.app_name}.exe"
Name: "{{group}}\\{{cm:UninstallProgram,{self.app_name}}}"; Filename: "{{uninstallexe}}"
Name: "{{autodesktop}}\\{self.app_name}"; Filename: "{{app}}\\{self.app_name}.exe"; Tasks: desktopicon
Name: "{{userappdata}}\\Microsoft\\Internet Explorer\\Quick Launch\\{self.app_name}"; Filename: "{{app}}\\{self.app_name}.exe"; Tasks: quicklaunchicon

[Registry]
Root: HKCU; Subkey: "Software\\Microsoft\\Windows\\CurrentVersion\\Run"; ValueType: string; ValueName: "{self.app_name}"; ValueData: """{{app}}\\{self.app_name}.exe"""; Flags: uninsdeletevalue; Tasks: autostart

[Run]
Filename: "{{app}}\\{self.app_name}.exe"; Description: "{{cm:LaunchProgram,{self.app_name}}}"; Flags: nowait postinstall skipifsilent

[UninstallRun]
Filename: "{{app}}\\scripts\\stop_all_processes.bat"; WorkingDir: "{{app}}"; Flags: runhidden waituntilterminated; RunOnceId: "StopProcesses"

[Code]
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // 创建配置目录
    CreateDir(ExpandConstant('{{userappdata}}\\{self.app_name}'));
    CreateDir(ExpandConstant('{{userappdata}}\\{self.app_name}\\config'));
    CreateDir(ExpandConstant('{{userappdata}}\\{self.app_name}\\logs'));
    CreateDir(ExpandConstant('{{userappdata}}\\{self.app_name}\\data'));
  end;
end;

procedure CurUninstallStepChanged(CurUninstallStep: TUninstallStep);
begin
  if CurUninstallStep = usPostUninstall then
  begin
    // 询问是否删除用户数据
    if MsgBox('是否删除所有用户数据和配置文件？', mbConfirmation, MB_YESNO) = IDYES then
    begin
      DelTree(ExpandConstant('{{userappdata}}\\{self.app_name}'), True, True, True);
    end;
  end;
end;
'''
        
        iss_file = self.installer_dir / f"{self.app_name}_Setup.iss"
        with open(iss_file, 'w', encoding='utf-8') as f:
            f.write(iss_content)
        
        print(f"  创建安装脚本: {iss_file.name}")
        return iss_file
    
    def create_process_management_scripts(self):
        """创建进程管理脚本"""
        print("创建进程管理脚本...")
        
        # 停止所有进程的脚本
        stop_script_content = '''@echo off
echo 正在停止造神计划相关进程...

REM 停止主程序
taskkill /F /IM "造神计划.exe" 2>nul

REM 停止Python进程（包含造神计划相关的）
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq python.exe" /FO CSV ^| find "python.exe"') do (
    taskkill /F /PID %%i 2>nul
)

REM 停止守护进程
taskkill /F /IM "universal_widget_daemon.py" 2>nul

echo 所有相关进程已停止
timeout /t 2 /nobreak >nul
'''
        
        scripts_dir = self.dist_dir / self.app_name / "scripts"
        scripts_dir.mkdir(exist_ok=True)
        
        stop_script_file = scripts_dir / "stop_all_processes.bat"
        with open(stop_script_file, 'w', encoding='gbk') as f:
            f.write(stop_script_content)
        
        print(f"  创建停止脚本: {stop_script_file.name}")
    
    def build_installer(self, iss_file: Path):
        """使用Inno Setup构建安装程序"""
        print("使用Inno Setup构建安装程序...")
        
        try:
            # 查找Inno Setup编译器
            inno_paths = [
                r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
                r"C:\Program Files\Inno Setup 6\ISCC.exe",
                r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
                r"C:\Program Files\Inno Setup 5\ISCC.exe",
            ]
            
            iscc_path = None
            for path in inno_paths:
                if Path(path).exists():
                    iscc_path = path
                    break
            
            if not iscc_path:
                print("  错误: 未找到Inno Setup编译器")
                print("  请安装Inno Setup: https://jrsoftware.org/isinfo.php")
                return False
            
            cmd = [iscc_path, str(iss_file)]
            print(f"  执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("  Inno Setup构建成功")
                return True
            else:
                print(f"  Inno Setup构建失败: {result.stderr}")
                return False
        
        except Exception as e:
            print(f"  Inno Setup构建异常: {e}")
            return False
    
    def build_all(self):
        """构建完整的安装程序"""
        print(f"开始构建 {self.app_name} 安装程序")
        print("=" * 50)
        
        try:
            # 1. 清理构建目录
            self.clean_build_dirs()
            
            # 2. 创建PyInstaller规格文件
            spec_file = self.create_pyinstaller_spec()
            
            # 3. 构建可执行文件
            if not self.build_executable(spec_file):
                print("构建失败: PyInstaller构建失败")
                return False
            
            # 4. 创建进程管理脚本
            self.create_process_management_scripts()
            
            # 5. 创建Inno Setup脚本
            iss_file = self.create_inno_setup_script()
            
            # 6. 构建安装程序
            if not self.build_installer(iss_file):
                print("构建失败: Inno Setup构建失败")
                return False
            
            print("=" * 50)
            print("安装程序构建完成！")
            print(f"安装程序位置: {self.installer_dir}")
            
            # 显示生成的文件
            installer_files = list(self.installer_dir.glob("*.exe"))
            if installer_files:
                print(f"安装程序文件: {installer_files[0].name}")
            
            return True
        
        except Exception as e:
            print(f"构建过程中发生错误: {e}")
            return False


def main():
    """主函数"""
    builder = InstallerBuilder()
    
    try:
        success = builder.build_all()
        if success:
            print("\n构建成功完成！")
        else:
            print("\n构建失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n构建过程中发生未预期的错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()