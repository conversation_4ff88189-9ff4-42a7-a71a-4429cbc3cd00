# Personal Manager 桌面快捷方式设置指南

本指南将帮助您为 Personal Manager 应用程序创建桌面快捷方式，以便从 Windows 桌面直接启动。

## 快速安装

### 方法 1: 自动安装（推荐）

1. 双击运行 `install_shortcut.bat`
2. 按照屏幕提示操作
3. 完成后，您将在桌面上看到"私人经理"快捷方式

### 方法 2: 手动安装

1. 首先创建应用程序图标：
   ```bash
   python create_icon.py
   ```

2. 运行 PowerShell 脚本创建快捷方式：
   ```powershell
   powershell -ExecutionPolicy Bypass -File "create_desktop_shortcut.ps1"
   ```

## 文件说明

### 核心文件

- **`main.py`** - 应用程序主入口点
- **`start_personal_manager.vbs`** - VBScript 无窗口启动脚本（推荐）
- **`start_personal_manager.bat`** - 批处理启动脚本（调试用）
- **`start_personal_manager_silent.bat`** - 静默批处理启动脚本
- **`resources/icons/app.ico`** - 应用程序图标

### 安装脚本

- **`install_shortcut.bat`** - 一键安装脚本
- **`create_desktop_shortcut.ps1`** - PowerShell 快捷方式创建脚本
- **`create_icon.py`** - 图标生成脚本

## 启动脚本功能

### VBScript 启动器 (`start_personal_manager.vbs`) - 推荐

1. **无窗口启动** - 不显示命令行窗口，只显示应用程序界面
2. **环境变量设置** - 配置 Qt 高 DPI 支持
3. **工作目录设置** - 确保正确的模块导入路径
4. **静默运行** - 完全后台启动，用户体验最佳

### 批处理启动器 (`start_personal_manager.bat`) - 调试用

1. **环境检查** - 验证 Python 是否已安装
2. **依赖检查** - 检查并自动安装 PyQt6 等依赖
3. **错误处理** - 显示详细的错误信息
4. **调试信息** - 适合故障排除

## 快捷方式特性

创建的桌面快捷方式具有以下特性：

- **名称**: "私人经理"
- **描述**: "Personal Manager - 个人管理系统"
- **图标**: 自定义应用程序图标
- **工作目录**: 源代码文件夹
- **目标**: VBScript 无窗口启动脚本
- **启动方式**: 静默启动，无命令行窗口

## 故障排除

### 常见问题

1. **Python 未找到**
   - 确保 Python 3.8+ 已安装
   - 将 Python 添加到系统 PATH

2. **PyQt6 安装失败**
   - 手动运行: `pip install PyQt6`
   - 检查网络连接

3. **PowerShell 执行策略错误**
   - 以管理员身份运行 PowerShell
   - 执行: `Set-ExecutionPolicy RemoteSigned`

4. **图标未显示**
   - 确保 PIL/Pillow 已安装: `pip install Pillow`
   - 重新运行 `create_icon.py`

### 手动创建快捷方式

如果自动脚本失败，您可以手动创建快捷方式：

1. 右键点击桌面 → "新建" → "快捷方式"
2. 目标位置: `C:\path\to\personal_manager_project\source_code\start_personal_manager.bat`
3. 名称: "私人经理"
4. 右键快捷方式 → "属性" → 设置图标为 `resources\icons\app.ico`

## 高级配置

### 自定义启动参数

编辑 `start_personal_manager.bat` 文件，在 `python main.py` 行添加参数：

```batch
python main.py --debug --config custom_config.yaml
```

### 环境变量

您可以在批处理文件中添加更多环境变量：

```batch
set PERSONAL_MANAGER_CONFIG_DIR=C:\MyConfig
set PERSONAL_MANAGER_LOG_LEVEL=DEBUG
```

### 图标自定义

要使用自定义图标：

1. 将您的 `.ico` 文件放在 `resources/icons/` 目录
2. 编辑 `create_desktop_shortcut.ps1` 中的 `$IconFile` 路径

## 卸载

要移除快捷方式：

1. 删除桌面上的"私人经理"快捷方式
2. 删除开始菜单中的快捷方式（如果已创建）
3. 可选：删除 `resources/icons/app.ico` 和 `app.png`

## 支持

如果遇到问题，请检查：

1. Python 版本是否为 3.8+
2. 所有依赖是否已正确安装
3. 文件路径是否正确
4. Windows 用户权限是否足够

---

**注意**: 首次运行可能需要一些时间来安装依赖项。请耐心等待安装完成。
