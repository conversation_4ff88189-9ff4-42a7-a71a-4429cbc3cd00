"""
测试最终修复：学习总时长记录 + 主界面按钮功能
"""

import sys
from pathlib import Path
import time

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件
from modules.study_tracker.study_floating_widget import StudyFloatingWidget


class FinalFixesTestWindow(QMainWindow):
    """最终修复测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("最终修复测试：学习总时长 + 主界面按钮")
        self.setGeometry(100, 100, 700, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("测试最终修复功能")
        layout.addWidget(self.status_label)
        
        # 信息标签
        info_label = QLabel("""
🎯 测试目标：

1. 学习总时长记录修复：
   - 今日统计应包含当前正在进行的会话
   - 实时更新学习时长显示
   - 正确计算累计时间

2. 主界面按钮功能：
   - 参考计划小组件的"打开应用"按钮实现
   - 检查主应用是否运行
   - 激活已有窗口或启动新进程

🧪 测试步骤：

第一阶段：学习时长测试
1. 创建悬浮小组件 → 观察初始今日统计
2. 开始学习 → 观察今日统计是否实时更新
3. 学习几秒钟 → 验证时长累计是否正确

第二阶段：主界面按钮测试
4. 点击"主界面"按钮 → 应该启动或激活主应用
5. 验证主应用检测和窗口激活功能
        """)
        layout.addWidget(info_label)
        
        # 控制按钮
        create_btn = QPushButton("1. 创建悬浮小组件")
        create_btn.clicked.connect(self.create_floating_widget)
        layout.addWidget(create_btn)
        
        start_btn = QPushButton("2. 开始学习")
        start_btn.clicked.connect(self.start_study)
        layout.addWidget(start_btn)
        
        test_main_btn = QPushButton("3. 测试主界面按钮")
        test_main_btn.clicked.connect(self.test_main_button)
        layout.addWidget(test_main_btn)
        
        stop_btn = QPushButton("4. 停止学习")
        stop_btn.clicked.connect(self.stop_study)
        layout.addWidget(stop_btn)
        
        # 状态显示
        self.time_display_label = QLabel("当前时间: 00:00:00")
        layout.addWidget(self.time_display_label)
        
        self.today_stats_label = QLabel("今日统计: 等待数据...")
        layout.addWidget(self.today_stats_label)
        
        self.session_status_label = QLabel("会话状态: 无")
        layout.addWidget(self.session_status_label)
        
        # 测试结果
        self.test_result_label = QLabel("测试结果: 等待测试...")
        layout.addWidget(self.test_result_label)
        
        # 悬浮小组件实例
        self.floating_widget = None
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_monitor)
        self.monitor_timer.start(1000)  # 每秒更新
    
    def create_floating_widget(self):
        """创建悬浮小组件"""
        try:
            if self.floating_widget is None:
                self.status_label.setText("正在创建悬浮小组件...")
                self.floating_widget = StudyFloatingWidget()
                
                # 连接信号
                self.floating_widget.start_study.connect(self.on_start_signal)
                self.floating_widget.stop_study.connect(self.on_stop_signal)
            
            self.floating_widget.show()
            self.floating_widget.raise_()
            self.floating_widget.activateWindow()
            
            # 检查初始今日统计
            initial_today = self.floating_widget.today_label.text()
            self.status_label.setText(f"✅ 悬浮小组件已创建")
            self.today_stats_label.setText(f"初始今日统计: {initial_today}")
            
            print(f"✅ 悬浮小组件已创建，初始今日统计: {initial_today}")
            
        except Exception as e:
            self.status_label.setText(f"创建失败: {str(e)}")
            print(f"Error creating floating widget: {e}")
            import traceback
            traceback.print_exc()
    
    def start_study(self):
        """开始学习"""
        if self.floating_widget:
            print("🔥 开始学习...")
            self.floating_widget.start_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def test_main_button(self):
        """测试主界面按钮"""
        if self.floating_widget:
            print("🏠 测试主界面按钮...")
            self.status_label.setText("测试主界面按钮功能...")
            
            # 直接调用主界面按钮的方法
            self.floating_widget.open_main_app()
            
            self.test_result_label.setText("✅ 主界面按钮功能已测试，请检查是否启动了主应用")
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def stop_study(self):
        """停止学习"""
        if self.floating_widget:
            print("⏹️ 停止学习...")
            self.floating_widget.stop_session()
        else:
            self.status_label.setText("请先创建悬浮小组件")
    
    def update_monitor(self):
        """更新监控信息"""
        if self.floating_widget and hasattr(self.floating_widget, 'study_service'):
            try:
                # 获取时间显示
                time_display = self.floating_widget.time_label.text()
                self.time_display_label.setText(f"当前时间: {time_display}")
                
                # 获取今日统计
                today_display = self.floating_widget.today_label.text()
                self.today_stats_label.setText(f"今日统计: {today_display}")
                
                # 获取会话状态
                cache = self.floating_widget.study_service.current_session_cache
                if cache['id'] is not None:
                    status = cache['status']
                    self.session_status_label.setText(f"会话状态: {status}")
                    
                    # 检查今日统计是否实时更新
                    if status == 'active' and "0分钟" not in today_display:
                        self.test_result_label.setText("✅ 学习时长统计正常更新")
                    elif status == 'active' and "0分钟" in today_display:
                        self.test_result_label.setText("❌ 学习时长统计未更新")
                else:
                    self.session_status_label.setText("会话状态: 无")
                    
            except Exception as e:
                self.time_display_label.setText("当前时间: 错误")
                self.today_stats_label.setText("今日统计: 错误")
                self.session_status_label.setText("会话状态: 错误")
                print(f"Monitor error: {e}")
    
    def on_start_signal(self):
        """开始学习信号"""
        self.status_label.setText("✅ 学习已开始，观察今日统计是否实时更新")
        print("✅ 学习已开始，观察今日统计更新")
    
    def on_stop_signal(self):
        """停止学习信号"""
        self.status_label.setText("⏹️ 学习已停止")
        print("⏹️ 学习已停止")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.floating_widget:
            self.floating_widget.close()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        # 创建测试窗口
        window = FinalFixesTestWindow()
        window.show()
        
        print("最终修复测试程序已启动")
        print("测试重点：")
        print("1. 学习总时长记录是否实时更新")
        print("2. 主界面按钮是否正确启动主应用")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
