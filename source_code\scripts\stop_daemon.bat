@echo off
chcp 65001 >nul
title 停止桌面任务小部件守护进程

echo ========================================
echo 停止桌面任务小部件守护进程
echo ========================================
echo.

cd /d "%~dp0"

echo 检查守护进程状态...
if not exist "daemon.pid" (
    echo 未找到守护进程PID文件
    echo 守护进程可能未运行
    goto :end
)

python -c "
import psutil
import os
import signal
import time

try:
    with open('daemon.pid', 'r') as f:
        pid = int(f.read().strip())
    
    if not psutil.pid_exists(pid):
        print('守护进程不存在，清理PID文件')
        os.remove('daemon.pid')
        exit(0)
    
    print(f'找到守护进程，PID: {pid}')
    print('正在停止守护进程...')
    
    # 发送终止信号
    process = psutil.Process(pid)
    process.terminate()
    
    # 等待进程结束
    try:
        process.wait(timeout=10)
        print('守护进程已正常停止')
    except psutil.TimeoutExpired:
        print('强制终止守护进程')
        process.kill()
        process.wait(timeout=5)
    
    # 清理PID文件
    if os.path.exists('daemon.pid'):
        os.remove('daemon.pid')
    
    print('守护进程已完全停止')
    
except Exception as e:
    print(f'停止守护进程时出错: {e}')
    exit(1)
"

if errorlevel 1 (
    echo 停止守护进程失败
) else (
    echo 守护进程已成功停止
)

:end
echo.
echo 按任意键退出...
pause >nul
