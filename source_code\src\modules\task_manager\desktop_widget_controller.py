"""
桌面插件控制器

管理桌面插件的启动、停止和自启动配置
"""

import os
import sys
import json
import subprocess
import psutil
import winreg
from pathlib import Path
from typing import Optional, Dict, Any
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

from core.logger import LoggerMixin
from core.config import ConfigManager


class DesktopWidgetController(QObject, LoggerMixin):
    """桌面插件控制器"""
    
    # 信号
    status_changed = pyqtSignal(bool)  # 状态变化信号 (True=运行中, False=已停止)
    error_occurred = pyqtSignal(str)   # 错误信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config_manager = ConfigManager()
        
        # 路径配置
        self.project_root = Path(__file__).parent.parent.parent.parent.parent / "source_code"
        self.daemon_script = self.project_root / "desktop_widget_daemon.py"
        self.daemon_pid_file = self.project_root / "daemon.pid"
        self.start_script = self.project_root / "start_desktop_widget.py"
        self.widget_status_file = self.project_root.parent / "widget_status.txt"
        
        # 状态监控定时器 - 快速响应状态变化
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.check_status)
        self.status_timer.start(2000)  # 每2秒检查一次状态，快速响应用户操作
        
        # 当前状态
        self._is_running = False
        self.daemon_process = None  # 守护进程对象
        
    def is_running(self) -> bool:
        """检查桌面插件是否正在运行 - 优化版本"""
        try:
            # 首先检查守护进程PID文件（最快的方法）
            if self.daemon_pid_file.exists():
                try:
                    with open(self.daemon_pid_file, 'r') as f:
                        pid_str = f.read().strip()
                        pid = int(pid_str)

                    if psutil.pid_exists(pid):
                        # 快速检查进程是否是我们的守护进程
                        try:
                            process = psutil.Process(pid)
                            cmdline = ' '.join(process.cmdline())
                            if 'desktop_widget_daemon.py' in cmdline:
                                return True
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            pass
                except (ValueError, IOError):
                    # PID文件损坏，删除它
                    try:
                        self.daemon_pid_file.unlink()
                    except:
                        pass

            # 如果守护进程检查失败，快速扫描相关进程（限制扫描范围）
            try:
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        # 只检查Python进程，提高效率
                        if proc.info['name'] and 'python' in proc.info['name'].lower():
                            cmdline = ' '.join(proc.info['cmdline'] or [])
                            if ('start_desktop_widget.py' in cmdline or
                                'desktop_widget.py' in cmdline or
                                'desktop_widget_daemon.py' in cmdline):
                                return True
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
            except Exception:
                pass  # 如果进程扫描失败，不影响整体功能

            return False

        except Exception as e:
            self.logger.error(f"检查运行状态失败: {e}")
            return False
    
    def start_widget(self) -> bool:
        """启动桌面插件"""
        try:
            # 检查是否已在运行
            is_running = self.is_running()

            if is_running:
                self.logger.info("桌面插件已在运行，发送手动接管命令")
                # 发送手动接管命令给已运行的守护进程
                result = self.send_manual_takeover_command()
                return result

            self.logger.info("启动桌面插件守护进程...")

            # 启动守护进程
            # 设置环境变量确保UTF-8编码
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSFSENCODING'] = '0'
            if os.name == 'nt':
                env['PYTHONUTF8'] = '1'

            self.daemon_process = subprocess.Popen(
                [sys.executable, str(self.daemon_script), "--manual"],
                cwd=str(self.project_root),
                env=env,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )

            # 快速检查启动状态 - 减少等待时间
            import time
            for i in range(10):  # 最多等待1秒，每100ms检查一次
                time.sleep(0.1)
                is_running = self.is_running()
                if is_running:
                    self.logger.info("桌面插件启动成功")
                    self._update_status(True)
                    return True

            # 如果1秒内没有启动成功，再等待一下
            time.sleep(0.5)
            is_running_final = self.is_running()
            if is_running_final:
                self.logger.info("桌面插件启动成功")
                self._update_status(True)
                return True
            else:
                self.logger.error("桌面插件启动失败")
                self.error_occurred.emit("桌面插件启动失败")
                return False

        except Exception as e:
            self.logger.error(f"启动桌面插件失败: {e}")
            self.error_occurred.emit(f"启动失败: {str(e)}")
            return False
    
    def stop_widget(self) -> bool:
        """停止桌面插件"""
        try:
            is_running = self.is_running()
            if not is_running:
                self.logger.info("桌面插件未在运行")
                return True

            self.logger.info("停止桌面插件...")
            
            # 停止守护进程
            if self.daemon_pid_file.exists():
                try:
                    with open(self.daemon_pid_file, 'r') as f:
                        pid = int(f.read().strip())
                    
                    if psutil.pid_exists(pid):
                        process = psutil.Process(pid)
                        process.terminate()
                        
                        # 快速等待进程结束
                        try:
                            process.wait(timeout=3)  # 减少到3秒
                        except psutil.TimeoutExpired:
                            process.kill()
                            
                except Exception as e:
                    self.logger.warning(f"停止守护进程失败: {e}")
            
            # 停止所有相关进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if ('start_desktop_widget.py' in cmdline or 
                        'desktop_widget.py' in cmdline or
                        'desktop_widget_daemon.py' in cmdline):
                        proc.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 快速检查停止状态 - 减少等待时间
            import time
            for i in range(5):  # 最多等待0.5秒，每100ms检查一次
                time.sleep(0.1)
                if not self.is_running():
                    self.logger.info("桌面插件已停止")
                    self._update_status(False)
                    return True

            # 如果还没停止，强制认为已停止（避免长时间等待）
            self.logger.info("桌面插件停止完成")
            self.daemon_process = None  # 清理进程对象
            self._update_status(False)
            return True
                
        except Exception as e:
            self.logger.error(f"停止桌面插件失败: {e}")
            self.error_occurred.emit(f"停止失败: {str(e)}")
            return False
    
    def is_auto_start_enabled(self) -> bool:
        """检查是否启用了自启动"""
        try:
            # 检查注册表
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                              r"Software\Microsoft\Windows\CurrentVersion\Run") as key:
                try:
                    winreg.QueryValueEx(key, "桌面任务小部件")
                    return True
                except FileNotFoundError:
                    pass
            
            # 检查启动文件夹
            startup_folder = Path(os.environ['APPDATA']) / "Microsoft" / "Windows" / "Start Menu" / "Programs" / "Startup"
            startup_file = startup_folder / "桌面任务小部件.bat"
            if startup_file.exists():
                return True
                
            return False
            
        except Exception as e:
            self.logger.error(f"检查自启动状态失败: {e}")
            return False
    
    def set_auto_start(self, enabled: bool) -> bool:
        """设置自启动"""
        try:
            if enabled:
                return self._enable_auto_start()
            else:
                return self._disable_auto_start()
        except Exception as e:
            self.logger.error(f"设置自启动失败: {e}")
            self.error_occurred.emit(f"设置自启动失败: {str(e)}")
            return False
    
    def _enable_auto_start(self) -> bool:
        """启用自启动"""
        try:
            # 方法1：注册表
            daemon_path = str(self.daemon_script)
            command = f'"{sys.executable}" "{daemon_path}"'
            
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                              r"Software\Microsoft\Windows\CurrentVersion\Run", 
                              0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "桌面任务小部件", 0, winreg.REG_SZ, command)
            
            self.logger.info("自启动已启用（注册表方式）")
            return True
            
        except Exception as e:
            self.logger.error(f"启用自启动失败: {e}")
            return False
    
    def _disable_auto_start(self) -> bool:
        """禁用自启动"""
        success = True
        
        try:
            # 删除注册表项
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                              r"Software\Microsoft\Windows\CurrentVersion\Run", 
                              0, winreg.KEY_SET_VALUE) as key:
                try:
                    winreg.DeleteValue(key, "桌面任务小部件")
                    self.logger.info("已删除注册表自启动项")
                except FileNotFoundError:
                    pass
        except Exception as e:
            self.logger.warning(f"删除注册表自启动项失败: {e}")
            success = False
        
        try:
            # 删除启动文件夹中的文件
            startup_folder = Path(os.environ['APPDATA']) / "Microsoft" / "Windows" / "Start Menu" / "Programs" / "Startup"
            startup_file = startup_folder / "桌面任务小部件.bat"
            if startup_file.exists():
                startup_file.unlink()
                self.logger.info("已删除启动文件夹中的自启动文件")
        except Exception as e:
            self.logger.warning(f"删除启动文件夹自启动文件失败: {e}")
            success = False
        
        return success

    def _force_terminate_all_processes(self):
        """强制终止所有相关进程，确保彻底清理"""
        # 1. 终止守护进程
        if self.daemon_process and self.daemon_process.poll() is None:
            try:
                self.daemon_process.terminate()
                self.daemon_process.wait(timeout=2)
            except Exception as e:
                try:
                    self.daemon_process.kill()
                except:
                    pass
            finally:
                self.daemon_process = None

        # 2. 通过PID文件终止守护进程
        if self.daemon_pid_file.exists():
            try:
                with open(self.daemon_pid_file, 'r') as f:
                    pid = int(f.read().strip())

                if psutil.pid_exists(pid):
                    process = psutil.Process(pid)
                    process.terminate()
                    try:
                        process.wait(timeout=2)
                    except psutil.TimeoutExpired:
                        process.kill()

                # 删除PID文件
                self.daemon_pid_file.unlink()
            except Exception as e:
                self.logger.warning(f"通过PID文件终止进程失败: {e}")

        # 3. 扫描并终止所有相关进程
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if ('start_desktop_widget.py' in cmdline or
                        'desktop_widget.py' in cmdline or
                        'desktop_widget_daemon.py' in cmdline):
                        proc.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            self.logger.warning(f"扫描终止进程失败: {e}")

    def check_status(self):
        """检查状态并发送信号"""
        # 首先检查小组件是否通过状态文件通知关闭
        status_file_closed = False
        if self.widget_status_file.exists():
            try:
                with open(self.widget_status_file, 'r', encoding='utf-8') as f:
                    status_content = f.read().strip()
                if status_content.startswith('closed:'):
                    self.logger.info("检测到小组件已关闭（通过状态文件）")
                    status_file_closed = True

                    # 强制终止所有相关进程，确保彻底清理
                    self._force_terminate_all_processes()

                    # 删除状态文件
                    try:
                        self.widget_status_file.unlink()
                    except Exception as delete_error:
                        self.logger.warning(f"删除状态文件失败: {delete_error}")
            except Exception as e:
                self.logger.warning(f"读取小组件状态文件失败: {e}")

        # 根据状态文件通知决定当前状态
        if status_file_closed:
            # 如果状态文件通知关闭，直接设置为 False，不再调用 is_running()
            current_status = False
        else:
            # 否则正常检查进程状态
            current_status = self.is_running()

        if current_status != self._is_running:
            self._update_status(current_status)
    
    def send_manual_takeover_command(self) -> bool:
        """发送手动接管命令给已运行的守护进程"""
        try:
            command_file = self.project_root / "daemon_command.txt"

            with open(command_file, 'w', encoding='utf-8') as f:
                f.write("manual_takeover")

            self.logger.info("已发送手动接管命令给守护进程")
            return True

        except Exception as e:
            self.logger.error(f"发送手动接管命令失败: {e}")
            return False

    def send_show_command(self) -> bool:
        """发送显示命令给已运行的守护进程"""
        try:
            command_file = self.project_root / "daemon_command.txt"
            with open(command_file, 'w', encoding='utf-8') as f:
                f.write("show_widget")

            self.logger.info("已发送显示命令给守护进程")
            return True

        except Exception as e:
            self.logger.error(f"发送显示命令失败: {e}")
            return False

    def _update_status(self, is_running: bool):
        """更新状态并发送信号"""
        self._is_running = is_running
        self.status_changed.emit(is_running)
    
    def get_widget_enabled_setting(self) -> bool:
        """获取桌面插件开关设置"""
        return self.config_manager.get('desktop_widget.enabled', False)
    
    def set_widget_enabled_setting(self, enabled: bool):
        """保存桌面插件开关设置"""
        self.config_manager.set('desktop_widget.enabled', enabled)
        self.config_manager.save()
