@echo off
chcp 65001 >nul
title 桌面任务小部件

echo ========================================
echo 启动桌面任务小部件
echo ========================================
echo.

cd /d "%~dp0"

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请确保已安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo 检查依赖包...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到PyQt6包
    echo 正在尝试安装...
    pip install PyQt6
    if errorlevel 1 (
        echo 安装失败，请手动安装: pip install PyQt6
        pause
        exit /b 1
    )
)

echo 启动桌面小部件...
python start_desktop_widget.py

if errorlevel 1 (
    echo.
    echo 桌面小部件启动失败
    pause
) else (
    echo.
    echo 桌面小部件已退出
)
