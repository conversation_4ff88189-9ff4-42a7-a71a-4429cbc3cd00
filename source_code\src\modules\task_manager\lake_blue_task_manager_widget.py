"""
Lake Blue Theme Task Manager Widget
湖面蓝主题任务管理界面 - 集成版本

基于现有的WeeklyTaskManagerWidget，应用湖面蓝主题设计
"""

from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QSplitter, QScrollArea, QGroupBox, QGridLayout, QSpacerItem,
    QSizePolicy, QApplication, QTextEdit, QComboBox, QLineEdit
)
from PyQt6.QtCore import Qt, pyqtSignal, QDate, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QIcon, QPainter, QPen, QBrush, QColor, QLinearGradient

from core.logger import LoggerMixin
from models.task_models import Task, TaskStatus, TaskPriority, TaskCategory
from .task_service import TaskService
from .weekly_task_table import WeeklyTaskTable
from .date_week_display import DateWeekDisplay
from .desktop_widget_controller import DesktopWidgetController
from .lake_blue_schedule_widget import LakeBlueTheme, StatusBar, WeekNavigationBar, MainSchedulePanel, SummaryPanel


class LakeBlueTaskManagerWidget(QWidget, LoggerMixin):
    """湖面蓝主题任务管理主界面 - 集成现有功能"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.task_service = TaskService()
        self.current_tasks = []
        self.categories = []
        self.current_date = date.today()

        # 初始化桌面小组件控制器
        self.desktop_widget_controller = DesktopWidgetController(self)
        self.desktop_widget_controller.status_changed.connect(self.on_widget_status_changed)
        self.desktop_widget_controller.error_occurred.connect(self.on_widget_error)

        self.init_ui()
        self.setup_style()
        self.connect_signals()
        self.load_data()
        
        # 设置定时器定期刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # 每分钟刷新一次
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建顶部控制栏
        self.create_top_control_bar()
        layout.addWidget(self.top_control_bar)
        
        # 创建主内容区域
        self.create_main_content_area()
        layout.addWidget(self.main_content_splitter)
        
        # 设置窗口属性
        self.setWindowTitle("任务管理 - 湖面蓝主题")
        self.setMinimumSize(1200, 800)
    
    def create_top_control_bar(self):
        """创建顶部控制栏"""
        self.top_control_bar = QFrame()
        self.top_control_bar.setMaximumHeight(80)
        self.top_control_bar.setStyleSheet(f"""
            QFrame {{
                {LakeBlueTheme.get_gradient_style(LakeBlueTheme.SEAWEED_GREEN, LakeBlueTheme.MEDIUM_BLUE)}
                border-radius: 8px;
                margin: 2px;
            }}
        """)
        
        layout = QHBoxLayout(self.top_control_bar)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)
        
        # 左侧：标题和日期信息
        left_layout = QVBoxLayout()
        
        title_label = QLabel("🌊 湖面蓝主题任务管理")
        title_label.setFont(QFont("Inter", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white;")
        left_layout.addWidget(title_label)
        
        # 日期信息
        self.date_info_label = QLabel()
        self.date_info_label.setFont(QFont("", 10))
        self.date_info_label.setStyleSheet("color: #E0F8FF;")
        self.update_date_info()
        left_layout.addWidget(self.date_info_label)
        
        layout.addLayout(left_layout)
        layout.addStretch()
        
        # 右侧：控制按钮
        self.create_control_buttons(layout)
    
    def create_control_buttons(self, parent_layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 视图切换按钮
        self.view_toggle_btn = QPushButton("📊 切换到表格视图")
        self.view_toggle_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {LakeBlueTheme.LIGHT_BLUE};
                color: {LakeBlueTheme.DEEP_BLUE};
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: white;
            }}
        """)
        self.view_toggle_btn.clicked.connect(self.toggle_view_mode)
        button_layout.addWidget(self.view_toggle_btn)
        
        # 桌面小组件开关
        self.widget_toggle_btn = QPushButton("🖥️ 桌面小组件")
        self.widget_toggle_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {LakeBlueTheme.MINT_GREEN};
                color: {LakeBlueTheme.DEEP_BLUE};
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: white;
            }}
        """)
        self.widget_toggle_btn.clicked.connect(self.toggle_desktop_widget)
        button_layout.addWidget(self.widget_toggle_btn)
        
        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {LakeBlueTheme.SEAWEED_GREEN};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {LakeBlueTheme.MEDIUM_BLUE};
            }}
        """)
        refresh_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(refresh_btn)
        
        parent_layout.addLayout(button_layout)
    
    def create_main_content_area(self):
        """创建主内容区域"""
        self.main_content_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 创建湖面蓝主题日程面板（60%）
        self.schedule_panel = MainSchedulePanel()
        self.main_content_splitter.addWidget(self.schedule_panel)
        
        # 创建右侧面板容器（40%）
        self.create_right_panel()
        self.main_content_splitter.addWidget(self.right_panel_container)
        
        # 设置分割比例
        self.main_content_splitter.setStretchFactor(0, 6)  # 主面板60%
        self.main_content_splitter.setStretchFactor(1, 4)  # 右侧面板40%
        
        # 默认显示湖面蓝主题视图
        self.current_view_mode = "schedule"  # "schedule" 或 "table"
    
    def create_right_panel(self):
        """创建右侧面板"""
        self.right_panel_container = QFrame()
        layout = QVBoxLayout(self.right_panel_container)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 湖面蓝主题统计面板
        self.summary_panel = SummaryPanel()
        layout.addWidget(self.summary_panel)
        
        # 传统表格视图（初始隐藏）
        self.create_table_view()
        layout.addWidget(self.table_view_frame)
        self.table_view_frame.hide()
    
    def create_table_view(self):
        """创建传统表格视图"""
        self.table_view_frame = QFrame()
        self.table_view_frame.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-radius: 8px;
                border: 1px solid {LakeBlueTheme.LIGHT_BLUE};
            }}
        """)
        
        layout = QVBoxLayout(self.table_view_frame)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 表格标题
        table_title = QLabel("📋 传统表格视图")
        table_title.setFont(QFont("Inter", 12, QFont.Weight.Bold))
        table_title.setStyleSheet(f"color: {LakeBlueTheme.DEEP_BLUE};")
        layout.addWidget(table_title)
        
        # 周任务表格
        self.weekly_table = WeeklyTaskTable()
        layout.addWidget(self.weekly_table)
    
    def setup_style(self):
        """设置整体样式"""
        self.setStyleSheet(f"""
            LakeBlueTaskManagerWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 {LakeBlueTheme.WAVE_WHITE}, 
                    stop:0.5 #F0F8FF, 
                    stop:1 {LakeBlueTheme.LIGHT_BLUE});
            }}
        """)
    
    def connect_signals(self):
        """连接信号和槽"""
        # 日程面板信号
        if hasattr(self.schedule_panel, 'week_nav'):
            self.schedule_panel.week_nav.date_selected.connect(self.on_date_selected)
        
        # 表格信号
        if hasattr(self, 'weekly_table'):
            try:
                self.weekly_table.task_updated.connect(self.on_task_updated)
                self.weekly_table.task_deleted.connect(self.on_task_deleted)
                self.weekly_table.new_task_requested.connect(self.on_new_task_requested)
                self.weekly_table.week_changed.connect(self.on_week_changed)
            except AttributeError as e:
                self.logger.warning(f"Some table signals not available: {e}")
    
    def load_data(self):
        """加载数据"""
        try:
            # 加载分类
            self.categories = self.task_service.get_categories()
            
            # 加载任务
            all_tasks = self.task_service.get_tasks(limit=1000)
            self.current_tasks = all_tasks
            
            # 更新日程面板
            self.schedule_panel.load_tasks_for_date(self.current_date)
            
            # 更新表格（如果存在）
            if hasattr(self, 'weekly_table'):
                try:
                    # 使用现有的表格数据加载方法
                    self.weekly_table.refresh_data()
                except AttributeError:
                    # 如果没有refresh_data方法，尝试其他方式
                    self.logger.debug("Table refresh method not available, using alternative approach")
            
            self.logger.info("Lake blue task manager data loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load task manager data: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        self.load_data()
        print("🔄 数据已刷新")
    
    def update_date_info(self):
        """更新日期信息"""
        today = date.today()
        weekday_names = ["一", "二", "三", "四", "五", "六", "日"]
        weekday = weekday_names[today.weekday()]
        
        # 计算周数和剩余天数
        year_start = date(today.year, 1, 1)
        week_num = ((today - year_start).days // 7) + 1
        year_end = date(today.year, 12, 31)
        remaining_days = (year_end - today).days
        
        date_text = f"{today.year}年{today.month:02d}月{today.day:02d}日 星期{weekday} 第{week_num}周 剩余{remaining_days}天"
        self.date_info_label.setText(date_text)
    
    def toggle_view_mode(self):
        """切换视图模式"""
        if self.current_view_mode == "schedule":
            # 切换到表格视图
            self.schedule_panel.hide()
            self.summary_panel.hide()
            self.table_view_frame.show()
            self.view_toggle_btn.setText("🌊 切换到日程视图")
            self.current_view_mode = "table"
            print("📊 已切换到表格视图")
        else:
            # 切换到日程视图
            self.table_view_frame.hide()
            self.schedule_panel.show()
            self.summary_panel.show()
            self.view_toggle_btn.setText("📊 切换到表格视图")
            self.current_view_mode = "schedule"
            print("🌊 已切换到日程视图")
    
    def toggle_desktop_widget(self):
        """切换桌面小组件"""
        try:
            if self.desktop_widget_controller.is_running():
                self.desktop_widget_controller.stop_widget()
                self.widget_toggle_btn.setText("🖥️ 启动桌面小组件")
                print("🖥️ 桌面小组件已停止")
            else:
                self.desktop_widget_controller.start_widget()
                self.widget_toggle_btn.setText("🖥️ 停止桌面小组件")
                print("🖥️ 桌面小组件已启动")
        except Exception as e:
            self.logger.error(f"Failed to toggle desktop widget: {e}")
    
    # 事件处理方法
    def on_date_selected(self, selected_date: date):
        """日期选择事件"""
        self.current_date = selected_date
        self.schedule_panel.load_tasks_for_date(selected_date)
        print(f"📅 已选择日期: {selected_date}")
    
    def on_task_updated(self, task_data: dict):
        """任务更新事件"""
        self.refresh_data()
        print(f"✅ 任务已更新: {task_data.get('title', '未知任务')}")
    
    def on_task_deleted(self, task_id: str):
        """任务删除事件"""
        self.refresh_data()
        print(f"🗑️ 任务已删除: {task_id}")
    
    def on_new_task_requested(self, cell_data: dict):
        """新任务请求事件"""
        # 打开任务创建对话框
        time_period = cell_data.get('time_period', None)
        self.show_add_task_dialog(time_period)
        print(f"➕ 请求创建新任务: {cell_data}")
    
    def on_week_changed(self, week_start: date):
        """周变更事件"""
        self.current_date = week_start
        self.schedule_panel.load_tasks_for_date(week_start)
        print(f"📅 周已变更: {week_start}")
    
    def on_widget_status_changed(self, is_running: bool):
        """桌面小组件状态变更"""
        if is_running:
            self.widget_toggle_btn.setText("🖥️ 停止桌面小组件")
        else:
            self.widget_toggle_btn.setText("🖥️ 启动桌面小组件")
    
    def on_widget_error(self, error_message: str):
        """桌面小组件错误"""
        self.logger.error(f"Desktop widget error: {error_message}")
        print(f"❌ 桌面小组件错误: {error_message}")

    def show_add_task_dialog(self, time_period: str = None):
        """显示添加任务对话框"""
        try:
            # 尝试使用现有的任务对话框
            from .weekly_task_manager_widget import TaskDialog

            dialog = TaskDialog(categories=self.categories, parent=self)

            # 如果指定了时间段，设置默认标签
            if time_period:
                time_period_map = {
                    "morning": "上午",
                    "afternoon": "下午",
                    "evening": "晚上",
                    "weekly": "周计划"
                }
                default_tag = time_period_map.get(time_period, "")
                if hasattr(dialog, 'tags_edit'):
                    dialog.tags_edit.setText(default_tag)

            if dialog.exec() == dialog.DialogCode.Accepted:
                task_data = dialog.get_task_data()
                self.create_task(task_data)

        except ImportError:
            # 如果对话框不可用，显示简单消息
            print(f"📝 请求添加任务到时间段: {time_period}")
            self.logger.info(f"Add task requested for time period: {time_period}")

    def create_task(self, task_data: dict):
        """创建新任务"""
        try:
            # 使用现有的任务服务创建任务
            task = Task(
                title=task_data.get('title', ''),
                description=task_data.get('description', ''),
                status=TaskStatus.PENDING,
                priority=TaskPriority.NORMAL,
                due_date=task_data.get('due_date'),
                tags=task_data.get('tags', [])
            )

            created_task = self.task_service.create_task(task)

            # 刷新界面
            self.load_data()

            self.logger.info(f"Task created: {created_task.title}")
            print(f"✅ 任务已创建: {created_task.title}")

        except Exception as e:
            self.logger.error(f"Failed to create task: {e}")
            print(f"❌ 创建任务失败: {e}")
