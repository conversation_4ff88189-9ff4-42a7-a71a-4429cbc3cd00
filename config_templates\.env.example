# Personal Manager System - Environment Variables Example
# Copy this file to .env and modify the values as needed

# Application Settings
PM_APP_DEBUG=false
PM_APP_LOG_LEVEL=INFO

# Database Settings
PM_DATABASE_URL=sqlite:///personal_manager.db
PM_DATABASE_ECHO=false

# Security Settings
PM_SECURITY_MASTER_KEY=your_master_key_here
PM_SECURITY_ENCRYPTION_KEY=your_encryption_key_here

# Network Settings
PM_NETWORK_PROXY_HOST=
PM_NETWORK_PROXY_PORT=
PM_NETWORK_PROXY_USERNAME=
PM_NETWORK_PROXY_PASSWORD=

# Cloud Sync Settings (Optional)
PM_SYNC_PROVIDER=none
PM_SYNC_API_KEY=
PM_SYNC_SECRET_KEY=

# Notification Settings
PM_NOTIFICATIONS_EMAIL_SMTP_HOST=
PM_NOTIFICATIONS_EMAIL_SMTP_PORT=587
PM_NOTIFICATIONS_EMAIL_USERNAME=
PM_NOTIFICATIONS_EMAIL_PASSWORD=

# Backup Settings
PM_BACKUP_ENCRYPTION_PASSWORD=your_backup_password_here
PM_BACKUP_CLOUD_PROVIDER=none
PM_BACKUP_CLOUD_API_KEY=

# Development Settings
PM_DEV_MODE=false
PM_DEV_MOCK_DATA=false
