"""
自动化测试悬浮小组件关闭状态同步功能
"""

import sys
from pathlib import Path
import time

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 初始化核心组件
from core.database import init_db_manager
from core.config import Config

# 导入学习追踪悬浮小组件和控制器
from modules.study_tracker.study_floating_widget import StudyFloatingWidget
from modules.study_tracker.study_floating_widget_controller import StudyFloatingWidgetController


def test_close_sync_auto():
    """自动化测试关闭状态同步"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_manager = init_db_manager(config)
        print("数据库初始化成功")
        
        print("\n🧪 自动化测试悬浮小组件关闭状态同步")
        print("=" * 50)
        
        # 1. 创建控制器
        print("1. 创建控制器（模拟桌面应用端）...")
        controller = StudyFloatingWidgetController()
        
        # 记录状态变化
        status_changes = []
        
        def on_status_changed(is_running):
            status_text = "运行中" if is_running else "已停止"
            status_changes.append((time.time(), status_text))
            print(f"   🔄 控制器状态变化: {status_text}")
        
        controller.status_changed.connect(on_status_changed)
        
        # 等待初始状态检查
        time.sleep(3)
        initial_status = controller.is_running()
        print(f"   初始状态: {'运行中' if initial_status else '已停止'}")
        
        # 2. 创建悬浮小组件
        print("\n2. 创建悬浮小组件...")
        widget = StudyFloatingWidget()
        widget.show()
        widget.raise_()
        widget.activateWindow()
        print("   ✅ 悬浮小组件已创建并显示")
        
        # 等待控制器检测到小组件
        print("   等待控制器检测到小组件...")
        for i in range(10):
            time.sleep(1)
            current_status = controller.is_running()
            print(f"   第{i+1}秒: 控制器状态 = {'运行中' if current_status else '已停止'}")
            if current_status:
                print("   ✅ 控制器已检测到悬浮小组件运行")
                break
        else:
            print("   ❌ 控制器未能检测到悬浮小组件")
            return
        
        # 3. 关闭悬浮小组件
        print("\n3. 关闭悬浮小组件...")
        widget.close()  # 这会触发closeEvent和状态文件创建
        print("   ✅ 悬浮小组件已关闭")
        
        # 4. 手动触发状态检查并等待控制器检测到关闭状态
        print("   等待控制器检测到关闭状态...")
        sync_success = False
        for i in range(10):
            time.sleep(1)
            # 手动触发状态检查
            controller.check_status()
            current_status = controller.is_running()
            print(f"   第{i+1}秒: 控制器状态 = {'运行中' if current_status else '已停止'}")
            if not current_status:
                print("   ✅ 控制器已检测到悬浮小组件关闭")
                sync_success = True
                break
        
        # 5. 检查状态文件
        print("\n4. 检查状态文件...")
        status_file = Path(__file__).parent.parent / "study_floating_status.txt"
        if status_file.exists():
            with open(status_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            print(f"   状态文件内容: {content}")
            if content.startswith('closed:'):
                print("   ✅ 状态文件正确创建")
            else:
                print("   ❌ 状态文件内容异常")
        else:
            print("   ❌ 状态文件未创建")
        
        # 6. 测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果:")
        
        if sync_success:
            print("✅ 关闭状态同步测试通过")
            print("   - 悬浮小组件关闭时正确创建状态文件")
            print("   - 控制器自动检测到关闭状态")
            print("   - 桌面应用端状态自动更新为'已停止'")
        else:
            print("❌ 关闭状态同步测试失败")
            print("   - 控制器未能自动检测到关闭状态")
            print("   - 需要手动刷新才能更新状态")
        
        print(f"\n状态变化记录: {len(status_changes)} 次")
        for timestamp, status in status_changes:
            print(f"   {time.strftime('%H:%M:%S', time.localtime(timestamp))}: {status}")
        
        app.quit()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        app.quit()


if __name__ == "__main__":
    test_close_sync_auto()
