"""
Database Management System
"""

import logging
from pathlib import Path
from typing import Optional, Any
from contextlib import contextmanager

from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from .logger import LoggerMixin


# Base class for all models
Base = declarative_base()


class DatabaseManager(LoggerMixin):
    """Database connection and session manager"""

    def __init__(self, config=None, database_url: str = None, echo: bool = False):
        """Initialize database manager

        Args:
            config: Configuration manager instance
            database_url: Database connection URL (overrides config)
            echo: Whether to echo SQL statements
        """
        if database_url:
            self.database_url = database_url
        elif config:
            self.database_url = config.get('database.url')
            echo = config.get('database.echo', False)
        else:
            raise ValueError("Either config or database_url must be provided")

        self.echo = echo
        self.engine = None
        self.SessionLocal = None
        
    def initialize(self):
        """Initialize database connection and create tables"""
        try:
            # Create engine
            if self.database_url.startswith('sqlite'):
                # SQLite specific configuration
                self.engine = create_engine(
                    self.database_url,
                    echo=self.echo,
                    poolclass=StaticPool,
                    connect_args={
                        'check_same_thread': False,
                        'timeout': 20
                    }
                )
                
                # Enable foreign key constraints for SQLite
                @event.listens_for(self.engine, "connect")
                def set_sqlite_pragma(dbapi_connection, connection_record):
                    cursor = dbapi_connection.cursor()
                    cursor.execute("PRAGMA foreign_keys=ON")
                    cursor.execute("PRAGMA journal_mode=WAL")
                    cursor.execute("PRAGMA synchronous=NORMAL")
                    cursor.execute("PRAGMA cache_size=1000")
                    cursor.execute("PRAGMA temp_store=MEMORY")
                    cursor.close()
            else:
                self.engine = create_engine(
                    self.database_url,
                    echo=self.echo
                )
            
            # Create session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # Create database directory if it doesn't exist
            if self.database_url.startswith('sqlite'):
                db_path = self.database_url.replace('sqlite:///', '')
                Path(db_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Create all tables
            self.create_tables()
            
            self.logger.info("Database initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            raise
    
    def create_tables(self):
        """Create all database tables"""
        try:
            Base.metadata.create_all(bind=self.engine)
            self.logger.info("Database tables created successfully")
        except Exception as e:
            self.logger.error(f"Failed to create tables: {e}")
            raise
    
    def drop_tables(self):
        """Drop all database tables"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            self.logger.info("Database tables dropped successfully")
        except Exception as e:
            self.logger.error(f"Failed to drop tables: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """Get database session with automatic cleanup
        
        Yields:
            Database session
        """
        if not self.SessionLocal:
            raise RuntimeError("Database not initialized")
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def get_session_sync(self) -> Session:
        """Get database session (synchronous)
        
        Returns:
            Database session (must be closed manually)
        """
        if not self.SessionLocal:
            raise RuntimeError("Database not initialized")
        
        return self.SessionLocal()
    
    def execute_raw_sql(self, sql: str, params: Optional[dict] = None) -> Any:
        """Execute raw SQL query
        
        Args:
            sql: SQL query string
            params: Query parameters
            
        Returns:
            Query result
        """
        with self.get_session() as session:
            result = session.execute(sql, params or {})
            return result.fetchall()
    
    def backup_database(self, backup_path: str):
        """Create database backup
        
        Args:
            backup_path: Path to backup file
        """
        if not self.database_url.startswith('sqlite'):
            raise NotImplementedError("Backup only supported for SQLite databases")
        
        try:
            import shutil
            
            # Get source database path
            source_path = self.database_url.replace('sqlite:///', '')
            
            # Create backup directory
            Path(backup_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Copy database file
            shutil.copy2(source_path, backup_path)
            
            self.logger.info(f"Database backed up to {backup_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to backup database: {e}")
            raise
    
    def restore_database(self, backup_path: str):
        """Restore database from backup
        
        Args:
            backup_path: Path to backup file
        """
        if not self.database_url.startswith('sqlite'):
            raise NotImplementedError("Restore only supported for SQLite databases")
        
        try:
            import shutil
            
            # Get target database path
            target_path = self.database_url.replace('sqlite:///', '')
            
            # Close existing connections
            if self.engine:
                self.engine.dispose()
            
            # Restore database file
            shutil.copy2(backup_path, target_path)
            
            # Reinitialize connection
            self.initialize()
            
            self.logger.info(f"Database restored from {backup_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to restore database: {e}")
            raise
    
    def get_database_info(self) -> dict:
        """Get database information
        
        Returns:
            Dictionary with database information
        """
        info = {
            'url': self.database_url,
            'engine': str(self.engine) if self.engine else None,
            'tables': []
        }
        
        if self.engine:
            try:
                # Get table names
                info['tables'] = list(Base.metadata.tables.keys())
                
                # Get database size (SQLite only)
                if self.database_url.startswith('sqlite'):
                    db_path = self.database_url.replace('sqlite:///', '')
                    if Path(db_path).exists():
                        info['size_bytes'] = Path(db_path).stat().st_size
                        info['size_mb'] = round(info['size_bytes'] / (1024 * 1024), 2)
                
            except Exception as e:
                self.logger.error(f"Failed to get database info: {e}")
        
        return info
    
    def close(self):
        """Close database connection"""
        if self.engine:
            self.engine.dispose()
            self.logger.info("Database connection closed")


# Global database manager instance
db_manager: Optional[DatabaseManager] = None


def get_db_manager() -> DatabaseManager:
    """Get global database manager instance
    
    Returns:
        Database manager instance
    """
    global db_manager
    if db_manager is None:
        raise RuntimeError("Database manager not initialized")
    return db_manager


def init_db_manager(config=None, database_url: str = None, echo: bool = False) -> DatabaseManager:
    """Initialize global database manager

    Args:
        config: Configuration manager instance
        database_url: Database connection URL
        echo: Whether to echo SQL statements

    Returns:
        Database manager instance
    """
    global db_manager
    db_manager = DatabaseManager(config=config, database_url=database_url, echo=echo)
    db_manager.initialize()
    return db_manager


@contextmanager
def get_session():
    """Get database session using global database manager

    Yields:
        Database session
    """
    db = get_db_manager()
    with db.get_session() as session:
        yield session


def close_all_sessions():
    """Close all database sessions"""
    global db_manager
    if db_manager:
        db_manager.close()
