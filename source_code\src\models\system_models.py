"""
系统监控相关数据模型

包含系统性能监控、进程监控、硬件信息等模型
"""

from sqlalchemy import Column, String, Text, Integer, Float, Boolean, DateTime, Index, JSON
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from .base import BaseModel


class SystemMetrics(BaseModel):
    """系统性能指标模型"""
    
    __tablename__ = 'system_metrics'
    
    # CPU指标
    cpu_percent = Column(Float, nullable=True)
    cpu_count_physical = Column(Integer, nullable=True)
    cpu_count_logical = Column(Integer, nullable=True)
    cpu_freq_current = Column(Float, nullable=True)
    
    # 内存指标
    memory_total = Column(Integer, nullable=True)      # 字节
    memory_used = Column(Integer, nullable=True)       # 字节
    memory_available = Column(Integer, nullable=True)  # 字节
    memory_percent = Column(Float, nullable=True)
    
    # 磁盘指标
    disk_total = Column(Integer, nullable=True)        # 字节
    disk_used = Column(Integer, nullable=True)         # 字节
    disk_free = Column(Integer, nullable=True)         # 字节
    disk_percent = Column(Float, nullable=True)
    
    # 网络指标
    network_bytes_sent = Column(Integer, nullable=True)
    network_bytes_recv = Column(Integer, nullable=True)
    network_packets_sent = Column(Integer, nullable=True)
    network_packets_recv = Column(Integer, nullable=True)
    
    # 系统负载
    load_average_1m = Column(Float, nullable=True)
    load_average_5m = Column(Float, nullable=True)
    load_average_15m = Column(Float, nullable=True)
    
    # 温度信息（JSON格式存储）
    temperatures = Column(JSON, nullable=True)
    
    # 索引
    __table_args__ = (
        Index('idx_system_metrics_created', 'created_at'),
        Index('idx_system_metrics_cpu', 'cpu_percent'),
        Index('idx_system_metrics_memory', 'memory_percent'),
    )
    
    @classmethod
    def record_metrics(cls, session: Session, metrics_data: Dict[str, Any]) -> 'SystemMetrics':
        """记录系统指标"""
        metrics = cls(**metrics_data)
        session.add(metrics)
        session.flush()
        return metrics
    
    @classmethod
    def get_recent_metrics(cls, session: Session, hours: int = 24, limit: int = 100) -> List['SystemMetrics']:
        """获取最近的系统指标"""
        since = datetime.utcnow() - timedelta(hours=hours)
        return session.query(cls).filter(
            cls.created_at >= since,
            cls.is_deleted == False
        ).order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_average_metrics(cls, session: Session, hours: int = 24) -> Optional[Dict[str, float]]:
        """获取平均系统指标"""
        from sqlalchemy import func
        
        since = datetime.utcnow() - timedelta(hours=hours)
        result = session.query(
            func.avg(cls.cpu_percent).label('avg_cpu'),
            func.avg(cls.memory_percent).label('avg_memory'),
            func.avg(cls.disk_percent).label('avg_disk')
        ).filter(
            cls.created_at >= since,
            cls.is_deleted == False
        ).first()
        
        if result:
            return {
                'avg_cpu': float(result.avg_cpu or 0),
                'avg_memory': float(result.avg_memory or 0),
                'avg_disk': float(result.avg_disk or 0)
            }
        return None
    
    @classmethod
    def cleanup_old_metrics(cls, session: Session, days: int = 30) -> int:
        """清理旧的系统指标"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        old_metrics = session.query(cls).filter(
            cls.created_at < cutoff_date,
            cls.is_deleted == False
        ).all()
        
        count = len(old_metrics)
        for metric in old_metrics:
            metric.soft_delete()
            session.add(metric)
        
        session.flush()
        return count


class ProcessMonitor(BaseModel):
    """进程监控模型"""
    
    __tablename__ = 'process_monitors'
    
    pid = Column(Integer, nullable=False)
    name = Column(String(255), nullable=False)
    exe_path = Column(Text, nullable=True)
    cmdline = Column(Text, nullable=True)
    username = Column(String(100), nullable=True)
    status = Column(String(50), nullable=True)
    
    # 资源使用
    cpu_percent = Column(Float, nullable=True)
    memory_percent = Column(Float, nullable=True)
    memory_rss = Column(Integer, nullable=True)      # 物理内存
    memory_vms = Column(Integer, nullable=True)      # 虚拟内存
    num_threads = Column(Integer, nullable=True)
    
    # 时间信息
    create_time = Column(DateTime, nullable=True)
    
    # 监控状态
    is_monitored = Column(Boolean, default=False)    # 是否被监控
    alert_cpu_threshold = Column(Float, nullable=True)     # CPU告警阈值
    alert_memory_threshold = Column(Float, nullable=True)  # 内存告警阈值
    
    # 索引
    __table_args__ = (
        Index('idx_process_monitors_pid', 'pid'),
        Index('idx_process_monitors_name', 'name'),
        Index('idx_process_monitors_monitored', 'is_monitored'),
        Index('idx_process_monitors_created', 'created_at'),
    )
    
    @classmethod
    def record_process(cls, session: Session, process_data: Dict[str, Any]) -> 'ProcessMonitor':
        """记录进程信息"""
        # 检查是否已存在相同PID的记录
        existing = session.query(cls).filter(
            cls.pid == process_data.get('pid'),
            cls.is_deleted == False
        ).first()
        
        if existing:
            # 更新现有记录
            existing.update_from_dict(process_data)
            session.add(existing)
            session.flush()
            return existing
        else:
            # 创建新记录
            process = cls(**process_data)
            session.add(process)
            session.flush()
            return process
    
    @classmethod
    def get_monitored_processes(cls, session: Session) -> List['ProcessMonitor']:
        """获取被监控的进程"""
        return session.query(cls).filter(
            cls.is_monitored == True,
            cls.is_deleted == False
        ).all()
    
    @classmethod
    def get_high_resource_processes(cls, session: Session, 
                                   cpu_threshold: float = 80.0,
                                   memory_threshold: float = 80.0) -> List['ProcessMonitor']:
        """获取高资源使用的进程"""
        return session.query(cls).filter(
            (cls.cpu_percent >= cpu_threshold) | (cls.memory_percent >= memory_threshold),
            cls.is_deleted == False
        ).order_by(cls.cpu_percent.desc()).all()
    
    @classmethod
    def add_to_monitor(cls, session: Session, pid: int, 
                      cpu_threshold: float = None, memory_threshold: float = None) -> bool:
        """添加进程到监控列表"""
        process = session.query(cls).filter(
            cls.pid == pid,
            cls.is_deleted == False
        ).first()
        
        if process:
            process.is_monitored = True
            if cpu_threshold is not None:
                process.alert_cpu_threshold = cpu_threshold
            if memory_threshold is not None:
                process.alert_memory_threshold = memory_threshold
            session.add(process)
            session.flush()
            return True
        
        return False


class HardwareInfo(BaseModel):
    """硬件信息模型"""
    
    __tablename__ = 'hardware_info'
    
    component_type = Column(String(50), nullable=False)  # cpu, memory, disk, network, etc.
    component_name = Column(String(255), nullable=False)
    manufacturer = Column(String(255), nullable=True)
    model = Column(String(255), nullable=True)
    serial_number = Column(String(255), nullable=True)
    
    # 规格信息（JSON格式）
    specifications = Column(JSON, nullable=True)
    
    # 状态信息
    status = Column(String(50), default='active')  # active, inactive, error
    health_status = Column(String(50), nullable=True)  # good, warning, critical
    
    # 索引
    __table_args__ = (
        Index('idx_hardware_info_type', 'component_type'),
        Index('idx_hardware_info_status', 'status'),
    )
    
    @classmethod
    def update_hardware_info(cls, session: Session, component_type: str, 
                           component_name: str, **kwargs) -> 'HardwareInfo':
        """更新硬件信息"""
        hardware = session.query(cls).filter(
            cls.component_type == component_type,
            cls.component_name == component_name,
            cls.is_deleted == False
        ).first()
        
        if hardware:
            hardware.update_from_dict(kwargs)
            session.add(hardware)
        else:
            hardware = cls(
                component_type=component_type,
                component_name=component_name,
                **kwargs
            )
            session.add(hardware)
        
        session.flush()
        return hardware
    
    @classmethod
    def get_by_type(cls, session: Session, component_type: str) -> List['HardwareInfo']:
        """根据组件类型获取硬件信息"""
        return session.query(cls).filter(
            cls.component_type == component_type,
            cls.is_deleted == False
        ).all()


class SystemAlert(BaseModel):
    """系统告警模型"""
    
    __tablename__ = 'system_alerts'
    
    alert_type = Column(String(50), nullable=False)  # cpu, memory, disk, process, etc.
    severity = Column(String(20), nullable=False)    # info, warning, error, critical
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    source = Column(String(100), nullable=True)      # 告警源
    
    # 状态
    is_acknowledged = Column(Boolean, default=False)
    acknowledged_at = Column(DateTime, nullable=True)
    is_resolved = Column(Boolean, default=False)
    resolved_at = Column(DateTime, nullable=True)
    
    # 相关数据（JSON格式）
    alert_metadata = Column(JSON, nullable=True)
    
    # 索引
    __table_args__ = (
        Index('idx_system_alerts_type', 'alert_type'),
        Index('idx_system_alerts_severity', 'severity'),
        Index('idx_system_alerts_status', 'is_acknowledged', 'is_resolved'),
        Index('idx_system_alerts_created', 'created_at'),
    )
    
    @classmethod
    def create_alert(cls, session: Session, alert_type: str, severity: str,
                    title: str, message: str, source: str = None,
                    alert_metadata: Dict[str, Any] = None) -> 'SystemAlert':
        """创建系统告警"""
        alert = cls(
            alert_type=alert_type,
            severity=severity,
            title=title,
            message=message,
            source=source,
            alert_metadata=alert_metadata
        )
        session.add(alert)
        session.flush()
        return alert
    
    @classmethod
    def get_unresolved_alerts(cls, session: Session) -> List['SystemAlert']:
        """获取未解决的告警"""
        return session.query(cls).filter(
            cls.is_resolved == False,
            cls.is_deleted == False
        ).order_by(cls.created_at.desc()).all()
    
    @classmethod
    def get_alerts_by_severity(cls, session: Session, severity: str) -> List['SystemAlert']:
        """根据严重程度获取告警"""
        return session.query(cls).filter(
            cls.severity == severity,
            cls.is_deleted == False
        ).order_by(cls.created_at.desc()).all()
    
    def acknowledge(self):
        """确认告警"""
        self.is_acknowledged = True
        self.acknowledged_at = datetime.utcnow()
    
    def resolve(self):
        """解决告警"""
        self.is_resolved = True
        self.resolved_at = datetime.utcnow()
        if not self.is_acknowledged:
            self.acknowledge()
