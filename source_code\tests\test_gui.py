#!/usr/bin/env python3
"""
Test GUI Application for Personal Manager System

This script tests the GUI functionality without running the full application.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

def test_basic_gui():
    """Test basic GUI functionality"""
    print("Testing basic GUI functionality...")
    
    app = QApplication(sys.argv)
    
    # Create a simple test window
    window = QMainWindow()
    window.setWindowTitle("Personal Manager System - GUI Test")
    window.setGeometry(100, 100, 800, 600)
    
    # Central widget
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    
    # Layout
    layout = QVBoxLayout(central_widget)
    
    # Title label
    title_label = QLabel("Personal Manager System")
    title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    title_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
    layout.addWidget(title_label)
    
    # Status label
    status_label = QLabel("GUI Environment: Working ✓")
    status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    status_label.setFont(QFont("Arial", 14))
    layout.addWidget(status_label)
    
    # Test modules button
    test_button = QPushButton("Test Module Imports")
    test_button.clicked.connect(lambda: test_module_imports(status_label))
    layout.addWidget(test_button)
    
    # Close button
    close_button = QPushButton("Close")
    close_button.clicked.connect(window.close)
    layout.addWidget(close_button)
    
    print("✓ Basic GUI window created successfully")
    
    # Show window
    window.show()
    
    # Don't start event loop for automated testing
    print("✓ GUI test completed successfully")
    return app, window

def test_module_imports(status_label=None):
    """Test module imports"""
    try:
        # Test core modules
        from core.config import ConfigManager
        from core.database import DatabaseManager
        from core.logger import LoggerMixin
        
        # Test service modules
        from modules.file_manager import FileService
        from modules.system_monitor import SystemService
        from modules.task_manager import TaskService
        
        # Test GUI modules
        from modules.file_manager import FileManagerWidget
        from modules.system_monitor import SystemMonitorWidget
        from modules.task_manager import TaskManagerWidget
        
        message = "All modules imported successfully ✓"
        print(f"✓ {message}")
        
        if status_label:
            status_label.setText(message)
        
        return True
        
    except ImportError as e:
        message = f"Module import error: {e}"
        print(f"✗ {message}")
        
        if status_label:
            status_label.setText(message)
        
        return False

def main():
    """Main test function"""
    print("Personal Manager System - GUI Test Suite")
    print("=" * 50)
    
    # Test module imports first
    print("\n1. Testing module imports...")
    if test_module_imports():
        print("✓ Module imports successful")
    else:
        print("✗ Module imports failed")
        return 1
    
    # Test basic GUI
    print("\n2. Testing basic GUI...")
    try:
        app, window = test_basic_gui()
        print("✓ GUI test successful")
        
        # Optional: Show window for manual inspection
        if len(sys.argv) > 1 and sys.argv[1] == "--show":
            print("\nShowing test window... Close it to continue.")
            sys.exit(app.exec())
        
    except Exception as e:
        print(f"✗ GUI test failed: {e}")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 All GUI tests passed!")
    print("\nTo see the test window, run: python test_gui.py --show")
    return 0

if __name__ == "__main__":
    sys.exit(main())
