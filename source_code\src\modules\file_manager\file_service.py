"""
File Service for File Manager Module

This service provides business logic for file operations and management.
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session

from core.database import get_session
from core.logger import LoggerMixin
from models.file_models import FileBookmark, FileTag, FileTagRelation, FileHistory, FileSearch
from utils.file_utils import FileUtils


class FileService(LoggerMixin):
    """Service class for file management operations"""
    
    def __init__(self):
        self.file_utils = FileUtils()
    
    def get_directory_contents(self, path: str) -> Dict[str, Any]:
        """Get contents of a directory
        
        Args:
            path: Directory path
            
        Returns:
            Dictionary containing files and directories
        """
        try:
            path_obj = Path(path)
            if not path_obj.exists() or not path_obj.is_dir():
                return {'files': [], 'directories': [], 'error': 'Invalid directory'}
            
            files = []
            directories = []
            
            for item in path_obj.iterdir():
                try:
                    if item.is_file():
                        file_info = {
                            'name': item.name,
                            'path': str(item),
                            'size': item.stat().st_size,
                            'modified': datetime.fromtimestamp(item.stat().st_mtime),
                            'extension': item.suffix.lower(),
                            'is_hidden': item.name.startswith('.')
                        }
                        files.append(file_info)
                    elif item.is_dir():
                        dir_info = {
                            'name': item.name,
                            'path': str(item),
                            'modified': datetime.fromtimestamp(item.stat().st_mtime),
                            'is_hidden': item.name.startswith('.')
                        }
                        directories.append(dir_info)
                except (PermissionError, OSError) as e:
                    self.logger.warning(f"Cannot access {item}: {e}")
                    continue
            
            # Sort files and directories
            files.sort(key=lambda x: x['name'].lower())
            directories.sort(key=lambda x: x['name'].lower())
            
            return {
                'files': files,
                'directories': directories,
                'total_files': len(files),
                'total_directories': len(directories)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting directory contents for {path}: {e}")
            return {'files': [], 'directories': [], 'error': str(e)}
    
    def copy_file(self, source: str, destination: str) -> bool:
        """Copy a file
        
        Args:
            source: Source file path
            destination: Destination file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            source_path = Path(source)
            dest_path = Path(destination)
            
            if not source_path.exists():
                self.logger.error(f"Source file does not exist: {source}")
                return False
            
            # Create destination directory if it doesn't exist
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file
            shutil.copy2(source, destination)
            
            self.logger.info(f"File copied from {source} to {destination}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error copying file from {source} to {destination}: {e}")
            return False
    
    def move_file(self, source: str, destination: str) -> bool:
        """Move a file
        
        Args:
            source: Source file path
            destination: Destination file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            source_path = Path(source)
            dest_path = Path(destination)
            
            if not source_path.exists():
                self.logger.error(f"Source file does not exist: {source}")
                return False
            
            # Create destination directory if it doesn't exist
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Move file
            shutil.move(source, destination)
            
            self.logger.info(f"File moved from {source} to {destination}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error moving file from {source} to {destination}: {e}")
            return False
    
    def delete_file(self, file_path: str, use_trash: bool = True) -> bool:
        """Delete a file
        
        Args:
            file_path: Path to file to delete
            use_trash: Whether to move to trash or permanently delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            path_obj = Path(file_path)
            
            if not path_obj.exists():
                self.logger.error(f"File does not exist: {file_path}")
                return False
            
            if use_trash:
                # Use send2trash for safe deletion
                from send2trash import send2trash
                send2trash(str(path_obj))
                self.logger.info(f"File moved to trash: {file_path}")
            else:
                # Permanent deletion
                if path_obj.is_file():
                    path_obj.unlink()
                elif path_obj.is_dir():
                    shutil.rmtree(path_obj)
                self.logger.info(f"File permanently deleted: {file_path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting file {file_path}: {e}")
            return False
    
    def rename_file(self, old_path: str, new_name: str) -> bool:
        """Rename a file
        
        Args:
            old_path: Current file path
            new_name: New file name
            
        Returns:
            True if successful, False otherwise
        """
        try:
            old_path_obj = Path(old_path)
            new_path_obj = old_path_obj.parent / new_name
            
            if not old_path_obj.exists():
                self.logger.error(f"File does not exist: {old_path}")
                return False
            
            if new_path_obj.exists():
                self.logger.error(f"File with new name already exists: {new_path_obj}")
                return False
            
            old_path_obj.rename(new_path_obj)
            
            self.logger.info(f"File renamed from {old_path} to {new_path_obj}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error renaming file from {old_path} to {new_name}: {e}")
            return False
    
    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a file
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information or None if error
        """
        try:
            path_obj = Path(file_path)
            
            if not path_obj.exists():
                return None
            
            stat = path_obj.stat()
            
            file_info = {
                'name': path_obj.name,
                'path': str(path_obj),
                'size': stat.st_size,
                'size_formatted': self.file_utils.format_file_size(stat.st_size),
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'accessed': datetime.fromtimestamp(stat.st_atime),
                'extension': path_obj.suffix.lower(),
                'is_file': path_obj.is_file(),
                'is_directory': path_obj.is_dir(),
                'is_hidden': path_obj.name.startswith('.'),
                'permissions': oct(stat.st_mode)[-3:],
            }
            
            # Add file hash for files
            if path_obj.is_file() and stat.st_size < 100 * 1024 * 1024:  # Only for files < 100MB
                file_info['md5'] = self.calculate_file_hash(file_path)
            
            return file_info
            
        except Exception as e:
            self.logger.error(f"Error getting file info for {file_path}: {e}")
            return None
    
    def calculate_file_hash(self, file_path: str, algorithm: str = 'md5') -> Optional[str]:
        """Calculate hash of a file
        
        Args:
            file_path: Path to the file
            algorithm: Hash algorithm (md5, sha1, sha256)
            
        Returns:
            File hash or None if error
        """
        try:
            hash_obj = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            self.logger.error(f"Error calculating {algorithm} hash for {file_path}: {e}")
            return None
    
    def search_files(self, directory: str, pattern: str, include_subdirs: bool = True) -> List[Dict[str, Any]]:
        """Search for files matching a pattern
        
        Args:
            directory: Directory to search in
            pattern: Search pattern (supports wildcards)
            include_subdirs: Whether to search in subdirectories
            
        Returns:
            List of matching files
        """
        try:
            results = []
            path_obj = Path(directory)
            
            if not path_obj.exists() or not path_obj.is_dir():
                return results
            
            # Use glob pattern matching
            if include_subdirs:
                search_pattern = f"**/*{pattern}*"
                matches = path_obj.glob(search_pattern)
            else:
                search_pattern = f"*{pattern}*"
                matches = path_obj.glob(search_pattern)
            
            for match in matches:
                if match.is_file():
                    file_info = self.get_file_info(str(match))
                    if file_info:
                        results.append(file_info)
            
            # Record search in database
            self.record_search(pattern, directory, len(results))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error searching files in {directory} with pattern {pattern}: {e}")
            return []
    
    def record_search(self, query: str, directory: str, result_count: int):
        """Record a search query in the database
        
        Args:
            query: Search query
            directory: Directory searched
            result_count: Number of results found
        """
        try:
            with get_session() as session:
                search_record = FileSearch(
                    query=query,
                    directory=directory,
                    result_count=result_count
                )
                session.add(search_record)
                session.commit()
                
        except Exception as e:
            self.logger.error(f"Error recording search: {e}")

    # Bookmark management methods
    def add_bookmark(self, file_path: str, name: str = None, description: str = None) -> bool:
        """Add a file bookmark

        Args:
            file_path: Path to the file
            name: Custom name for bookmark
            description: Description of the bookmark

        Returns:
            True if successful, False otherwise
        """
        try:
            path_obj = Path(file_path)

            if not path_obj.exists():
                self.logger.error(f"File does not exist: {file_path}")
                return False

            with get_session() as session:
                # Check if bookmark already exists
                existing = FileBookmark.get_by_path(session, file_path)
                if existing:
                    self.logger.warning(f"Bookmark already exists for: {file_path}")
                    return False

                bookmark = FileBookmark(
                    name=name or path_obj.name,
                    path=file_path,
                    description=description,
                    is_folder=path_obj.is_dir()
                )
                session.add(bookmark)
                session.commit()

                self.logger.info(f"Bookmark added for: {file_path}")
                return True

        except Exception as e:
            self.logger.error(f"Error adding bookmark for {file_path}: {e}")
            return False

    def remove_bookmark(self, file_path: str) -> bool:
        """Remove a file bookmark

        Args:
            file_path: Path to the file

        Returns:
            True if successful, False otherwise
        """
        try:
            with get_session() as session:
                bookmark = FileBookmark.get_by_path(session, file_path)
                if not bookmark:
                    self.logger.warning(f"No bookmark found for: {file_path}")
                    return False

                bookmark.soft_delete()
                session.commit()

                self.logger.info(f"Bookmark removed for: {file_path}")
                return True

        except Exception as e:
            self.logger.error(f"Error removing bookmark for {file_path}: {e}")
            return False

    def get_bookmarks(self) -> List[Dict[str, Any]]:
        """Get all file bookmarks

        Returns:
            List of bookmark dictionaries
        """
        try:
            with get_session() as session:
                bookmarks = session.query(FileBookmark).filter(
                    FileBookmark.is_deleted == False
                ).order_by(FileBookmark.sort_order, FileBookmark.name).all()

                result = []
                for bookmark in bookmarks:
                    bookmark_dict = bookmark.to_dict()
                    # Check if file still exists
                    bookmark_dict['exists'] = Path(bookmark.path).exists()
                    result.append(bookmark_dict)

                return result

        except Exception as e:
            self.logger.error(f"Error getting bookmarks: {e}")
            return []

    # Tag management methods
    def add_tag(self, name: str, color: str = None, description: str = None) -> bool:
        """Add a new file tag

        Args:
            name: Tag name
            color: Tag color (hex format)
            description: Tag description

        Returns:
            True if successful, False otherwise
        """
        try:
            with get_session() as session:
                # Check if tag already exists
                existing = FileTag.get_by_name(session, name)
                if existing:
                    self.logger.warning(f"Tag already exists: {name}")
                    return False

                tag = FileTag(
                    name=name,
                    color=color or '#007ACC',
                    description=description
                )
                session.add(tag)
                session.commit()

                self.logger.info(f"Tag added: {name}")
                return True

        except Exception as e:
            self.logger.error(f"Error adding tag {name}: {e}")
            return False

    def tag_file(self, file_path: str, tag_name: str) -> bool:
        """Add a tag to a file

        Args:
            file_path: Path to the file
            tag_name: Name of the tag

        Returns:
            True if successful, False otherwise
        """
        try:
            path_obj = Path(file_path)

            if not path_obj.exists():
                self.logger.error(f"File does not exist: {file_path}")
                return False

            with get_session() as session:
                # Get or create tag
                tag = FileTag.get_by_name(session, tag_name)
                if not tag:
                    tag = FileTag(name=tag_name, color='#007ACC')
                    session.add(tag)
                    session.flush()

                # Check if relation already exists
                existing = session.query(FileTagRelation).filter(
                    FileTagRelation.file_path == file_path,
                    FileTagRelation.tag_id == tag.id,
                    FileTagRelation.is_deleted == False
                ).first()

                if existing:
                    self.logger.warning(f"File {file_path} already has tag {tag_name}")
                    return False

                # Create tag relation
                relation = FileTagRelation(
                    file_path=file_path,
                    tag_id=tag.id
                )
                session.add(relation)
                session.commit()

                self.logger.info(f"Tag {tag_name} added to file: {file_path}")
                return True

        except Exception as e:
            self.logger.error(f"Error tagging file {file_path} with {tag_name}: {e}")
            return False

    def untag_file(self, file_path: str, tag_name: str) -> bool:
        """Remove a tag from a file

        Args:
            file_path: Path to the file
            tag_name: Name of the tag

        Returns:
            True if successful, False otherwise
        """
        try:
            with get_session() as session:
                tag = FileTag.get_by_name(session, tag_name)
                if not tag:
                    self.logger.warning(f"Tag not found: {tag_name}")
                    return False

                relation = session.query(FileTagRelation).filter(
                    FileTagRelation.file_path == file_path,
                    FileTagRelation.tag_id == tag.id,
                    FileTagRelation.is_deleted == False
                ).first()

                if not relation:
                    self.logger.warning(f"File {file_path} does not have tag {tag_name}")
                    return False

                relation.soft_delete()
                session.commit()

                self.logger.info(f"Tag {tag_name} removed from file: {file_path}")
                return True

        except Exception as e:
            self.logger.error(f"Error removing tag {tag_name} from file {file_path}: {e}")
            return False

    def get_file_tags(self, file_path: str) -> List[Dict[str, Any]]:
        """Get all tags for a file

        Args:
            file_path: Path to the file

        Returns:
            List of tag dictionaries
        """
        try:
            with get_session() as session:
                tags = FileTag.get_file_tags(session, file_path)
                return [tag.to_dict() for tag in tags]

        except Exception as e:
            self.logger.error(f"Error getting tags for file {file_path}: {e}")
            return []

    def get_all_tags(self) -> List[Dict[str, Any]]:
        """Get all available tags

        Returns:
            List of tag dictionaries
        """
        try:
            with get_session() as session:
                tags = session.query(FileTag).filter(
                    FileTag.is_deleted == False
                ).order_by(FileTag.name).all()

                return [tag.to_dict() for tag in tags]

        except Exception as e:
            self.logger.error(f"Error getting all tags: {e}")
            return []

    def record_file_access(self, file_path: str):
        """Record file access for history tracking

        Args:
            file_path: Path to the accessed file
        """
        try:
            path_obj = Path(file_path)

            if not path_obj.exists() or not path_obj.is_file():
                return

            with get_session() as session:
                FileHistory.record_access(session, file_path)
                session.commit()

        except Exception as e:
            self.logger.error(f"Error recording file access for {file_path}: {e}")

    def get_recent_files(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recently accessed files

        Args:
            limit: Maximum number of files to return

        Returns:
            List of recent file dictionaries
        """
        try:
            with get_session() as session:
                recent_files = FileHistory.get_recent_files(session, limit)

                result = []
                for file_record in recent_files:
                    file_dict = file_record.to_dict()
                    # Check if file still exists
                    file_dict['exists'] = Path(file_record.file_path).exists()
                    # Add file info if it exists
                    if file_dict['exists']:
                        file_info = self.get_file_info(file_record.file_path)
                        if file_info:
                            file_dict.update(file_info)
                    result.append(file_dict)

                return result

        except Exception as e:
            self.logger.error(f"Error getting recent files: {e}")
            return []
